-- Database triggers for automated actions

-- Trigger to update updated_at column
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON public.users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_trainers_updated_at
    BEFORE UPDATE ON public.trainers
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_trainees_profiles_updated_at
    BEFORE UPDATE ON public.trainees_profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_trainer_assignments_updated_at
    BEFORE UPDATE ON public.trainer_assignments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subscriptions_updated_at
    BEFORE UPDATE ON public.subscriptions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sessions_updated_at
    BEFORE UPDATE ON public.sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_nutrition_plans_updated_at
    BEFORE UPDATE ON public.nutrition_plans
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_workout_plans_updated_at
    BEFORE UPDATE ON public.workout_plans
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Trigger to update trainer rating when review is added/updated
CREATE OR REPLACE FUNCTION trigger_update_trainer_rating()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        PERFORM calculate_trainer_rating(NEW.trainer_id);
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        PERFORM calculate_trainer_rating(OLD.trainer_id);
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_trainer_rating_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.reviews
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_trainer_rating();

-- Trigger to create notification when plan is assigned
CREATE OR REPLACE FUNCTION trigger_plan_assigned_notification()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        IF TG_TABLE_NAME = 'nutrition_plans' THEN
            PERFORM create_notification(
                NEW.trainee_id,
                'خطة غذائية جديدة',
                'تم إرسال خطة غذائية جديدة من مدربك',
                'plan_assigned',
                jsonb_build_object('plan_id', NEW.id, 'plan_type', 'nutrition')
            );
        ELSIF TG_TABLE_NAME = 'workout_plans' THEN
            PERFORM create_notification(
                NEW.trainee_id,
                'خطة رياضية جديدة',
                'تم إرسال خطة رياضية جديدة من مدربك',
                'plan_assigned',
                jsonb_build_object('plan_id', NEW.id, 'plan_type', 'workout')
            );
        END IF;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER nutrition_plan_notification_trigger
    AFTER INSERT ON public.nutrition_plans
    FOR EACH ROW
    EXECUTE FUNCTION trigger_plan_assigned_notification();

CREATE TRIGGER workout_plan_notification_trigger
    AFTER INSERT ON public.workout_plans
    FOR EACH ROW
    EXECUTE FUNCTION trigger_plan_assigned_notification();

-- Trigger to create notification when session is booked
CREATE OR REPLACE FUNCTION trigger_session_booked_notification()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Notify trainee
        PERFORM create_notification(
            NEW.trainee_id,
            'جلسة جديدة مجدولة',
            'تم حجز جلسة جديدة معك',
            'session_booked',
            jsonb_build_object('session_id', NEW.id)
        );
        
        -- Notify trainer
        PERFORM create_notification(
            (SELECT user_id FROM public.trainers WHERE id = NEW.trainer_id),
            'جلسة جديدة مجدولة',
            'تم حجز جلسة جديدة معك',
            'session_booked',
            jsonb_build_object('session_id', NEW.id)
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER session_booked_notification_trigger
    AFTER INSERT ON public.sessions
    FOR EACH ROW
    EXECUTE FUNCTION trigger_session_booked_notification();
