import 'package:flutter/material.dart';
import '../../main.dart';
import '../../theme/app_theme.dart';
import '../../widgets/premium_card.dart';
import '../../widgets/premium_widgets.dart';
import '../../widgets/animated_widgets.dart';
import 'nutrition_plan_details_screen.dart';
import 'workout_plan_details_screen.dart';

class PlansScreen extends StatefulWidget {
  const PlansScreen({super.key});

  @override
  State<PlansScreen> createState() => _PlansScreenState();
}

class _PlansScreenState extends State<PlansScreen> {
  late Future<Map<String, List<Map<String, dynamic>>>> _plansFuture;

  @override
  void initState() {
    super.initState();
    _loadPlans();
  }

  void _loadPlans() {
    final userId = supabase.auth.currentUser!.id;
    _plansFuture = _fetchPlans(userId);
  }

  Future<Map<String, List<Map<String, dynamic>>>> _fetchPlans(
      String userId) async {
    final nutritionResponse = await supabase.from('nutrition_plans').select('''
          *,
          nutrition_media (*)
        ''').eq('trainee_id', userId).order('created_at', ascending: false);

    final workoutResponse = await supabase.from('workout_plans').select('''
          *,
          workout_media (*)
        ''').eq('trainee_id', userId).order('created_at', ascending: false);

    return {
      'nutrition': nutritionResponse,
      'workout': workoutResponse,
    };
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Container(
          decoration: const BoxDecoration(
            gradient: AppTheme.backgroundGradient,
          ),
          child: SafeArea(
            child: Column(
              children: [
                _buildPremiumHeader(),
                const SizedBox(height: 20),
                _buildAdvancedTabBar(),
                const SizedBox(height: 20),
                Expanded(
                  child: FutureBuilder<Map<String, List<Map<String, dynamic>>>>(
                    future: _plansFuture,
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return _buildShimmerLoading();
                      }
                      if (snapshot.hasError) {
                        return _buildErrorState();
                      }
                      final plans = snapshot.data!;
                      final nutritionPlans = plans['nutrition']!;
                      final workoutPlans = plans['workout']!;

                      return TabBarView(
                        children: [
                          _buildAdvancedPlansList(nutritionPlans, 'nutrition'),
                          _buildAdvancedPlansList(workoutPlans, 'workout'),
                        ],
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPremiumHeader() {
    return SlideInAnimation(
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            PremiumIconButton(
              icon: Icons.arrow_back_ios,
              onPressed: () => Navigator.pop(context),
              hasGradient: false,
              backgroundColor: AppTheme.surfaceLight,
              iconColor: AppTheme.primaryGold,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'خططك المخصصة',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimary,
                        ),
                  ),
                  Text(
                    'اتبع خططك الغذائية والرياضية',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textSecondary,
                        ),
                  ),
                ],
              ),
            ),
            FloatingWidget(
              child: Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: AppTheme.primaryShadow,
                ),
                child: const Icon(
                  Icons.assignment,
                  color: Colors.black,
                  size: 26,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdvancedTabBar() {
    return SlideInAnimation(
      delay: 200,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20),
        decoration: BoxDecoration(
          color: AppTheme.surfaceLight,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: AppTheme.primaryGold.withValues(alpha: 0.1),
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: TabBar(
          labelColor: Colors.black,
          unselectedLabelColor: AppTheme.textSecondary,
          indicator: BoxDecoration(
            gradient: AppTheme.primaryGradient,
            borderRadius: BorderRadius.circular(12),
          ),
          indicatorPadding: const EdgeInsets.all(4),
          labelStyle: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
          unselectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
          tabs: const [
            Tab(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.restaurant_menu, size: 18),
                  SizedBox(width: 8),
                  Text('الخطط الغذائية'),
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.fitness_center, size: 18),
                  SizedBox(width: 8),
                  Text('الخطط الرياضية'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmerLoading() {
    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: 4,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: ShimmerLoading(
            child: Container(
              height: 180,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildErrorState() {
    return ScaleInAnimation(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                color: AppTheme.coralRed.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.error_outline,
                size: 50,
                color: AppTheme.coralRed,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'حدث خطأ في تحميل الخطط',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'يرجى المحاولة مرة أخرى',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondary,
                  ),
            ),
            const SizedBox(height: 24),
            PremiumButton(
              text: 'إعادة المحاولة',
              onPressed: () => _loadPlans(),
              icon: Icons.refresh,
              width: 160,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdvancedPlansList(
      List<Map<String, dynamic>> plans, String type) {
    if (plans.isEmpty) {
      return _buildEmptyState(type);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: plans.length,
      itemBuilder: (context, index) {
        final plan = plans[index];
        return SlideInAnimation(
          delay: index * 100,
          child: _buildPremiumPlanCard(plan, type, index),
        );
      },
    );
  }

  Widget _buildEmptyState(String type) {
    final icon =
        type == 'nutrition' ? Icons.restaurant_menu : Icons.fitness_center;
    final title =
        type == 'nutrition' ? 'لا توجد خطط غذائية' : 'لا توجد خطط رياضية';
    final subtitle = type == 'nutrition'
        ? 'سيتم إضافة خطط غذائية قريباً'
        : 'سيتم إضافة خطط رياضية قريباً';

    return ScaleInAnimation(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                color: AppTheme.textMuted.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                icon,
                size: 50,
                color: AppTheme.textMuted,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondary,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPremiumPlanCard(
      Map<String, dynamic> plan, String type, int index) {
    final isNutrition = type == 'nutrition';
    final title = plan['title'] ?? (isNutrition ? 'خطة غذائية' : 'خطة رياضية');
    final description = plan['description'] ?? 'وصف الخطة';
    final duration = plan['duration_weeks'] ?? 4;
    final difficulty = plan['difficulty_level'] ?? 'متوسط';

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: PremiumCard(
        gradientColors: isNutrition
            ? [
                AppTheme.emeraldGreen.withValues(alpha: 0.1),
                AppTheme.lightBlue.withValues(alpha: 0.05),
              ]
            : [
                AppTheme.warmOrange.withValues(alpha: 0.1),
                AppTheme.primaryGold.withValues(alpha: 0.05),
              ],
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    gradient: isNutrition
                        ? LinearGradient(
                            colors: [
                              AppTheme.emeraldGreen,
                              AppTheme.lightBlue,
                            ],
                          )
                        : LinearGradient(
                            colors: [
                              AppTheme.warmOrange,
                              AppTheme.primaryGold,
                            ],
                          ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: (isNutrition
                                ? AppTheme.emeraldGreen
                                : AppTheme.warmOrange)
                            .withValues(alpha: 0.3),
                        blurRadius: 12,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: Icon(
                    isNutrition ? Icons.restaurant_menu : Icons.fitness_center,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        description,
                        style: const TextStyle(
                          color: AppTheme.textSecondary,
                          fontSize: 14,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color:
                        _getDifficultyColor(difficulty).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: _getDifficultyColor(difficulty)
                          .withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    difficulty,
                    style: TextStyle(
                      color: _getDifficultyColor(difficulty),
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildInfoChip(
                  Icons.schedule,
                  '$duration أسابيع',
                  AppTheme.lightBlue,
                ),
                const SizedBox(width: 12),
                _buildInfoChip(
                  Icons.trending_up,
                  'نشط',
                  AppTheme.emeraldGreen,
                ),
              ],
            ),
            const SizedBox(height: 16),
            PremiumButton(
              text: 'عرض التفاصيل',
              onPressed: () {
                if (isNutrition) {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => NutritionPlanDetailsScreen(
                        planId: plan['id'],
                      ),
                    ),
                  );
                } else {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => WorkoutPlanDetailsScreen(
                        planId: plan['id'],
                      ),
                    ),
                  );
                }
              },
              hasGradient: false,
              backgroundColor: AppTheme.surfaceLight,
              textColor: AppTheme.primaryGold,
              height: 45,
              icon: Icons.visibility,
              width: double.infinity,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip(IconData icon, String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'سهل':
        return AppTheme.emeraldGreen;
      case 'متوسط':
        return AppTheme.warmOrange;
      case 'صعب':
        return AppTheme.coralRed;
      default:
        return AppTheme.primaryGold;
    }
  }
}
