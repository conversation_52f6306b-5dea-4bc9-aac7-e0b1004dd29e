-- Insert sample data for testing

-- Insert system settings
INSERT INTO public.system_settings (key, value, description) VALUES
('app_version', '"1.0.0"', 'Current app version'),
('maintenance_mode', 'false', 'Maintenance mode flag'),
('max_sessions_per_day', '8', 'Maximum sessions per trainer per day'),
('session_reminder_hours', '2', 'Hours before session to send reminder'),
('subscription_grace_period_days', '3', 'Grace period for expired subscriptions');

-- Insert sample specializations for trainers
INSERT INTO public.system_settings (key, value, description) VALUES
('trainer_specializations', 
'["خسارة الوزن", "بناء العضل", "تمارين القلب", "التغذية الرياضية", "إعادة التأهيل", "اللياقة العامة", "تدريب كبار السن", "تدريب الأطفال"]', 
'Available trainer specializations');

-- Insert sample health conditions
INSERT INTO public.system_settings (key, value, description) VALUES
('health_conditions', 
'["السكري", "ضغط الدم", "أمراض القلب", "التهاب المفاصل", "هشاشة العظام", "الربو", "مشاكل الظهر", "إصابات رياضية"]', 
'Common health conditions');

-- Insert sample dietary preferences
INSERT INTO public.system_settings (key, value, description) VALUES
('dietary_preferences', 
'["نباتي", "عالي البروتين", "قليل الكربوهيدرات", "خالي من الجلوتين", "كيتو", "متوسطي", "خالي من اللاكتوز", "حلال"]', 
'Available dietary preferences');

-- Insert sample trainer (you'll need to create this user first in Supabase Auth)
-- This is just an example - in production, trainers will register through the app
/*
INSERT INTO public.users (id, email, full_name, user_type) VALUES
('550e8400-e29b-41d4-a716-446655440001', '<EMAIL>', 'أحمد محمد - مدرب معتمد', 'trainer');

INSERT INTO public.trainers (user_id, specialization, bio, experience_years, certifications, price_per_session, price_per_month, is_verified, is_available) VALUES
('550e8400-e29b-41d4-a716-446655440001', 
'{"خسارة الوزن", "بناء العضل", "التغذية الرياضية"}', 
'مدرب معتمد مع خبرة 8 سنوات في مجال اللياقة البدنية والتغذية الرياضية. متخصص في برامج خسارة الوزن وبناء العضل.',
8,
'{"ACSM Certified Personal Trainer", "Precision Nutrition Level 1", "First Aid CPR"}',
150.00,
800.00,
true,
true);
*/
