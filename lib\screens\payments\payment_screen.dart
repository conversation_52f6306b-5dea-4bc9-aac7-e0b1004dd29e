import 'package:flutter/material.dart';
import '../../main.dart';
import '../../theme/app_theme.dart';
import '../../widgets/custom_app_bar.dart';

class PaymentScreen extends StatefulWidget {
  final Map<String, dynamic> trainer;
  final String planType;
  final double amount;

  const PaymentScreen({
    super.key,
    required this.trainer,
    required this.planType,
    required this.amount,
  });

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  bool _isLoading = false;

  Future<void> _processPayment() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Simulate payment processing
      await Future.delayed(const Duration(seconds: 2));

      final traineeId = supabase.auth.currentUser!.id;
      final trainerId = widget.trainer['id'];

      // Create subscription
      final subscriptionResponse = await supabase.from('subscriptions').insert({
        'trainee_id': traineeId,
        'trainer_id': trainerId,
        'plan_type': widget.planType,
        'price': widget.amount,
        'start_date': DateTime.now().toIso8601String(),
        'end_date': widget.planType == 'monthly'
            ? DateTime.now().add(const Duration(days: 30)).toIso8601String()
            : DateTime.now().add(const Duration(days: 1)).toIso8601String(),
        'status': 'active',
      }).select();

      final subscriptionId = subscriptionResponse[0]['id'];

      // Create payment record
      await supabase.from('payments').insert({
        'subscription_id': subscriptionId,
        'amount': widget.amount,
        'status': 'completed',
        'paid_at': DateTime.now().toIso8601String(),
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم الدفع بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
      Navigator.of(context).popUntil((route) => route.isFirst);
    } catch (error) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ: ${error.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'الدفع',
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص الطلب',
              style: const TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimary,
              ),
            ),
            const SizedBox(height: 20),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('المدرب',
                            style: TextStyle(color: AppTheme.textSecondary)),
                        Text(widget.trainer['users']['full_name'],
                            style:
                                const TextStyle(color: AppTheme.textPrimary)),
                      ],
                    ),
                    const Divider(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('الخطة',
                            style: TextStyle(color: AppTheme.textSecondary)),
                        Text(
                            widget.planType == 'monthly'
                                ? 'اشتراك شهري'
                                : 'جلسة واحدة',
                            style:
                                const TextStyle(color: AppTheme.textPrimary)),
                      ],
                    ),
                    const Divider(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('المبلغ',
                            style: TextStyle(color: AppTheme.textSecondary)),
                        Text('${widget.amount} ريال',
                            style: const TextStyle(
                                color: AppTheme.primaryGold,
                                fontWeight: FontWeight.bold)),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const Spacer(),
            ElevatedButton(
              onPressed: _isLoading ? null : _processPayment,
              child: _isLoading
                  ? const CircularProgressIndicator(color: Colors.black)
                  : const Text('تأكيد الدفع'),
            ),
          ],
        ),
      ),
    );
  }
}
