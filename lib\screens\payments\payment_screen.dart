import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../theme/app_theme.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/animated_widgets.dart';

class PaymentScreen extends StatefulWidget {
  final Map<String, dynamic> trainer;
  final String planType;
  final double amount;
  final bool isRenewal;
  final bool isReactivation;
  final String? subscriptionId;

  const PaymentScreen({
    super.key,
    required this.trainer,
    required this.planType,
    required this.amount,
    this.isRenewal = false,
    this.isReactivation = false,
    this.subscriptionId,
  });

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  final supabase = Supabase.instance.client;
  bool _isLoading = false;
  String _selectedPaymentMethod = 'credit_card';

  // Helper methods for safe data access
  String _getTrainerName() {
    try {
      // Try different possible structures
      if (widget.trainer['users'] != null &&
          widget.trainer['users']['full_name'] != null) {
        return widget.trainer['users']['full_name'].toString();
      } else if (widget.trainer['full_name'] != null) {
        return widget.trainer['full_name'].toString();
      } else if (widget.trainer['name'] != null) {
        return widget.trainer['name'].toString();
      } else {
        return 'مدرب غير محدد';
      }
    } catch (e) {
      return 'مدرب غير محدد';
    }
  }

  String _getTrainerId() {
    try {
      if (widget.trainer['id'] != null) {
        return widget.trainer['id'].toString();
      } else if (widget.trainer['user_id'] != null) {
        return widget.trainer['user_id'].toString();
      } else {
        throw Exception('معرف المدرب غير موجود');
      }
    } catch (e) {
      throw Exception('معرف المدرب غير صحيح');
    }
  }

  Future<void> _processPayment() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Simulate payment processing
      await Future.delayed(const Duration(seconds: 2));

      final traineeId = supabase.auth.currentUser!.id;
      final trainerId = _getTrainerId();

      if (widget.isRenewal && widget.subscriptionId != null) {
        // Update existing subscription for renewal
        await _renewSubscription();
      } else if (widget.isReactivation && widget.subscriptionId != null) {
        // Reactivate cancelled subscription
        await _reactivateSubscription();
      } else {
        // Create new subscription
        await _createNewSubscription(traineeId, trainerId);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_getSuccessMessage()),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).popUntil((route) => route.isFirst);
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: ${error.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _createNewSubscription(
      String traineeId, String trainerId) async {
    try {
      // Create subscription
      final subscriptionResponse = await supabase.from('subscriptions').insert({
        'trainee_id': traineeId,
        'trainer_id': trainerId,
        'plan_type': widget.planType,
        'price': widget.amount,
        'start_date': DateTime.now().toIso8601String(),
        'end_date': widget.planType == 'monthly'
            ? DateTime.now().add(const Duration(days: 30)).toIso8601String()
            : DateTime.now().add(const Duration(days: 1)).toIso8601String(),
        'status': 'active',
        'auto_renew': false,
      }).select();

      if (subscriptionResponse.isEmpty) {
        throw Exception('فشل في إنشاء الاشتراك');
      }

      final subscriptionId = subscriptionResponse[0]['id'];

      // Create payment record
      await supabase.from('payments').insert({
        'subscription_id': subscriptionId,
        'amount': widget.amount,
        'payment_method': _selectedPaymentMethod,
        'status': 'completed',
        'paid_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      throw Exception('خطأ في إنشاء الاشتراك: ${e.toString()}');
    }
  }

  Future<void> _renewSubscription() async {
    // Update subscription dates
    final newEndDate = widget.planType == 'monthly'
        ? DateTime.now().add(const Duration(days: 30))
        : DateTime.now().add(const Duration(days: 1));

    await supabase.from('subscriptions').update({
      'end_date': newEndDate.toIso8601String(),
      'status': 'active',
      'updated_at': DateTime.now().toIso8601String(),
    }).eq('id', widget.subscriptionId!);

    // Create payment record
    await supabase.from('payments').insert({
      'subscription_id': widget.subscriptionId!,
      'amount': widget.amount,
      'payment_method': _selectedPaymentMethod,
      'status': 'completed',
      'paid_at': DateTime.now().toIso8601String(),
    });
  }

  Future<void> _reactivateSubscription() async {
    // Reactivate subscription
    await supabase.from('subscriptions').update({
      'status': 'active',
      'start_date': DateTime.now().toIso8601String(),
      'end_date': widget.planType == 'monthly'
          ? DateTime.now().add(const Duration(days: 30)).toIso8601String()
          : DateTime.now().add(const Duration(days: 1)).toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    }).eq('id', widget.subscriptionId!);

    // Create payment record
    await supabase.from('payments').insert({
      'subscription_id': widget.subscriptionId!,
      'amount': widget.amount,
      'payment_method': _selectedPaymentMethod,
      'status': 'completed',
      'paid_at': DateTime.now().toIso8601String(),
    });
  }

  String _getSuccessMessage() {
    if (widget.isRenewal) {
      return 'تم تجديد الاشتراك بنجاح';
    } else if (widget.isReactivation) {
      return 'تم إعادة تفعيل الاشتراك بنجاح';
    } else {
      return 'تم إنشاء الاشتراك بنجاح';
    }
  }

  String _getScreenTitle() {
    if (widget.isRenewal) {
      return 'تجديد الاشتراك';
    } else if (widget.isReactivation) {
      return 'إعادة تفعيل الاشتراك';
    } else {
      return 'الدفع';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: _getScreenTitle(),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFF8F9FA),
              Color(0xFFFFFFFF),
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SlideInAnimation(
                  delay: 100,
                  child: Text(
                    'ملخص الطلب',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary,
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('المدرب',
                                style:
                                    TextStyle(color: AppTheme.textSecondary)),
                            Text(_getTrainerName(),
                                style: const TextStyle(
                                    color: AppTheme.textPrimary)),
                          ],
                        ),
                        const Divider(),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('الخطة',
                                style:
                                    TextStyle(color: AppTheme.textSecondary)),
                            Text(
                                widget.planType == 'monthly'
                                    ? 'اشتراك شهري'
                                    : 'جلسة واحدة',
                                style: const TextStyle(
                                    color: AppTheme.textPrimary)),
                          ],
                        ),
                        const Divider(),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('المبلغ',
                                style:
                                    TextStyle(color: AppTheme.textSecondary)),
                            Text('${widget.amount} ريال',
                                style: const TextStyle(
                                    color: AppTheme.primaryGold,
                                    fontWeight: FontWeight.bold)),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                const Spacer(),
                ElevatedButton(
                  onPressed: _isLoading ? null : _processPayment,
                  child: _isLoading
                      ? const CircularProgressIndicator(color: Colors.black)
                      : const Text('تأكيد الدفع'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
