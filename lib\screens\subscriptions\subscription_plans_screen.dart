import 'package:flutter/material.dart';
import '../../main.dart';
import '../../theme/app_theme.dart';
import '../../widgets/premium_button.dart';
import '../../widgets/premium_card.dart';
import '../../widgets/animated_widgets.dart';
import '../../models/subscription_plan_model.dart';
import 'subscription_plan_details_screen.dart';

class SubscriptionPlansScreen extends StatefulWidget {
  const SubscriptionPlansScreen({super.key});

  @override
  State<SubscriptionPlansScreen> createState() =>
      _SubscriptionPlansScreenState();
}

class _SubscriptionPlansScreenState extends State<SubscriptionPlansScreen> {
  late Future<List<Map<String, dynamic>>> _plansFuture;
  String _selectedPlanType = 'all';

  @override
  void initState() {
    super.initState();
    _loadPlans();
  }

  void _loadPlans() {
    _plansFuture = _fetchPlans();
  }

  Future<List<Map<String, dynamic>>> _fetchPlans() async {
    final response = await supabase.from('subscription_plans').select('''
          *,
          trainers(*, users(*))
        ''').eq('is_active', true).order('price', ascending: true);

    return response;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              _buildFilterTabs(),
              Expanded(
                child: FutureBuilder<List<Map<String, dynamic>>>(
                  future: _plansFuture,
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return _buildLoadingState();
                    }
                    if (snapshot.hasError) {
                      return _buildErrorState();
                    }

                    final allPlans = snapshot.data!;
                    final filteredPlans = _filterPlans(allPlans);

                    if (filteredPlans.isEmpty) {
                      return _buildEmptyState();
                    }

                    return _buildPlansList(filteredPlans);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return SlideInAnimation(
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            PremiumIconButton(
              icon: Icons.arrow_back_ios,
              onPressed: () => Navigator.pop(context),
              hasGradient: false,
              backgroundColor: AppTheme.surfaceLight,
              iconColor: AppTheme.primaryGold,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'خطط الاشتراك',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimary,
                        ),
                  ),
                  Text(
                    'اختر الخطة المناسبة لك',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textSecondary,
                        ),
                  ),
                ],
              ),
            ),
            FloatingWidget(
              child: Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: AppTheme.primaryShadow,
                ),
                child: const Icon(
                  Icons.card_membership,
                  color: Colors.black,
                  size: 26,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterTabs() {
    return SlideInAnimation(
      delay: 200,
      child: Container(
        height: 50,
        margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        child: Row(
          children: [
            Expanded(child: _buildFilterTab('all', 'الكل')),
            const SizedBox(width: 8),
            Expanded(child: _buildFilterTab('basic', 'بازيك')),
            const SizedBox(width: 8),
            Expanded(child: _buildFilterTab('standard', 'ستاندرد')),
            const SizedBox(width: 8),
            Expanded(child: _buildFilterTab('premium', 'بريميوم')),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterTab(String type, String title) {
    final isSelected = _selectedPlanType == type;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedPlanType = type;
        });
      },
      child: Container(
        decoration: BoxDecoration(
          gradient: isSelected ? AppTheme.primaryGradient : null,
          color: isSelected ? null : AppTheme.surfaceLight,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected
                ? Colors.transparent
                : AppTheme.primaryGold.withValues(alpha: 0.2),
          ),
        ),
        child: Center(
          child: Text(
            title,
            style: TextStyle(
              color: isSelected ? Colors.black : AppTheme.textSecondary,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
              fontSize: 14,
            ),
          ),
        ),
      ),
    );
  }

  List<Map<String, dynamic>> _filterPlans(List<Map<String, dynamic>> plans) {
    if (_selectedPlanType == 'all') return plans;
    return plans
        .where((plan) =>
            plan['name'].toString().toLowerCase() == _selectedPlanType)
        .toList();
  }

  Widget _buildLoadingState() {
    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: 3,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 20),
          child: ShimmerLoading(
            child: Container(
              height: 200,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildErrorState() {
    return ScaleInAnimation(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                color: AppTheme.coralRed.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.error_outline,
                size: 50,
                color: AppTheme.coralRed,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'حدث خطأ في تحميل الخطط',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'يرجى المحاولة مرة أخرى',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondary,
                  ),
            ),
            const SizedBox(height: 24),
            PremiumButton(
              text: 'إعادة المحاولة',
              onPressed: () => _loadPlans(),
              icon: Icons.refresh,
              width: 160,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return ScaleInAnimation(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                color: AppTheme.textMuted.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.card_membership,
                size: 50,
                color: AppTheme.textMuted,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'لا توجد خطط متاحة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'سيتم إضافة خطط جديدة قريباً',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondary,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlansList(List<Map<String, dynamic>> plans) {
    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: plans.length,
      itemBuilder: (context, index) {
        final plan = plans[index];
        return SlideInAnimation(
          delay: index * 100,
          child: _buildPlanCard(plan, index),
        );
      },
    );
  }

  Widget _buildPlanCard(Map<String, dynamic> plan, int index) {
    final trainer = plan['trainers'];
    final user = trainer['users'];
    final planName = plan['name'];
    final features = _parseStringList(plan['features']);
    final isPopular = planName.toLowerCase() == 'standard';

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: Stack(
        children: [
          PremiumCard(
            hasGradient: true,
            gradientColors: [
              SubscriptionPlan.getPlanColor(planName).withValues(alpha: 0.1),
              AppTheme.primaryGold.withValues(alpha: 0.05),
            ],
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // العنوان والسعر
                Row(
                  children: [
                    Container(
                      width: 70,
                      height: 70,
                      decoration: BoxDecoration(
                        color: SubscriptionPlan.getPlanColor(planName),
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: SubscriptionPlan.getPlanColor(planName)
                                .withValues(alpha: 0.4),
                            blurRadius: 15,
                            spreadRadius: 3,
                          ),
                        ],
                      ),
                      child: Icon(
                        SubscriptionPlan.getPlanIcon(planName),
                        color: Colors.white,
                        size: 35,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'خطة $planName',
                            style: const TextStyle(
                              fontSize: 22,
                              fontWeight: FontWeight.bold,
                              color: AppTheme.textPrimary,
                            ),
                          ),
                          Text(
                            'مع ${user['full_name']}',
                            style: const TextStyle(
                              color: AppTheme.textSecondary,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '${plan['price']} ريال',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: SubscriptionPlan.getPlanColor(planName),
                          ),
                        ),
                        Text(
                          '${plan['duration_days']} يوم',
                          style: const TextStyle(
                            color: AppTheme.textSecondary,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

                // الوصف
                if (plan['description'] != null) ...[
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: SubscriptionPlan.getPlanColor(planName)
                          .withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      plan['description'],
                      style: const TextStyle(
                        color: AppTheme.textPrimary,
                        fontSize: 14,
                        height: 1.4,
                      ),
                    ),
                  ),
                ],

                // المميزات
                if (features.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  Text(
                    'مميزات الخطة:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: SubscriptionPlan.getPlanColor(planName),
                    ),
                  ),
                  const SizedBox(height: 12),
                  ...features.take(4).map((feature) => Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: Row(
                          children: [
                            Container(
                              width: 20,
                              height: 20,
                              decoration: BoxDecoration(
                                color: AppTheme.emeraldGreen,
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: const Icon(
                                Icons.check,
                                size: 14,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                feature,
                                style: const TextStyle(
                                  color: AppTheme.textPrimary,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )),
                  if (features.length > 4)
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        'و ${features.length - 4} مميزات أخرى...',
                        style: TextStyle(
                          color: SubscriptionPlan.getPlanColor(planName),
                          fontSize: 12,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),
                ],

                // زر الاشتراك
                const SizedBox(height: 20),
                Row(
                  children: [
                    Expanded(
                      child: PremiumButton(
                        text: 'عرض التفاصيل',
                        onPressed: () => _viewPlanDetails(plan),
                        hasGradient: false,
                        backgroundColor: AppTheme.surfaceLight,
                        textColor: AppTheme.textPrimary,
                        height: 45,
                        icon: Icons.info_outline,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: PremiumButton(
                        text: 'اشترك الآن',
                        onPressed: () => _subscribeToPlan(plan),
                        hasGradient: true,
                        gradientColors: [
                          SubscriptionPlan.getPlanColor(planName),
                          SubscriptionPlan.getPlanColor(planName)
                              .withValues(alpha: 0.8),
                        ],
                        height: 45,
                        icon: Icons.payment,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // شارة الأكثر شعبية
          if (isPopular)
            Positioned(
              top: -10,
              right: 20,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [AppTheme.primaryGold, AppTheme.warmOrange],
                  ),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.primaryGold.withValues(alpha: 0.4),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Text(
                  'الأكثر شعبية',
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _viewPlanDetails(Map<String, dynamic> plan) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SubscriptionPlanDetailsScreen(plan: plan),
      ),
    ).then((result) {
      // إذا تم الاشتراك بنجاح، يمكن تحديث القائمة
      if (result == true) {
        _loadPlans();
      }
    });
  }

  void _subscribeToPlan(Map<String, dynamic> plan) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Row(
          children: [
            Icon(
              SubscriptionPlan.getPlanIcon(plan['name']),
              color: SubscriptionPlan.getPlanColor(plan['name']),
            ),
            const SizedBox(width: 12),
            Text('تأكيد الاشتراك'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('هل تريد الاشتراك في خطة ${plan['name']}؟'),
            const SizedBox(height: 8),
            Text(
              'السعر: ${plan['price']} ريال لـ ${plan['duration_days']} يوم',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryGold,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          PremiumButton(
            text: 'تأكيد',
            onPressed: () {
              Navigator.pop(context);
              _processPurchase(plan);
            },
            width: 80,
            height: 35,
          ),
        ],
      ),
    );
  }

  void _processPurchase(Map<String, dynamic> plan) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(
              child: Text('تم تأكيد الاشتراك في خطة ${plan['name']} بنجاح!'),
            ),
          ],
        ),
        backgroundColor: AppTheme.emeraldGreen,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  List<String> _parseStringList(dynamic data) {
    if (data == null) return [];

    if (data is List) {
      return data.map((e) => e.toString()).toList();
    }

    if (data is String) {
      if (data.isEmpty) return [];
      try {
        return data
            .split(',')
            .map((e) => e.trim())
            .where((e) => e.isNotEmpty)
            .toList();
      } catch (e) {
        return [data];
      }
    }

    return [];
  }
}
