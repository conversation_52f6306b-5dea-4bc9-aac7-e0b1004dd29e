class Review {
  final String id;
  final String traineeId;
  final String trainerId;
  final String? sessionId;
  final int rating;
  final String? comment;
  final bool isAnonymous;
  final DateTime createdAt;
  final DateTime updatedAt;

  // بيانات إضافية للعرض
  final String? traineeName;
  final Map<String, dynamic>? traineeData;
  final Map<String, dynamic>? sessionData;

  Review({
    required this.id,
    required this.traineeId,
    required this.trainerId,
    this.sessionId,
    required this.rating,
    this.comment,
    this.isAnonymous = false,
    required this.createdAt,
    required this.updatedAt,
    this.traineeName,
    this.traineeData,
    this.sessionData,
  });

  factory Review.fromJson(Map<String, dynamic> json) {
    final userData = _getSafeMap(json, 'users');
    final traineeName =
        userData != null ? _getSafeString(userData, 'full_name') : null;

    return Review(
      id: _getSafeString(json, 'id', ''),
      traineeId: _getSafeString(json, 'trainee_id', ''),
      trainerId: _getSafeString(json, 'trainer_id', ''),
      sessionId: _getSafeString(json, 'session_id'),
      rating: _getSafeInt(json, 'rating', 1),
      comment: _getSafeString(json, 'comment'),
      isAnonymous: _getSafeBool(json, 'is_anonymous', false),
      createdAt: _getSafeDateTime(json, 'created_at') ?? DateTime.now(),
      updatedAt: _getSafeDateTime(json, 'updated_at') ?? DateTime.now(),
      traineeName: traineeName,
      traineeData: userData,
      sessionData: _getSafeMap(json, 'session_data'),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'trainee_id': traineeId,
      'trainer_id': trainerId,
      'session_id': sessionId,
      'rating': rating,
      'comment': comment,
      'is_anonymous': isAnonymous,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // دوال مساعدة آمنة لتحويل البيانات
  static String _getSafeString(Map<String, dynamic> data, String key,
      [String? defaultValue]) {
    final value = data[key];
    if (value == null) return defaultValue ?? '';
    return value.toString();
  }

  static int _getSafeInt(
      Map<String, dynamic> data, String key, int defaultValue) {
    final value = data[key];
    if (value == null) return defaultValue;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      return int.tryParse(value) ?? defaultValue;
    }
    return defaultValue;
  }

  static bool _getSafeBool(
      Map<String, dynamic> data, String key, bool defaultValue) {
    final value = data[key];
    if (value == null) return defaultValue;
    if (value is bool) return value;
    if (value is String) {
      return value.toLowerCase() == 'true';
    }
    return defaultValue;
  }

  static Map<String, dynamic>? _getSafeMap(
      Map<String, dynamic> data, String key) {
    final value = data[key];
    if (value == null) return null;
    if (value is Map<String, dynamic>) return value;
    if (value is Map) return Map<String, dynamic>.from(value);
    return null;
  }

  static DateTime? _getSafeDateTime(Map<String, dynamic> data, String key) {
    final value = data[key];
    if (value == null) return null;
    if (value is String) {
      return DateTime.tryParse(value);
    }
    return null;
  }

  // دالة للحصول على اسم المتدرب المعروض
  String get displayName {
    if (isAnonymous) return 'مجهول';
    if (traineeName != null && traineeName!.isNotEmpty) {
      return traineeName!;
    }
    if (traineeData != null) {
      return _getSafeString(traineeData!, 'full_name', 'متدرب');
    }
    return 'متدرب';
  }

  // دالة للحصول على صورة المتدرب
  String? get traineeAvatar {
    if (isAnonymous) return null;
    if (traineeData != null) {
      return _getSafeString(traineeData!, 'avatar_url');
    }
    return null;
  }

  // دالة للحصول على عنوان الجلسة
  String? get sessionTitle {
    if (sessionData != null) {
      return _getSafeString(sessionData!, 'title');
    }
    return null;
  }

  // دالة للحصول على نص التقييم
  String get ratingText {
    switch (rating) {
      case 1:
        return 'ضعيف جداً';
      case 2:
        return 'ضعيف';
      case 3:
        return 'متوسط';
      case 4:
        return 'جيد';
      case 5:
        return 'ممتاز';
      default:
        return 'غير محدد';
    }
  }

  // دالة لنسخ الكائن مع تعديل بعض القيم
  Review copyWith({
    String? id,
    String? traineeId,
    String? trainerId,
    String? sessionId,
    int? rating,
    String? comment,
    bool? isAnonymous,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? traineeData,
    Map<String, dynamic>? sessionData,
  }) {
    return Review(
      id: id ?? this.id,
      traineeId: traineeId ?? this.traineeId,
      trainerId: trainerId ?? this.trainerId,
      sessionId: sessionId ?? this.sessionId,
      rating: rating ?? this.rating,
      comment: comment ?? this.comment,
      isAnonymous: isAnonymous ?? this.isAnonymous,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      traineeData: traineeData ?? this.traineeData,
      sessionData: sessionData ?? this.sessionData,
    );
  }

  @override
  String toString() {
    return 'Review(id: $id, trainerId: $trainerId, rating: $rating, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Review && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// فئة لحساب إحصائيات التقييمات
class ReviewStats {
  final double averageRating;
  final int totalReviews;
  final Map<int, int> ratingDistribution;

  ReviewStats({
    required this.averageRating,
    required this.totalReviews,
    required this.ratingDistribution,
  });

  factory ReviewStats.fromReviews(List<Review> reviews) {
    if (reviews.isEmpty) {
      return ReviewStats(
        averageRating: 0.0,
        totalReviews: 0,
        ratingDistribution: {1: 0, 2: 0, 3: 0, 4: 0, 5: 0},
      );
    }

    final distribution = <int, int>{1: 0, 2: 0, 3: 0, 4: 0, 5: 0};
    double totalRating = 0;

    for (final review in reviews) {
      totalRating += review.rating;
      distribution[review.rating] = (distribution[review.rating] ?? 0) + 1;
    }

    return ReviewStats(
      averageRating: totalRating / reviews.length,
      totalReviews: reviews.length,
      ratingDistribution: distribution,
    );
  }

  // دالة للحصول على نسبة كل تقييم
  double getPercentage(int rating) {
    if (totalReviews == 0) return 0.0;
    return (ratingDistribution[rating] ?? 0) / totalReviews * 100;
  }
}
