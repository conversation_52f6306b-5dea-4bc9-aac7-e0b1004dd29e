import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/premium_card.dart';
import '../../widgets/premium_widgets.dart';
import '../../widgets/animated_widgets.dart';
import '../subscriptions/new_subscription_screen.dart';

class TrainerDetailsScreen extends StatefulWidget {
  final Map<String, dynamic> trainer;

  const TrainerDetailsScreen({
    super.key,
    required this.trainer,
  });

  @override
  State<TrainerDetailsScreen> createState() => _TrainerDetailsScreenState();
}

class _TrainerDetailsScreenState extends State<TrainerDetailsScreen> {
  // دوال مساعدة للتعامل الآمن مع البيانات
  String? _getSafeString(dynamic data, String key, String? defaultValue) {
    if (data == null) return defaultValue;
    final value = data[key];
    if (value == null) return defaultValue;
    return value.toString();
  }

  double _getSafeDouble(dynamic data, String key, double defaultValue) {
    if (data == null) return defaultValue;
    final value = data[key];
    if (value == null) return defaultValue;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? defaultValue;
    }
    return defaultValue;
  }

  int _getSafeInt(dynamic data, String key, int defaultValue) {
    if (data == null) return defaultValue;
    final value = data[key];
    if (value == null) return defaultValue;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      return int.tryParse(value) ?? defaultValue;
    }
    return defaultValue;
  }

  List<String> _getSafeStringList(dynamic data, String key) {
    if (data == null) return [];
    final value = data[key];
    if (value == null) return [];
    if (value is String && value.isNotEmpty) {
      return value
          .split(',')
          .map((e) => e.trim())
          .where((e) => e.isNotEmpty)
          .toList();
    }
    if (value is List) {
      return value.map((e) => e.toString()).toList();
    }
    return [];
  }

  @override
  Widget build(BuildContext context) {
    // التحقق من صحة البيانات
    if (widget.trainer.isEmpty) {
      return Scaffold(
        backgroundColor: Colors.transparent,
        body: Container(
          decoration: const BoxDecoration(
            gradient: AppTheme.backgroundGradient,
          ),
          child: const Center(
            child: Text(
              'لا توجد بيانات للمدرب',
              style: TextStyle(
                color: AppTheme.textPrimary,
                fontSize: 18,
              ),
            ),
          ),
        ),
      );
    }

    // التعامل الآمن مع البيانات
    final user = widget.trainer['users'];
    final name = _getSafeString(user, 'full_name', 'مدرب') ?? 'مدرب';
    final avatarUrl = _getSafeString(user, 'avatar_url', null);
    final rating = _getSafeDouble(widget.trainer, 'rating', 0.0);
    final specializations =
        _getSafeStringList(widget.trainer, 'specializations');
    final experience = _getSafeInt(widget.trainer, 'experience_years', 0);
    final bio =
        _getSafeString(widget.trainer, 'bio', 'لا توجد معلومات إضافية') ??
            'لا توجد معلومات إضافية';
    final pricePerSession =
        _getSafeDouble(widget.trainer, 'price_per_session', 0.0);
    final pricePerMonth =
        _getSafeDouble(widget.trainer, 'price_per_month', 0.0);

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      _buildTrainerCard(name, avatarUrl, rating, experience),
                      const SizedBox(height: 20),
                      _buildSpecializationsCard(specializations),
                      const SizedBox(height: 20),
                      _buildBioCard(bio),
                      const SizedBox(height: 20),
                      _buildPricingCard(pricePerSession, pricePerMonth),
                      const SizedBox(height: 20),
                      _buildActionButtons(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return SlideInAnimation(
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            PremiumIconButton(
              icon: Icons.arrow_back_ios,
              onPressed: () => Navigator.pop(context),
              hasGradient: false,
              backgroundColor: AppTheme.surfaceLight,
              iconColor: AppTheme.primaryGold,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'تفاصيل المدرب',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimary,
                        ),
                  ),
                  Text(
                    'معلومات شاملة عن المدرب',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textSecondary,
                        ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrainerCard(
      String name, String? avatarUrl, double rating, int experience) {
    return SlideInAnimation(
      delay: 100,
      child: PremiumCard(
        child: Column(
          children: [
            // صورة المدرب
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(30),
                gradient: AppTheme.primaryGradient,
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.primaryGold.withValues(alpha: 0.3),
                    blurRadius: 20,
                    spreadRadius: 3,
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(28),
                child: avatarUrl != null
                    ? ProgressiveImageLoader(
                        imageUrl: avatarUrl,
                        width: 116,
                        height: 116,
                        fit: BoxFit.cover,
                      )
                    : Container(
                        color: AppTheme.primaryGold.withValues(alpha: 0.1),
                        child: const Icon(
                          Icons.person,
                          size: 60,
                          color: AppTheme.primaryGold,
                        ),
                      ),
              ),
            ),
            const SizedBox(height: 20),
            // اسم المدرب
            Text(
              name,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            // التقييم
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ...List.generate(5, (starIndex) {
                  return Icon(
                    starIndex < rating ? Icons.star : Icons.star_border,
                    size: 20,
                    color: AppTheme.warmOrange,
                  );
                }),
                const SizedBox(width: 8),
                Text(
                  '($rating)',
                  style: const TextStyle(
                    color: AppTheme.textSecondary,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // سنوات الخبرة
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: AppTheme.emeraldGreen.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '$experience سنوات خبرة',
                style: const TextStyle(
                  color: AppTheme.emeraldGreen,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSpecializationsCard(List<String> specializations) {
    return SlideInAnimation(
      delay: 200,
      child: PremiumCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.fitness_center,
                  color: AppTheme.primaryGold,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  'التخصصات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (specializations.isNotEmpty)
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: specializations.map((spec) {
                  return Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: AppTheme.lightBlue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: AppTheme.lightBlue.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      spec.trim(),
                      style: const TextStyle(
                        color: AppTheme.lightBlue,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  );
                }).toList(),
              )
            else
              const Text(
                'لا توجد تخصصات محددة',
                style: TextStyle(
                  color: AppTheme.textSecondary,
                  fontSize: 14,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildBioCard(String bio) {
    return SlideInAnimation(
      delay: 300,
      child: PremiumCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.info_outline,
                  color: AppTheme.primaryGold,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  'نبذة عن المدرب',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              bio,
              style: const TextStyle(
                color: AppTheme.textSecondary,
                fontSize: 14,
                height: 1.6,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPricingCard(double pricePerSession, double pricePerMonth) {
    return SlideInAnimation(
      delay: 400,
      child: PremiumCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.payment,
                  color: AppTheme.primaryGold,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  'الأسعار',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildPriceItem('جلسة واحدة', '$pricePerSession ريال'),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildPriceItem('اشتراك شهري', '$pricePerMonth ريال'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPriceItem(String title, String price) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppTheme.surfaceLight,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Text(
            title,
            style: const TextStyle(
              color: AppTheme.textSecondary,
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            price,
            style: const TextStyle(
              color: AppTheme.primaryGold,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return SlideInAnimation(
      delay: 500,
      child: Row(
        children: [
          Expanded(
            child: PremiumButton(
              text: 'اشتراك',
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => NewSubscriptionScreen(
                      trainer: widget.trainer,
                      trainerId: widget.trainer['id'],
                    ),
                  ),
                );
              },
              height: 50,
              icon: Icons.star,
            ),
          ),
        ],
      ),
    );
  }
}
