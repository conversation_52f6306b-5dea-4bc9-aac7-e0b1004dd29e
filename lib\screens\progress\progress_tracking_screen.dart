import 'package:flutter/material.dart';
import '../../models/progress_tracking_model.dart';
import '../../widgets/custom_app_bar.dart';
import 'add_progress_screen.dart';

// نموذج مؤقت لملف المتدرب
class TraineeProfile {
  final String id;
  final String userId;
  final int age;
  final String gender;
  final double weight;
  final double height;
  final String fitnessGoal;
  final int activityLevel;
  final List<String> healthConditions;
  final List<String> dietaryPreferences;
  final List<String> allergies;
  final List<String> medications;
  final String? emergencyContactName;
  final String? emergencyContactPhone;
  final double? targetWeight;
  final DateTime? targetDate;
  final DateTime createdAt;
  final DateTime updatedAt;

  TraineeProfile({
    required this.id,
    required this.userId,
    required this.age,
    required this.gender,
    required this.weight,
    required this.height,
    required this.fitnessGoal,
    required this.activityLevel,
    required this.healthConditions,
    required this.dietaryPreferences,
    required this.allergies,
    required this.medications,
    this.emergencyContactName,
    this.emergencyContactPhone,
    this.targetWeight,
    this.targetDate,
    required this.createdAt,
    required this.updatedAt,
  });
}

class ProgressTrackingScreen extends StatefulWidget {
  final String traineeId;

  const ProgressTrackingScreen({
    super.key,
    required this.traineeId,
  });

  @override
  State<ProgressTrackingScreen> createState() => _ProgressTrackingScreenState();
}

class _ProgressTrackingScreenState extends State<ProgressTrackingScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  List<ProgressTracking> _progressData = [];
  TraineeProfile? _traineeProfile;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadProgressData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadProgressData() async {
    setState(() => _isLoading = true);

    try {
      // TODO: تحميل بيانات التقدم من Supabase
      // final response = await Supabase.instance.client
      //     .from('progress_tracking')
      //     .select()
      //     .eq('trainee_id', widget.traineeId)
      //     .order('recorded_at', ascending: false);

      // TODO: تحميل ملف المتدرب
      // final profileResponse = await Supabase.instance.client
      //     .from('trainees_profiles')
      //     .select()
      //     .eq('user_id', widget.traineeId)
      //     .single();

      // بيانات تجريبية
      _progressData = _generateSampleData();
      _traineeProfile = _generateSampleProfile();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل البيانات: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  List<ProgressTracking> _generateSampleData() {
    final now = DateTime.now();
    return List.generate(10, (index) {
      return ProgressTracking(
        id: 'progress_$index',
        traineeId: widget.traineeId,
        weight: 80.0 - (index * 0.5),
        bodyFatPercentage: 20.0 - (index * 0.3),
        muscleMass: 35.0 + (index * 0.2),
        measurements: {
          'chest': 100.0 - (index * 0.2),
          'waist': 85.0 - (index * 0.3),
          'arms': 35.0 + (index * 0.1),
          'thighs': 60.0 + (index * 0.1),
        },
        photos: [],
        notes: index % 3 == 0 ? 'تحسن ملحوظ في الأداء' : null,
        recordedAt: now.subtract(Duration(days: index * 7)),
        createdAt: now.subtract(Duration(days: index * 7)),
      );
    });
  }

  TraineeProfile _generateSampleProfile() {
    return TraineeProfile(
      id: 'profile_1',
      userId: widget.traineeId,
      age: 25,
      gender: 'male',
      weight: 80.0,
      height: 175.0,
      fitnessGoal: 'lose_weight',
      activityLevel: 3,
      healthConditions: [],
      dietaryPreferences: [],
      allergies: [],
      medications: [],
      emergencyContactName: 'أحمد محمد',
      emergencyContactPhone: '+966501234567',
      targetWeight: 75.0,
      targetDate: DateTime.now().add(const Duration(days: 90)),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: const CustomAppBar(
        title: 'تتبع التقدم',
        showBackButton: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                _buildStatsOverview(),
                _buildTabBar(),
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildWeightTab(),
                      _buildBodyCompositionTab(),
                      _buildMeasurementsTab(),
                      _buildPhotosTab(),
                    ],
                  ),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addNewProgress,
        backgroundColor: Colors.blue,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildStatsOverview() {
    if (_progressData.isEmpty || _traineeProfile == null) {
      return const SizedBox.shrink();
    }

    final latest = _progressData.first;
    final oldest = _progressData.last;
    final weightChange = (latest.weight ?? 0) - (oldest.weight ?? 0);
    final bmi = _calculateBMI(latest.weight, _traineeProfile!.height);

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'الوزن الحالي',
                  '${latest.weight?.toStringAsFixed(1) ?? '--'} كجم',
                  Icons.monitor_weight,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'التغيير',
                  '${weightChange >= 0 ? '+' : ''}${weightChange.toStringAsFixed(1)} كجم',
                  weightChange >= 0 ? Icons.trending_up : Icons.trending_down,
                  weightChange >= 0 ? Colors.green : Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'مؤشر كتلة الجسم',
                  bmi?.toStringAsFixed(1) ?? '--',
                  Icons.analytics,
                  Colors.purple,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'نسبة الدهون',
                  '${latest.bodyFatPercentage?.toStringAsFixed(1) ?? '--'}%',
                  Icons.pie_chart,
                  Colors.orange,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: Colors.blue,
        unselectedLabelColor: Colors.grey,
        indicator: BoxDecoration(
          color: Colors.blue.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        tabs: const [
          Tab(text: 'الوزن'),
          Tab(text: 'التركيب'),
          Tab(text: 'القياسات'),
          Tab(text: 'الصور'),
        ],
      ),
    );
  }

  Widget _buildWeightTab() {
    if (_progressData.isEmpty) {
      return _buildEmptyState('لا توجد بيانات وزن مسجلة');
    }

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // رسم بياني بسيط للوزن
          Container(
            height: 200,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                const Text(
                  'تطور الوزن',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: _buildSimpleWeightChart(),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Expanded(child: _buildProgressList()),
        ],
      ),
    );
  }

  Widget _buildSimpleWeightChart() {
    final weights = _progressData
        .where((p) => p.weight != null)
        .take(7)
        .toList()
        .reversed
        .toList();

    if (weights.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد بيانات كافية للرسم البياني',
          style: TextStyle(color: Colors.grey),
        ),
      );
    }

    final maxWeight =
        weights.map((p) => p.weight!).reduce((a, b) => a > b ? a : b);
    final minWeight =
        weights.map((p) => p.weight!).reduce((a, b) => a < b ? a : b);
    final range = maxWeight - minWeight;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: weights.map((progress) {
        final height = range > 0
            ? ((progress.weight! - minWeight) / range) * 120 + 20
            : 70.0;

        return Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              '${progress.weight!.toStringAsFixed(1)}',
              style: const TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Container(
              width: 20,
              height: height,
              decoration: BoxDecoration(
                color: Colors.blue,
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              '${progress.recordedAt.day}/${progress.recordedAt.month}',
              style: const TextStyle(
                fontSize: 8,
                color: Colors.grey,
              ),
            ),
          ],
        );
      }).toList(),
    );
  }

  Widget _buildBodyCompositionTab() {
    // TODO: تنفيذ تبويب تركيب الجسم
    return _buildEmptyState('قريباً - تتبع تركيب الجسم');
  }

  Widget _buildMeasurementsTab() {
    // TODO: تنفيذ تبويب القياسات
    return _buildEmptyState('قريباً - تتبع القياسات');
  }

  Widget _buildPhotosTab() {
    // TODO: تنفيذ تبويب الصور
    return _buildEmptyState('قريباً - صور التقدم');
  }

  Widget _buildProgressList() {
    return Expanded(
      child: ListView.builder(
        itemCount: _progressData.length,
        itemBuilder: (context, index) {
          final progress = _progressData[index];
          return _buildProgressCard(progress);
        },
      ),
    );
  }

  Widget _buildProgressCard(ProgressTracking progress) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(25),
            ),
            child: const Icon(
              Icons.monitor_weight,
              color: Colors.blue,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${progress.weight?.toStringAsFixed(1) ?? '--'} كجم',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _formatDate(progress.recordedAt),
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
                if (progress.notes != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    progress.notes!,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.analytics_outlined,
            size: 64,
            color: Colors.grey.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  double? _calculateBMI(double? weight, double height) {
    if (weight == null) return null;
    final heightInMeters = height / 100;
    return weight / (heightInMeters * heightInMeters);
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _addNewProgress() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddProgressScreen(
          traineeId: widget.traineeId,
          onProgressAdded: _loadProgressData,
        ),
      ),
    );
  }
}
