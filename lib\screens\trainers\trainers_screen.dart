import 'package:flutter/material.dart';
import '../../main.dart';
import '../../theme/app_theme.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/premium_card.dart';
import '../../widgets/premium_widgets.dart';
import '../../widgets/animated_widgets.dart';
import '../subscriptions/subscription_type_selection_screen.dart';
import 'trainer_details_screen.dart';

class TrainersScreen extends StatefulWidget {
  const TrainersScreen({super.key});

  @override
  State<TrainersScreen> createState() => _TrainersScreenState();
}

class _TrainersScreenState extends State<TrainersScreen> {
  late Future<List<Map<String, dynamic>>> _trainersFuture;

  @override
  void initState() {
    super.initState();
    _loadTrainers();
  }

  void _loadTrainers() {
    _trainersFuture = _fetchTrainers();
  }

  Future<List<Map<String, dynamic>>> _fetchTrainers() async {
    final response = await supabase
        .from('trainers')
        .select('*, users(full_name, avatar_url)')
        .eq('is_available', true)
        .eq('is_verified', true)
        .order('rating', ascending: false);
    return response;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildPremiumHeader(),
              const SizedBox(height: 20),
              _buildSearchAndFilter(),
              const SizedBox(height: 20),
              Expanded(
                child: FutureBuilder<List<Map<String, dynamic>>>(
                  future: _trainersFuture,
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return _buildShimmerLoading();
                    }
                    if (snapshot.hasError) {
                      return _buildErrorState();
                    }
                    final trainers = snapshot.data!;
                    if (trainers.isEmpty) {
                      return _buildEmptyState();
                    }
                    return _buildTrainersList(trainers);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPremiumHeader() {
    return SlideInAnimation(
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            PremiumIconButton(
              icon: Icons.arrow_back_ios,
              onPressed: () => Navigator.pop(context),
              hasGradient: false,
              backgroundColor: AppTheme.surfaceLight,
              iconColor: AppTheme.primaryGold,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'أفضل المدربين',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimary,
                        ),
                  ),
                  Text(
                    'اختر مدربك المثالي لرحلة لياقتك',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textSecondary,
                        ),
                  ),
                ],
              ),
            ),
            FloatingWidget(
              child: Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: AppTheme.primaryShadow,
                ),
                child: const Icon(
                  Icons.people,
                  color: Colors.black,
                  size: 26,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return SlideInAnimation(
      delay: 200,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Row(
          children: [
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: AppTheme.surfaceLight,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.primaryGold.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: TextField(
                  decoration: InputDecoration(
                    hintText: 'البحث عن مدرب...',
                    hintStyle: TextStyle(
                      color: AppTheme.textSecondary.withValues(alpha: 0.7),
                    ),
                    prefixIcon: const Icon(
                      Icons.search,
                      color: AppTheme.primaryGold,
                    ),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.all(16),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            PremiumIconButton(
              icon: Icons.filter_list,
              onPressed: () {
                // تنفيذ فلترة
              },
              hasGradient: false,
              backgroundColor: AppTheme.surfaceLight,
              iconColor: AppTheme.primaryGold,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmerLoading() {
    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: 5,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: ShimmerLoading(
            child: Container(
              height: 160,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildErrorState() {
    return ScaleInAnimation(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                color: AppTheme.coralRed.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.error_outline,
                size: 50,
                color: AppTheme.coralRed,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'حدث خطأ في تحميل المدربين',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'يرجى المحاولة مرة أخرى',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondary,
                  ),
            ),
            const SizedBox(height: 24),
            PremiumButton(
              text: 'إعادة المحاولة',
              onPressed: () => _loadTrainers(),
              icon: Icons.refresh,
              width: 160,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return ScaleInAnimation(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                color: AppTheme.textMuted.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.people_outline,
                size: 50,
                color: AppTheme.textMuted,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'لا يوجد مدربين متاحين',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'سيتم إضافة مدربين جدد قريباً',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondary,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrainersList(List<Map<String, dynamic>> trainers) {
    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: trainers.length,
      itemBuilder: (context, index) {
        final trainer = trainers[index];
        return SlideInAnimation(
          delay: index * 100,
          child: _buildPremiumTrainerCard(trainer, index),
        );
      },
    );
  }

  Widget _buildPremiumTrainerCard(Map<String, dynamic> trainer, int index) {
    final user = trainer['users'];
    final name = user?['full_name'] ?? 'مدرب';
    final avatarUrl = user?['avatar_url'];
    final rating = trainer['rating']?.toDouble() ?? 0.0;
    final specializations = trainer['specialization'] != null
        ? (trainer['specialization'] as List).map((e) => e.toString()).toList()
        : [];
    final experience = trainer['experience_years'] ?? 0;

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: PremiumCard(
        child: Column(
          children: [
            Row(
              children: [
                // صورة المدرب مع تأثير احترافي
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    gradient: AppTheme.primaryGradient,
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme.primaryGold.withValues(alpha: 0.3),
                        blurRadius: 15,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(18),
                    child: avatarUrl != null
                        ? ProgressiveImageLoader(
                            imageUrl: avatarUrl,
                            width: 76,
                            height: 76,
                            fit: BoxFit.cover,
                          )
                        : Container(
                            color: AppTheme.primaryGold.withValues(alpha: 0.1),
                            child: Icon(
                              Icons.person,
                              size: 40,
                              color: AppTheme.primaryGold,
                            ),
                          ),
                  ),
                ),
                const SizedBox(width: 16),
                // معلومات المدرب
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        name,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          ...List.generate(5, (starIndex) {
                            return Icon(
                              starIndex < rating
                                  ? Icons.star
                                  : Icons.star_border,
                              size: 16,
                              color: AppTheme.warmOrange,
                            );
                          }),
                          const SizedBox(width: 8),
                          Text(
                            '($rating)',
                            style: const TextStyle(
                              color: AppTheme.textSecondary,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: AppTheme.emeraldGreen.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          '$experience سنوات خبرة',
                          style: const TextStyle(
                            color: AppTheme.emeraldGreen,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // زر المشاهدة
                PremiumIconButton(
                  icon: Icons.arrow_forward_ios,
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TrainerDetailsScreen(
                          trainer: trainer,
                        ),
                      ),
                    );
                  },
                  size: 40,
                  hasGradient: false,
                  backgroundColor: AppTheme.primaryGold.withValues(alpha: 0.1),
                  iconColor: AppTheme.primaryGold,
                ),
              ],
            ),
            const SizedBox(height: 16),
            // التخصصات
            if (specializations.isNotEmpty) ...[
              Align(
                alignment: Alignment.centerRight,
                child: Text(
                  'التخصصات:',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimary,
                  ),
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: specializations.take(3).map((spec) {
                  return Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          AppTheme.lightBlue.withValues(alpha: 0.2),
                          AppTheme.primaryGold.withValues(alpha: 0.1),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppTheme.primaryGold.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      spec.trim(),
                      style: const TextStyle(
                        color: AppTheme.textPrimary,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  );
                }).toList(),
              ),
              const SizedBox(height: 12),
            ],
            // أزرار العمل
            Row(
              children: [
                Expanded(
                  child: PremiumButton(
                    text: 'عرض التفاصيل',
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => TrainerDetailsScreen(
                            trainer: trainer,
                          ),
                        ),
                      );
                    },
                    hasGradient: false,
                    backgroundColor: AppTheme.surfaceLight,
                    textColor: AppTheme.primaryGold,
                    height: 45,
                    icon: Icons.info_outline,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: PremiumButton(
                    text: 'اشتراك',
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => SubscriptionTypeSelectionScreen(
                            trainer: trainer,
                            trainerId: trainer['id'],
                          ),
                        ),
                      );
                    },
                    height: 45,
                    icon: Icons.star,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
