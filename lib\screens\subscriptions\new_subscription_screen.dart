import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';
import '../../widgets/custom_app_bar.dart';
import '../payments/payment_screen.dart';

class NewSubscriptionScreen extends StatefulWidget {
  final Map<String, dynamic>? trainer;
  final String? trainerId;

  const NewSubscriptionScreen({
    super.key,
    this.trainer,
    this.trainerId,
  });

  @override
  State<NewSubscriptionScreen> createState() => _NewSubscriptionScreenState();
}

class _NewSubscriptionScreenState extends State<NewSubscriptionScreen> {
  String _selectedPlan = 'monthly';

  @override
  Widget build(BuildContext context) {
    final user = widget.trainer?['users'];
    final trainerName =
        user != null && user['full_name'] != null ? user['full_name'] : '---';
    final pricePerMonth =
        widget.trainer != null && widget.trainer!['price_per_month'] != null
            ? widget.trainer!['price_per_month']
            : 0;
    final pricePerSession =
        widget.trainer != null && widget.trainer!['price_per_session'] != null
            ? widget.trainer!['price_per_session']
            : 0;
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'اشتراك جديد',
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الاشتراك مع المدرب: $trainerName',
              style: const TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimary,
              ),
            ),
            const SizedBox(height: 20),
            _buildPlanOption(
              'شهري',
              '$pricePerMonth ريال',
              'monthly',
            ),
            _buildPlanOption(
              'جلسة واحدة',
              '$pricePerSession ريال',
              'session',
            ),
            const Spacer(),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => PaymentScreen(
                      trainer: widget.trainer ?? {},
                      planType: _selectedPlan,
                      amount: _selectedPlan == 'monthly'
                          ? pricePerMonth
                          : pricePerSession,
                    ),
                  ),
                );
              },
              child: const Text('الاستمرار للدفع'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlanOption(String title, String price, String value) {
    return RadioListTile<String>(
      title: Text(title, style: const TextStyle(color: AppTheme.textPrimary)),
      subtitle:
          Text(price, style: const TextStyle(color: AppTheme.textSecondary)),
      value: value,
      groupValue: _selectedPlan,
      onChanged: (newValue) {
        setState(() {
          _selectedPlan = newValue!;
        });
      },
      activeColor: AppTheme.primaryGold,
    );
  }
}
