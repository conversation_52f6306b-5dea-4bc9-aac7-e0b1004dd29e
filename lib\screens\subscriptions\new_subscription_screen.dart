import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';

import '../../widgets/premium_card.dart';
import '../../widgets/premium_button.dart';
import '../../widgets/animated_widgets.dart';
import '../payments/payment_screen.dart';

class NewSubscriptionScreen extends StatefulWidget {
  final Map<String, dynamic>? trainer;
  final String? trainerId;

  const NewSubscriptionScreen({
    super.key,
    this.trainer,
    this.trainerId,
  });

  @override
  State<NewSubscriptionScreen> createState() => _NewSubscriptionScreenState();
}

class _NewSubscriptionScreenState extends State<NewSubscriptionScreen> {
  final String _selectedPlan = 'session';
  bool _autoRenew = false;

  @override
  Widget build(BuildContext context) {
    final trainer = widget.trainer ?? {};
    final user = trainer['users'];
    final trainerName = _getSafeString(user, 'full_name', 'مدرب غير محدد');
    final trainerAvatar = _getSafeString(user, 'avatar_url', '');
    final rating = _getSafeDouble(trainer, 'rating', 0.0);
    final experience = _getSafeInt(trainer, 'experience_years', 0);
    final pricePerMonth = _getSafeDouble(trainer, 'price_per_month', 0.0);
    final pricePerSession = _getSafeDouble(trainer, 'price_per_session', 0.0);
    final specializations = _parseStringList(trainer['specializations']);

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SlideInAnimation(
                        child: _buildTrainerCard(trainerName, trainerAvatar,
                            rating, experience, specializations),
                      ),
                      const SizedBox(height: 24),
                      SlideInAnimation(
                        delay: 200,
                        child:
                            _buildPlanSelection(pricePerMonth, pricePerSession),
                      ),
                      const SizedBox(height: 24),
                      SlideInAnimation(
                        delay: 400,
                        child: _buildSubscriptionOptions(),
                      ),
                      const SizedBox(height: 24),
                      SlideInAnimation(
                        delay: 600,
                        child: _buildPricingSummary(
                            pricePerMonth, pricePerSession),
                      ),
                      const SizedBox(height: 32),
                      SlideInAnimation(
                        delay: 800,
                        child: _buildSubscribeButton(
                            pricePerMonth, pricePerSession),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.arrow_back, color: AppTheme.textPrimary),
          ),
          const Expanded(
            child: Text(
              'حجز جلسة تدريبية',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(width: 48), // للتوازن
        ],
      ),
    );
  }

  Widget _buildTrainerCard(String name, String? avatar, double rating,
      int experience, List<String> specializations) {
    return PremiumCard(
      child: Column(
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: AppTheme.primaryGold.withValues(alpha: 0.2),
                backgroundImage: avatar != null ? NetworkImage(avatar) : null,
                child: avatar == null
                    ? const Icon(Icons.person,
                        size: 30, color: AppTheme.primaryGold)
                    : null,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        const Icon(Icons.star,
                            color: AppTheme.primaryGold, size: 16),
                        const SizedBox(width: 4),
                        Text(
                          rating.toStringAsFixed(1),
                          style: const TextStyle(
                            color: AppTheme.textSecondary,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(width: 16),
                        const Icon(Icons.work,
                            color: AppTheme.primaryGold, size: 16),
                        const SizedBox(width: 4),
                        Text(
                          '$experience سنوات خبرة',
                          style: const TextStyle(
                            color: AppTheme.textSecondary,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (specializations.isNotEmpty) ...[
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 12),
            Align(
              alignment: Alignment.centerRight,
              child: Text(
                'التخصصات:',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimary,
                ),
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: specializations
                  .take(3)
                  .map((spec) => Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: AppTheme.primaryGold.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                              color:
                                  AppTheme.primaryGold.withValues(alpha: 0.3)),
                        ),
                        child: Text(
                          spec.trim(),
                          style: const TextStyle(
                            fontSize: 12,
                            color: AppTheme.primaryGold,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ))
                  .toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPlanSelection(double pricePerMonth, double pricePerSession) {
    return PremiumCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.fitness_center, color: AppTheme.primaryGold, size: 20),
              SizedBox(width: 8),
              Text(
                'تفاصيل الجلسة',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildPlanOption(
            'جلسة تدريبية واحدة',
            '${pricePerSession.toStringAsFixed(0)} ريال / جلسة',
            'session',
            Icons.fitness_center,
            'جلسة تدريبية شخصية مع المدرب لمدة ساعة واحدة',
          ),
        ],
      ),
    );
  }

  Widget _buildPlanOption(String title, String price, String value,
      IconData icon, String description) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.primaryGold.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.primaryGold,
          width: 2,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.primaryGold,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryGold,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  price,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.primaryGold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppTheme.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          const Icon(
            Icons.check_circle,
            color: AppTheme.primaryGold,
            size: 24,
          ),
        ],
      ),
    );
  }

  Widget _buildSubscriptionOptions() {
    return PremiumCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.settings, color: AppTheme.primaryGold, size: 20),
              SizedBox(width: 8),
              Text(
                'خيارات الاشتراك',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SwitchListTile(
            title: const Text(
              'التجديد التلقائي',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppTheme.textPrimary,
              ),
            ),
            subtitle: const Text(
              'سيتم تجديد اشتراكك تلقائياً عند انتهاء المدة',
              style: TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondary,
              ),
            ),
            value: _autoRenew,
            onChanged: (value) {
              setState(() {
                _autoRenew = value;
              });
            },
            activeColor: AppTheme.primaryGold,
            contentPadding: EdgeInsets.zero,
          ),
        ],
      ),
    );
  }

  Widget _buildPricingSummary(double pricePerMonth, double pricePerSession) {
    final selectedPrice =
        _selectedPlan == 'monthly' ? pricePerMonth : pricePerSession;
    final planName =
        _selectedPlan == 'monthly' ? 'الخطة الشهرية' : 'جلسة واحدة';

    return PremiumCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.receipt, color: AppTheme.primaryGold, size: 20),
              SizedBox(width: 8),
              Text(
                'ملخص السعر',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                planName,
                style: const TextStyle(
                  fontSize: 14,
                  color: AppTheme.textPrimary,
                ),
              ),
              Text(
                '${selectedPrice.toStringAsFixed(0)} ريال',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          const Divider(),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'المجموع',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimary,
                ),
              ),
              Text(
                '${selectedPrice.toStringAsFixed(0)} ريال',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryGold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSubscribeButton(double pricePerMonth, double pricePerSession) {
    final selectedPrice =
        _selectedPlan == 'monthly' ? pricePerMonth : pricePerSession;

    return PremiumButton(
      text: 'الاستمرار للدفع',
      onPressed: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => PaymentScreen(
              trainer: widget.trainer ?? {},
              planType: _selectedPlan,
              amount: selectedPrice,
            ),
          ),
        );
      },
      icon: Icons.payment,
      hasGradient: true,
      height: 50,
      width: double.infinity,
    );
  }

  // دوال مساعدة آمنة لتحويل البيانات
  String _getSafeString(dynamic data, String key, String defaultValue) {
    if (data == null) return defaultValue;
    final value = data[key];
    if (value == null) return defaultValue;
    return value.toString();
  }

  double _getSafeDouble(dynamic data, String key, double defaultValue) {
    if (data == null) return defaultValue;
    final value = data[key];
    if (value == null) return defaultValue;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? defaultValue;
    }
    return defaultValue;
  }

  int _getSafeInt(dynamic data, String key, int defaultValue) {
    if (data == null) return defaultValue;
    final value = data[key];
    if (value == null) return defaultValue;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      return int.tryParse(value) ?? defaultValue;
    }
    return defaultValue;
  }

  List<String> _parseStringList(dynamic value) {
    if (value == null) return [];

    // إذا كان JSArray أو List<dynamic>
    if (value is List) {
      return value.map((item) => item.toString()).toList();
    }

    // إذا كان String مفصول بفواصل
    if (value is String) {
      if (value.isEmpty) return [];
      return value.split(',').map((item) => item.trim()).toList();
    }

    // في حالة أي نوع آخر
    return [value.toString()];
  }
}
