import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/premium_card.dart';
import '../../widgets/premium_button.dart';
import '../../widgets/animated_widgets.dart';
import '../payments/payment_screen.dart';

class NewSubscriptionScreen extends StatefulWidget {
  final Map<String, dynamic>? trainer;
  final String? trainerId;

  const NewSubscriptionScreen({
    super.key,
    this.trainer,
    this.trainerId,
  });

  @override
  State<NewSubscriptionScreen> createState() => _NewSubscriptionScreenState();
}

class _NewSubscriptionScreenState extends State<NewSubscriptionScreen> {
  String _selectedPlan = 'monthly';
  bool _autoRenew = false;

  @override
  Widget build(BuildContext context) {
    final trainer = widget.trainer ?? {};
    final user = trainer['users'];
    final trainerName = user?['full_name'] ?? 'مدرب غير محدد';
    final trainerAvatar = user?['avatar_url'];
    final rating = trainer['rating']?.toDouble() ?? 0.0;
    final experience = trainer['experience_years'] ?? 0;
    final pricePerMonth = trainer['price_per_month']?.toDouble() ?? 0.0;
    final pricePerSession = trainer['price_per_session']?.toDouble() ?? 0.0;
    final specializations = trainer['specializations']?.split(',') ?? [];

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SlideInAnimation(
                        child: _buildTrainerCard(trainerName, trainerAvatar,
                            rating, experience, specializations),
                      ),
                      const SizedBox(height: 24),
                      SlideInAnimation(
                        delay: 200,
                        child:
                            _buildPlanSelection(pricePerMonth, pricePerSession),
                      ),
                      const SizedBox(height: 24),
                      SlideInAnimation(
                        delay: 400,
                        child: _buildSubscriptionOptions(),
                      ),
                      const SizedBox(height: 24),
                      SlideInAnimation(
                        delay: 600,
                        child: _buildPricingSummary(
                            pricePerMonth, pricePerSession),
                      ),
                      const SizedBox(height: 32),
                      SlideInAnimation(
                        delay: 800,
                        child: _buildSubscribeButton(
                            pricePerMonth, pricePerSession),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.arrow_back, color: AppTheme.textPrimary),
          ),
          const Expanded(
            child: Text(
              'اشتراك جديد',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(width: 48), // للتوازن
        ],
      ),
    );
  }

  Widget _buildTrainerCard(String name, String? avatar, double rating,
      int experience, List<String> specializations) {
    return PremiumCard(
      child: Column(
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: AppTheme.primaryGold.withValues(alpha: 0.2),
                backgroundImage: avatar != null ? NetworkImage(avatar) : null,
                child: avatar == null
                    ? const Icon(Icons.person,
                        size: 30, color: AppTheme.primaryGold)
                    : null,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        const Icon(Icons.star,
                            color: AppTheme.primaryGold, size: 16),
                        const SizedBox(width: 4),
                        Text(
                          rating.toStringAsFixed(1),
                          style: const TextStyle(
                            color: AppTheme.textSecondary,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(width: 16),
                        const Icon(Icons.work,
                            color: AppTheme.primaryGold, size: 16),
                        const SizedBox(width: 4),
                        Text(
                          '$experience سنوات خبرة',
                          style: const TextStyle(
                            color: AppTheme.textSecondary,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (specializations.isNotEmpty) ...[
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 12),
            Align(
              alignment: Alignment.centerRight,
              child: Text(
                'التخصصات:',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimary,
                ),
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: specializations
                  .take(3)
                  .map((spec) => Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: AppTheme.primaryGold.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                              color:
                                  AppTheme.primaryGold.withValues(alpha: 0.3)),
                        ),
                        child: Text(
                          spec.trim(),
                          style: const TextStyle(
                            fontSize: 12,
                            color: AppTheme.primaryGold,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ))
                  .toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPlanSelection(double pricePerMonth, double pricePerSession) {
    return PremiumCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.card_membership,
                  color: AppTheme.primaryGold, size: 20),
              SizedBox(width: 8),
              Text(
                'اختر خطة الاشتراك',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildPlanOption(
            'الخطة الشهرية',
            '${pricePerMonth.toStringAsFixed(0)} ريال / شهر',
            'monthly',
            Icons.calendar_month,
            'أفضل قيمة للالتزام طويل المدى',
          ),
          const SizedBox(height: 12),
          _buildPlanOption(
            'جلسة واحدة',
            '${pricePerSession.toStringAsFixed(0)} ريال / جلسة',
            'session',
            Icons.fitness_center,
            'مثالي للتجربة أو الجلسات المتقطعة',
          ),
        ],
      ),
    );
  }

  Widget _buildPlanOption(String title, String price, String value,
      IconData icon, String description) {
    final isSelected = _selectedPlan == value;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedPlan = value;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.primaryGold.withValues(alpha: 0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected
                ? AppTheme.primaryGold
                : AppTheme.textSecondary.withValues(alpha: 0.3),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isSelected
                    ? AppTheme.primaryGold
                    : AppTheme.textSecondary.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: isSelected ? Colors.white : AppTheme.textSecondary,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isSelected
                          ? AppTheme.primaryGold
                          : AppTheme.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    price,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: isSelected
                          ? AppTheme.primaryGold
                          : AppTheme.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            Radio<String>(
              value: value,
              groupValue: _selectedPlan,
              onChanged: (newValue) {
                setState(() {
                  _selectedPlan = newValue!;
                });
              },
              activeColor: AppTheme.primaryGold,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubscriptionOptions() {
    return PremiumCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.settings, color: AppTheme.primaryGold, size: 20),
              SizedBox(width: 8),
              Text(
                'خيارات الاشتراك',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SwitchListTile(
            title: const Text(
              'التجديد التلقائي',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppTheme.textPrimary,
              ),
            ),
            subtitle: const Text(
              'سيتم تجديد اشتراكك تلقائياً عند انتهاء المدة',
              style: TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondary,
              ),
            ),
            value: _autoRenew,
            onChanged: (value) {
              setState(() {
                _autoRenew = value;
              });
            },
            activeColor: AppTheme.primaryGold,
            contentPadding: EdgeInsets.zero,
          ),
        ],
      ),
    );
  }

  Widget _buildPricingSummary(double pricePerMonth, double pricePerSession) {
    final selectedPrice =
        _selectedPlan == 'monthly' ? pricePerMonth : pricePerSession;
    final planName =
        _selectedPlan == 'monthly' ? 'الخطة الشهرية' : 'جلسة واحدة';

    return PremiumCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.receipt, color: AppTheme.primaryGold, size: 20),
              SizedBox(width: 8),
              Text(
                'ملخص السعر',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                planName,
                style: const TextStyle(
                  fontSize: 14,
                  color: AppTheme.textPrimary,
                ),
              ),
              Text(
                '${selectedPrice.toStringAsFixed(0)} ريال',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          const Divider(),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'المجموع',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimary,
                ),
              ),
              Text(
                '${selectedPrice.toStringAsFixed(0)} ريال',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryGold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSubscribeButton(double pricePerMonth, double pricePerSession) {
    final selectedPrice =
        _selectedPlan == 'monthly' ? pricePerMonth : pricePerSession;

    return PremiumButton(
      text: 'الاستمرار للدفع',
      onPressed: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => PaymentScreen(
              trainer: widget.trainer ?? {},
              planType: _selectedPlan,
              amount: selectedPrice,
            ),
          ),
        );
      },
      icon: Icons.payment,
      hasGradient: true,
      height: 50,
      width: double.infinity,
    );
  }
}
