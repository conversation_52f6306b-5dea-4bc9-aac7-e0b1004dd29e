import 'package:flutter/material.dart';

abstract class AppLocalizations {
  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  // App Info
  String get appName;
  String get appSlogan;
  
  // Authentication
  String get welcome;
  String get login;
  String get register;
  String get email;
  String get password;
  String get confirmPassword;
  String get fullName;
  String get forgotPassword;
  String get dontHaveAccount;
  String get alreadyHaveAccount;
  String get createAccount;
  String get signInWithGoogle;
  String get or;
  String get signOut;
  
  // Navigation
  String get home;
  String get plans;
  String get sessions;
  String get profile;
  String get trainers;
  String get notifications;
  String get chat;
  String get settings;
  
  // Profile Setup
  String get profileSetup;
  String get tellUsAboutYou;
  String get basicInfo;
  String get age;
  String get gender;
  String get male;
  String get female;
  String get weight;
  String get height;
  String get fitnessGoal;
  String get healthConditions;
  String get dietaryPreferences;
  String get saveProfile;
  
  // Fitness Goals
  String get loseWeight;
  String get gainWeight;
  String get maintainWeight;
  String get buildMuscle;
  String get improveEndurance;
  String get generalFitness;
  
  // Home Screen
  String get quickStats;
  String get currentTrainer;
  String get noTrainerYet;
  String get chooseTrainer;
  String get nextSession;
  String get quickActions;
  String get nutritionPlans;
  String get workoutPlans;
  
  // Trainers
  String get selectTrainer;
  String get filterBySpecialization;
  String get allSpecializations;
  String get rating;
  String get experience;
  String get yearsExperience;
  String get pricePerSession;
  String get details;
  String get select;
  String get aboutTrainer;
  String get close;
  String get selectThisTrainer;
  
  // Plans
  String get myPlans;
  String get nutritionPlan;
  String get workoutPlan;
  String get noPlanYet;
  String get trainerWillSend;
  String get completed;
  String get markAsCompleted;
  String get meals;
  String get exercises;
  String get sets;
  String get reps;
  
  // Sessions
  String get mySessions;
  String get upcoming;
  String get past;
  String get noUpcomingSessions;
  String get noPastSessions;
  String get bookNewSession;
  String get scheduled;
  String get cancelled;
  String get rescheduleRequested;
  String get reschedule;
  String get withTrainer;
  
  // Notifications
  String get myNotifications;
  String get markAllAsRead;
  String get noNotifications;
  String get newNutritionPlan;
  String get newWorkoutPlan;
  String get sessionReminder;
  String get sessionBooked;
  
  // Chat
  String get messages;
  String get typeMessage;
  String get send;
  String get online;
  String get offline;
  
  // Common
  String get save;
  String get cancel;
  String get delete;
  String get edit;
  String get update;
  String get loading;
  String get error;
  String get success;
  String get retry;
  String get ok;
  String get yes;
  String get no;
  String get required;
  String get optional;
  String get kg;
  String get cm;
  String get years;
  String get minutes;
  String get hours;
  String get days;
  String get weeks;
  String get months;
  String get sar;
  
  // Validation Messages
  String get emailRequired;
  String get emailInvalid;
  String get passwordRequired;
  String get passwordTooShort;
  String get passwordsNotMatch;
  String get nameRequired;
  String get ageRequired;
  String get ageInvalid;
  String get weightRequired;
  String get weightInvalid;
  String get heightRequired;
  String get heightInvalid;
  
  // Success Messages
  String get profileUpdated;
  String get planCompleted;
  String get trainerSelected;
  String get sessionRescheduled;
  String get notificationsSent;
  
  // Error Messages
  String get unexpectedError;
  String get networkError;
  String get authError;
  String get permissionDenied;
  
  // Settings
  String get language;
  String get arabic;
  String get english;
  String get changeLanguage;
  String get privacySecurity;
  String get helpSupport;
  String get aboutApp;
  String get version;
  
  // BMI Categories
  String get underweight;
  String get normalWeight;
  String get overweight;
  String get obese;
  
  // Activity Levels
  String get sedentary;
  String get lightlyActive;
  String get moderatelyActive;
  String get veryActive;
  String get extremelyActive;
  
  // Time Ago
  String get now;
  String get minuteAgo;
  String get minutesAgo;
  String get hourAgo;
  String get hoursAgo;
  String get dayAgo;
  String get daysAgo;
  String get weekAgo;
  String get weeksAgo;
  String get monthAgo;
  String get monthsAgo;
  String get yearAgo;
  String get yearsAgo;
}
