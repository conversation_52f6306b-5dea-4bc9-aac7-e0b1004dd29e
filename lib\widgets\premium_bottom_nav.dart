import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'animated_widgets.dart';

class PremiumBottomNavBar extends StatefulWidget {
  final int currentIndex;
  final Function(int) onTap;
  final List<PremiumNavItem> items;

  const PremiumBottomNavBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
    required this.items,
  });

  @override
  State<PremiumBottomNavBar> createState() => _PremiumBottomNavBarState();
}

class _PremiumBottomNavBarState extends State<PremiumBottomNavBar>
    with TickerProviderStateMixin {
  late AnimationController _rippleController;
  late AnimationController _bounceController;

  @override
  void initState() {
    super.initState();
    _rippleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _rippleController.dispose();
    _bounceController.dispose();
    super.dispose();
  }

  void _onItemTapped(int index) {
    if (index != widget.currentIndex) {
      _rippleController.forward().then((_) {
        _rippleController.reset();
      });
      _bounceController.forward().then((_) {
        _bounceController.reverse();
      });
      widget.onTap(index);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 90,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(28),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryGold.withValues(alpha: 0.12),
            blurRadius: 30,
            offset: const Offset(0, 10),
            spreadRadius: 2,
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.15),
            blurRadius: 20,
            offset: const Offset(0, 5),
          ),
          BoxShadow(
            color: AppTheme.darkBackground.withValues(alpha: 0.8),
            blurRadius: 1,
            offset: const Offset(0, 0),
            spreadRadius: 1,
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(28),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                const Color(0xFF1A1A1A),
                const Color(0xFF2D2D2D),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            border: Border.all(
              color: AppTheme.primaryGold.withValues(alpha: 0.15),
              width: 1.5,
            ),
          ),
          child: Stack(
            children: [
              // Flowing background animation
              Positioned.fill(
                child: AnimatedBuilder(
                  animation: _rippleController,
                  builder: (context, child) {
                    return CustomPaint(
                      painter: FlowingBackgroundPainter(
                        animationValue: _rippleController.value,
                        selectedIndex: widget.currentIndex,
                        itemCount: widget.items.length,
                      ),
                    );
                  },
                ),
              ),
              // Glowing indicator line
              AnimatedPositioned(
                duration: const Duration(milliseconds: 400),
                curve: Curves.easeInOutCubic,
                left: (widget.currentIndex *
                        (MediaQuery.of(context).size.width - 32) /
                        widget.items.length) +
                    ((MediaQuery.of(context).size.width - 32) /
                                widget.items.length -
                            50) /
                        2,
                top: 6,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 400),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    gradient: AppTheme.primaryGradient,
                    borderRadius: BorderRadius.circular(3),
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme.primaryGold.withValues(alpha: 0.9),
                        blurRadius: 15,
                        spreadRadius: 3,
                      ),
                      BoxShadow(
                        color: AppTheme.primaryGold.withValues(alpha: 0.4),
                        blurRadius: 25,
                        spreadRadius: 6,
                      ),
                    ],
                  ),
                ),
              ),
              // Navigation items
              Padding(
                padding: const EdgeInsets.only(
                  top: 15,
                  bottom: 15,
                  left: 8,
                  right: 8,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: widget.items.asMap().entries.map((entry) {
                    int index = entry.key;
                    PremiumNavItem item = entry.value;
                    return _buildNavItem(index, item);
                  }).toList(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(int index, PremiumNavItem item) {
    final isSelected = widget.currentIndex == index;

    return GestureDetector(
      onTap: () => _onItemTapped(index),
      child: AnimatedBuilder(
        animation: _bounceController,
        builder: (context, child) {
          final scale = isSelected && _bounceController.isAnimating
              ? 1.0 + (_bounceController.value * 0.1)
              : 1.0;

          return Transform.scale(
            scale: scale,
            child: Container(
              width: 60,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Icon container with morphing animation
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOutCubic,
                    width: isSelected ? 50 : 42,
                    height: isSelected ? 50 : 42,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(isSelected ? 16 : 12),
                      gradient: isSelected
                          ? AppTheme.primaryGradient
                          : LinearGradient(
                              colors: [
                                AppTheme.surfaceLight.withValues(alpha: 0.15),
                                AppTheme.surfaceLight.withValues(alpha: 0.08),
                              ],
                            ),
                      boxShadow: isSelected
                          ? [
                              BoxShadow(
                                color:
                                    AppTheme.primaryGold.withValues(alpha: 0.4),
                                blurRadius: 20,
                                spreadRadius: 3,
                                offset: const Offset(0, 6),
                              ),
                              BoxShadow(
                                color:
                                    AppTheme.primaryGold.withValues(alpha: 0.2),
                                blurRadius: 30,
                                spreadRadius: 8,
                              ),
                            ]
                          : [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.03),
                                blurRadius: 6,
                                offset: const Offset(0, 2),
                              ),
                            ],
                      border: isSelected
                          ? null
                          : Border.all(
                              color: AppTheme.textMuted.withValues(alpha: 0.2),
                              width: 1,
                            ),
                    ),
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        // Pulsing glow effect
                        if (isSelected)
                          AnimatedBuilder(
                            animation: _rippleController,
                            builder: (context, child) {
                              return Container(
                                width: 35,
                                height: 35,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  gradient: RadialGradient(
                                    colors: [
                                      AppTheme.primaryGold.withValues(
                                          alpha: 0.3 *
                                              (1 - _rippleController.value)),
                                      AppTheme.primaryGold
                                          .withValues(alpha: 0.0),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                        // Icon with rotation animation
                        AnimatedRotation(
                          turns:
                              isSelected ? _bounceController.value * 0.05 : 0,
                          duration: const Duration(milliseconds: 200),
                          child: Icon(
                            item.icon,
                            size: isSelected ? 24 : 20,
                            color: isSelected
                                ? Colors.black
                                : AppTheme.textSecondary.withValues(alpha: 0.9),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 6),
                  // Label with color transition
                  AnimatedDefaultTextStyle(
                    duration: const Duration(milliseconds: 300),
                    style: TextStyle(
                      fontSize: isSelected ? 11 : 9.5,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.w500,
                      color: isSelected
                          ? AppTheme.primaryGold
                          : AppTheme.textSecondary.withValues(alpha: 0.8),
                      letterSpacing: 0.3,
                    ),
                    child: Text(
                      item.label,
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

class PremiumNavItem {
  final IconData icon;
  final String label;
  final Color? color;

  const PremiumNavItem({
    required this.icon,
    required this.label,
    this.color,
  });
}

class FlowingBackgroundPainter extends CustomPainter {
  final double animationValue;
  final int selectedIndex;
  final int itemCount;

  FlowingBackgroundPainter({
    required this.animationValue,
    required this.selectedIndex,
    required this.itemCount,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (animationValue == 0) return;

    final paint = Paint()
      ..color = AppTheme.primaryGold.withValues(alpha: 0.1 * animationValue)
      ..style = PaintingStyle.fill;

    final itemWidth = size.width / itemCount;
    final centerX = (selectedIndex * itemWidth) + (itemWidth / 2);

    // Create flowing ripple effect
    final radius = 60 * animationValue;

    canvas.drawCircle(
      Offset(centerX, size.height / 2),
      radius,
      paint,
    );

    // Secondary ripple
    final secondaryPaint = Paint()
      ..color = AppTheme.primaryGold.withValues(alpha: 0.05 * animationValue)
      ..style = PaintingStyle.fill;

    canvas.drawCircle(
      Offset(centerX, size.height / 2),
      radius * 1.5,
      secondaryPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
