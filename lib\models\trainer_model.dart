class TrainerModel {
  final String id;
  final String userId;
  final List<String> specialization;
  final String? bio;
  final int experienceYears;
  final List<String> certifications;
  final List<String> languages;
  final double rating;
  final int totalReviews;
  final double pricePerSession;
  final double pricePerMonth;
  final Map<String, dynamic> availability;
  final bool isVerified;
  final bool isAvailable;
  final DateTime createdAt;
  final DateTime updatedAt;

  TrainerModel({
    required this.id,
    required this.userId,
    required this.specialization,
    this.bio,
    required this.experienceYears,
    required this.certifications,
    required this.languages,
    required this.rating,
    required this.totalReviews,
    required this.pricePerSession,
    required this.pricePerMonth,
    required this.availability,
    required this.isVerified,
    required this.isAvailable,
    required this.createdAt,
    required this.updatedAt,
  });

  factory TrainerModel.fromJson(Map<String, dynamic> json) {
    return TrainerModel(
      id: json['id'],
      userId: json['user_id'],
      specialization: List<String>.from(json['specialization'] ?? []),
      bio: json['bio'],
      experienceYears: json['experience_years'] ?? 0,
      certifications: List<String>.from(json['certifications'] ?? []),
      languages: List<String>.from(json['languages'] ?? ['Arabic']),
      rating: (json['rating'] ?? 0.0).toDouble(),
      totalReviews: json['total_reviews'] ?? 0,
      pricePerSession: (json['price_per_session'] ?? 0.0).toDouble(),
      pricePerMonth: (json['price_per_month'] ?? 0.0).toDouble(),
      availability: Map<String, dynamic>.from(json['availability'] ?? {}),
      isVerified: json['is_verified'] ?? false,
      isAvailable: json['is_available'] ?? true,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'specialization': specialization,
      'bio': bio,
      'experience_years': experienceYears,
      'certifications': certifications,
      'languages': languages,
      'rating': rating,
      'total_reviews': totalReviews,
      'price_per_session': pricePerSession,
      'price_per_month': pricePerMonth,
      'availability': availability,
      'is_verified': isVerified,
      'is_available': isAvailable,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}
