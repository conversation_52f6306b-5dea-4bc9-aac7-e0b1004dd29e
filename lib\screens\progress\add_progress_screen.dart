import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../widgets/custom_app_bar.dart';

class AddProgressScreen extends StatefulWidget {
  final String traineeId;
  final VoidCallback? onProgressAdded;

  const AddProgressScreen({
    super.key,
    required this.traineeId,
    this.onProgressAdded,
  });

  @override
  State<AddProgressScreen> createState() => _AddProgressScreenState();
}

class _AddProgressScreenState extends State<AddProgressScreen> {
  final _formKey = GlobalKey<FormState>();
  final _weightController = TextEditingController();
  final _bodyFatController = TextEditingController();
  final _muscleMassController = TextEditingController();
  final _notesController = TextEditingController();

  final Map<String, TextEditingController> _measurementControllers = {
    'chest': TextEditingController(),
    'waist': TextEditingController(),
    'arms': TextEditingController(),
    'thighs': TextEditingController(),
    'hips': TextEditingController(),
    'neck': TextEditingController(),
  };

  bool _isLoading = false;

  @override
  void dispose() {
    _weightController.dispose();
    _bodyFatController.dispose();
    _muscleMassController.dispose();
    _notesController.dispose();
    _measurementControllers.values
        .forEach((controller) => controller.dispose());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: const CustomAppBar(
        title: 'إضافة تقدم جديد',
        showBackButton: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildBasicMeasurementsCard(),
                    const SizedBox(height: 16),
                    _buildBodyCompositionCard(),
                    const SizedBox(height: 16),
                    _buildBodyMeasurementsCard(),
                    const SizedBox(height: 16),
                    _buildNotesCard(),
                    const SizedBox(height: 24),
                    _buildSaveButton(),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildBasicMeasurementsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'القياسات الأساسية',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _weightController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'الوزن (كجم)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.monitor_weight),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال الوزن';
                }
                if (double.tryParse(value) == null) {
                  return 'يرجى إدخال رقم صحيح';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBodyCompositionCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تركيب الجسم',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _bodyFatController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'نسبة الدهون (%)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.pie_chart),
                    ),
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        final parsed = double.tryParse(value);
                        if (parsed == null || parsed < 0 || parsed > 100) {
                          return 'نسبة غير صحيحة';
                        }
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextFormField(
                    controller: _muscleMassController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'الكتلة العضلية (كجم)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.fitness_center),
                    ),
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        if (double.tryParse(value) == null) {
                          return 'رقم غير صحيح';
                        }
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBodyMeasurementsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'قياسات الجسم (سم)',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildMeasurementRow('الصدر', 'chest', 'الخصر', 'waist'),
            const SizedBox(height: 12),
            _buildMeasurementRow('الذراعين', 'arms', 'الفخذين', 'thighs'),
            const SizedBox(height: 12),
            _buildMeasurementRow('الوركين', 'hips', 'الرقبة', 'neck'),
          ],
        ),
      ),
    );
  }

  Widget _buildMeasurementRow(
      String label1, String key1, String label2, String key2) {
    return Row(
      children: [
        Expanded(
          child: TextFormField(
            controller: _measurementControllers[key1],
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              labelText: label1,
              border: const OutlineInputBorder(),
              prefixIcon: const Icon(Icons.straighten),
            ),
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                if (double.tryParse(value) == null) {
                  return 'رقم غير صحيح';
                }
              }
              return null;
            },
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: TextFormField(
            controller: _measurementControllers[key2],
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              labelText: label2,
              border: const OutlineInputBorder(),
              prefixIcon: const Icon(Icons.straighten),
            ),
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                if (double.tryParse(value) == null) {
                  return 'رقم غير صحيح';
                }
              }
              return null;
            },
          ),
        ),
      ],
    );
  }

  Widget _buildNotesCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملاحظات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              maxLines: 3,
              decoration: const InputDecoration(
                labelText: 'ملاحظات إضافية (اختياري)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.note),
                hintText: 'مثال: تحسن في الأداء، تغيير في النظام الغذائي...',
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _saveProgress,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: const Text(
          'حفظ التقدم',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Future<void> _saveProgress() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isLoading = true);

    try {
      // إنشاء كائن التقدم (يتم استخدامه في الحفظ لاحقاً)
      final measurements = <String, dynamic>{};
      _measurementControllers.forEach((key, controller) {
        if (controller.text.isNotEmpty) {
          final value = double.tryParse(controller.text);
          if (value != null) {
            measurements[key] = value;
          }
        }
      });
      // إنشاء البيانات للحفظ (بدون id لأن قاعدة البيانات ستنشئه تلقائياً)
      final progressData = {
        'trainee_id': widget.traineeId,
        'weight': double.tryParse(_weightController.text),
        'body_fat_percentage': double.tryParse(_bodyFatController.text),
        'muscle_mass': double.tryParse(_muscleMassController.text),
        'measurements': _measurementControllers.map(
          (key, controller) => MapEntry(
            key,
            double.tryParse(controller.text) ?? 0.0,
          ),
        ),
        'notes':
            _notesController.text.isNotEmpty ? _notesController.text : null,
        'recorded_at': DateTime.now().toIso8601String(),
      };

      // حفظ في قاعدة البيانات
      await Supabase.instance.client
          .from('progress_tracking')
          .insert(progressData);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ التقدم بنجاح'),
            backgroundColor: Colors.green,
          ),
        );

        // استدعاء callback لتحديث الشاشة الرئيسية
        widget.onProgressAdded?.call();

        // العودة للشاشة السابقة
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ التقدم: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
