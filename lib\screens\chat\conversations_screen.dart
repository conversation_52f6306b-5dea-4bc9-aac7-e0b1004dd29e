import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/chat_message_model.dart';
import '../../widgets/custom_app_bar.dart';
import 'chat_screen.dart';

class ConversationsScreen extends StatefulWidget {
  final String currentUserId;

  const ConversationsScreen({
    super.key,
    required this.currentUserId,
  });

  @override
  State<ConversationsScreen> createState() => _ConversationsScreenState();
}

class _ConversationsScreenState extends State<ConversationsScreen> {
  List<ConversationItem> _conversations = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadConversations();
  }

  Future<void> _loadConversations() async {
    setState(() => _isLoading = true);

    try {
      // تحميل المحادثات من Supabase
      // نحتاج لتحميل آخر رسالة لكل محادثة
      final response = await Supabase.instance.client
          .from('chat_messages')
          .select(
              'conversation_id, sender_id, receiver_id, message, created_at')
          .or('sender_id.eq.${widget.currentUserId},receiver_id.eq.${widget.currentUserId}')
          .order('created_at', ascending: false);

      // تجميع الرسائل حسب المحادثة
      final Map<String, ChatMessage> lastMessages = {};
      final Set<String> conversationIds = {};

      for (final data in response as List) {
        final message = ChatMessage.fromJson(data);
        final conversationId =
            _generateConversationId(message.senderId, message.receiverId);

        if (!conversationIds.contains(conversationId)) {
          conversationIds.add(conversationId);
          lastMessages[conversationId] = message;
        }
      }

      // تحويل إلى قائمة محادثات
      _conversations = lastMessages.entries.map((entry) {
        final message = entry.value;
        final otherUserId = message.senderId == widget.currentUserId
            ? message.receiverId
            : message.senderId;

        return ConversationItem(
          id: entry.key,
          otherUserId: otherUserId,
          otherUserName: 'المستخدم $otherUserId', // TODO: جلب الاسم الحقيقي
          lastMessage: message.message,
          lastMessageTime: message.createdAt,
          unreadCount: 0, // TODO: حساب الرسائل غير المقروءة
          isOnline: false, // TODO: تحديد حالة الاتصال
          otherUserType: 'trainer', // TODO: تحديد نوع المستخدم
        );
      }).toList();

      // إذا لم توجد محادثات، استخدم بيانات تجريبية
      if (_conversations.isEmpty) {
        _conversations = _generateSampleConversations();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل المحادثات: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  String _generateConversationId(String userId1, String userId2) {
    // ترتيب المعرفات لضمان نفس معرف المحادثة بغض النظر عن الترتيب
    final sortedIds = [userId1, userId2]..sort();
    return '${sortedIds[0]}_${sortedIds[1]}';
  }

  List<ConversationItem> _generateSampleConversations() {
    final now = DateTime.now();
    return [
      ConversationItem(
        id: 'conv_1',
        otherUserId: 'trainer_1',
        otherUserName: 'أحمد المدرب',
        otherUserType: 'مدرب',
        lastMessage: 'ممتاز! سأعد لك برنامج مخصص يناسب أهدافك',
        lastMessageTime: now.subtract(const Duration(minutes: 30)),
        unreadCount: 2,
        isOnline: true,
      ),
      ConversationItem(
        id: 'conv_2',
        otherUserId: 'trainee_1',
        otherUserName: 'فاطمة علي',
        otherUserType: 'متدربة',
        lastMessage: 'شكراً لك على البرنامج الرائع',
        lastMessageTime: now.subtract(const Duration(hours: 2)),
        unreadCount: 0,
        isOnline: false,
      ),
      ConversationItem(
        id: 'conv_3',
        otherUserId: 'trainer_2',
        otherUserName: 'محمد الخبير',
        otherUserType: 'مدرب',
        lastMessage: 'متى يمكنك البدء في الجلسة القادمة؟',
        lastMessageTime: now.subtract(const Duration(hours: 5)),
        unreadCount: 1,
        isOnline: true,
      ),
      ConversationItem(
        id: 'conv_4',
        otherUserId: 'trainee_2',
        otherUserName: 'خالد أحمد',
        otherUserType: 'متدرب',
        lastMessage: 'هل يمكن تعديل موعد الجلسة؟',
        lastMessageTime: now.subtract(const Duration(days: 1)),
        unreadCount: 0,
        isOnline: false,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: const CustomAppBar(
        title: 'المحادثات',
        showBackButton: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _conversations.isEmpty
              ? _buildEmptyState()
              : _buildConversationsList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 64,
            color: Colors.grey.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          const Text(
            'لا توجد محادثات بعد',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          const Text(
            'ستظهر محادثاتك هنا عندما تبدأ في التواصل',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildConversationsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _conversations.length,
      itemBuilder: (context, index) {
        final conversation = _conversations[index];
        return _buildConversationCard(conversation);
      },
    );
  }

  Widget _buildConversationCard(ConversationItem conversation) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        elevation: 2,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => _openChat(conversation),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Stack(
                  children: [
                    CircleAvatar(
                      radius: 25,
                      backgroundColor: Colors.blue.withValues(alpha: 0.1),
                      child: const Icon(Icons.person, color: Colors.blue),
                    ),
                    if (conversation.isOnline)
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: Colors.green,
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.white, width: 2),
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              conversation.otherUserName,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          Text(
                            _formatTime(conversation.lastMessageTime),
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.blue.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              conversation.otherUserType,
                              style: TextStyle(
                                fontSize: 10,
                                color: Colors.blue[700],
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          if (conversation.unreadCount > 0)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: const BoxDecoration(
                                color: Colors.red,
                                shape: BoxShape.circle,
                              ),
                              child: Text(
                                '${conversation.unreadCount}',
                                style: const TextStyle(
                                  fontSize: 10,
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        conversation.lastMessage,
                        style: TextStyle(
                          fontSize: 14,
                          color: conversation.unreadCount > 0
                              ? Colors.black87
                              : Colors.grey,
                          fontWeight: conversation.unreadCount > 0
                              ? FontWeight.w500
                              : FontWeight.normal,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                const Icon(
                  Icons.chevron_right,
                  color: Colors.grey,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _openChat(ConversationItem conversation) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatScreen(
          conversationId: conversation.id,
          otherUserId: conversation.otherUserId,
          otherUserName: conversation.otherUserName,
          currentUserId: widget.currentUserId,
        ),
      ),
    ).then((_) {
      // تحديث المحادثات عند العودة
      _loadConversations();
    });
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else {
      return '${time.day}/${time.month}';
    }
  }
}

class ConversationItem {
  final String id;
  final String otherUserId;
  final String otherUserName;
  final String otherUserType;
  final String lastMessage;
  final DateTime lastMessageTime;
  final int unreadCount;
  final bool isOnline;

  ConversationItem({
    required this.id,
    required this.otherUserId,
    required this.otherUserName,
    required this.otherUserType,
    required this.lastMessage,
    required this.lastMessageTime,
    required this.unreadCount,
    required this.isOnline,
  });
}
