import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../theme/app_theme.dart';
import '../../main.dart';

class PrivacyScreen extends StatefulWidget {
  const PrivacyScreen({super.key});

  @override
  State<PrivacyScreen> createState() => _PrivacyScreenState();
}

class _PrivacyScreenState extends State<PrivacyScreen> {
  bool _isLoading = false;

  Future<void> _changePassword() async {
    final controller = TextEditingController();
    final formKey = GlobalKey<FormState>();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تغيير كلمة المرور'),
        content: Form(
          key: formKey,
          child: TextFormField(
            controller: controller,
            obscureText: true,
            decoration: const InputDecoration(labelText: 'كلمة المرور الجديدة'),
            validator: (v) =>
                v == null || v.length < 6 ? 'كلمة المرور قصيرة' : null,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (!formKey.currentState!.validate()) return;
              Navigator.pop(context);
              setState(() => _isLoading = true);
              try {
                await supabase.auth.updateUser(
                  UserAttributes(password: controller.text),
                );
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
                      content: Text('تم تغيير كلمة المرور بنجاح'),
                      backgroundColor: AppTheme.primaryGold));
                }
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                    content: Text('خطأ: $e'), backgroundColor: Colors.red));
              }
              setState(() => _isLoading = false);
            },
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }

  Future<void> _deactivateAccount() async {
    setState(() => _isLoading = true);
    try {
      final user = supabase.auth.currentUser;
      if (user != null) {
        await supabase
            .from('users')
            .update({'is_active': false}).eq('id', user.id);
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
            content: Text('تم إخفاء الحساب بنجاح'),
            backgroundColor: AppTheme.primaryGold));
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ: $e'), backgroundColor: Colors.red));
    }
    setState(() => _isLoading = false);
  }

  Future<void> _deleteAccount() async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد حذف الحساب'),
        content: const Text(
            'هل أنت متأكد أنك تريد حذف حسابك نهائياً؟ لا يمكن التراجع.'),
        actions: [
          TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('إلغاء')),
          ElevatedButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text('حذف', style: TextStyle(color: Colors.white)),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red)),
        ],
      ),
    );
    if (confirm != true) return;
    setState(() => _isLoading = true);
    try {
      final user = supabase.auth.currentUser;
      if (user != null) {
        await supabase.from('users').delete().eq('id', user.id);
        await supabase.auth.signOut();
        if (mounted) {
          Navigator.of(context).popUntil((route) => route.isFirst);
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ: $e'), backgroundColor: Colors.red));
    }
    setState(() => _isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الخصوصية والأمان'),
      ),
      body: Stack(
        children: [
          Container(
            width: double.infinity,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [AppTheme.darkBackground, Color(0xFF1A1A1A)],
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 24),
                  const Text('إعدادات الخصوصية',
                      style: TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryGold)),
                  const SizedBox(height: 16),
                  const Text(
                      'يمكنك هنا التحكم في إعدادات الخصوصية والأمان الخاصة بحسابك.',
                      style: TextStyle(
                          fontSize: 16, color: AppTheme.textSecondary)),
                  const SizedBox(height: 32),
                  ListTile(
                    leading:
                        const Icon(Icons.lock, color: AppTheme.primaryGold),
                    title: const Text('تغيير كلمة المرور',
                        style: TextStyle(color: AppTheme.textPrimary)),
                    trailing: const Icon(Icons.arrow_forward_ios,
                        size: 16, color: AppTheme.textSecondary),
                    onTap: _changePassword,
                  ),
                  const Divider(),
                  ListTile(
                    leading: const Icon(Icons.visibility_off,
                        color: AppTheme.primaryGold),
                    title: const Text('إخفاء الحساب',
                        style: TextStyle(color: AppTheme.textPrimary)),
                    trailing: const Icon(Icons.arrow_forward_ios,
                        size: 16, color: AppTheme.textSecondary),
                    onTap: _deactivateAccount,
                  ),
                  const Divider(),
                  ListTile(
                    leading:
                        const Icon(Icons.delete_forever, color: Colors.red),
                    title: const Text('حذف الحساب',
                        style: TextStyle(color: Colors.red)),
                    trailing: const Icon(Icons.arrow_forward_ios,
                        size: 16, color: AppTheme.textSecondary),
                    onTap: _deleteAccount,
                  ),
                ],
              ),
            ),
          ),
          if (_isLoading)
            Container(
              color: Colors.black.withOpacity(0.3),
              child: const Center(
                child: CircularProgressIndicator(color: AppTheme.primaryGold),
              ),
            ),
        ],
      ),
    );
  }
}
