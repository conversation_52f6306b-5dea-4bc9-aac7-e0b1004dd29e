import 'package:flutter/foundation.dart';
import '../main.dart';

class SubscriptionService {
  static Future<Map<String, dynamic>?> getCurrentSubscription(String traineeId) async {
    try {
      final response = await supabase
          .from('subscriptions')
          .select('*, trainer:trainers(*, user:users(full_name))')
          .eq('trainee_id', traineeId)
          .eq('status', 'active')
          .gte('end_date', DateTime.now().toIso8601String().split('T')[0])
          .maybeSingle();

      return response;
    } catch (error) {
      if (kDebugMode) {
        print('Error fetching current subscription: $error');
      }
      return null;
    }
  }

  static Future<bool> createSubscription({
    required String traineeId,
    required String trainerId,
    required String planType,
    required double price,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      await supabase.from('subscriptions').insert({
        'trainee_id': traineeId,
        'trainer_id': trainerId,
        'plan_type': planType,
        'price': price,
        'start_date': startDate.toIso8601String().split('T')[0],
        'end_date': endDate.toIso8601String().split('T')[0],
        'status': 'pending',
        'created_at': DateTime.now().toIso8601String(),
      });

      return true;
    } catch (error) {
      if (kDebugMode) {
        print('Error creating subscription: $error');
      }
      return false;
    }
  }

  static Future<bool> cancelSubscription(String subscriptionId) async {
    try {
      await supabase
          .from('subscriptions')
          .update({
            'status': 'cancelled',
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', subscriptionId);

      return true;
    } catch (error) {
      if (kDebugMode) {
        print('Error cancelling subscription: $error');
      }
      return false;
    }
  }

  static Future<List<Map<String, dynamic>>> getSubscriptionHistory(String traineeId) async {
    try {
      final response = await supabase
          .from('subscriptions')
          .select('*, trainer:trainers(*, user:users(full_name))')
          .eq('trainee_id', traineeId)
          .order('created_at', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (error) {
      if (kDebugMode) {
        print('Error fetching subscription history: $error');
      }
      return [];
    }
  }

  static Future<bool> checkSubscriptionStatus(String traineeId) async {
    try {
      final response = await supabase.rpc('check_subscription_status', params: {
        'trainee_uuid': traineeId,
      });

      return response as bool;
    } catch (error) {
      if (kDebugMode) {
        print('Error checking subscription status: $error');
      }
      return false;
    }
  }
}
