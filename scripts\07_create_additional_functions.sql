-- Additional database functions for advanced features

-- Function to get user conversations
CREATE OR <PERSON><PERSON>LACE FUNCTION get_user_conversations(user_uuid UUID)
RETURNS TABLE (
    other_user_id UUID,
    other_user_name TEXT,
    other_user_avatar TEXT,
    last_message TEXT,
    last_message_time TIMESTAMP WITH TIME ZONE,
    unread_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    WITH conversation_users AS (
        SELECT DISTINCT
            CASE 
                WHEN sender_id = user_uuid THEN receiver_id
                ELSE sender_id
            END as other_id
        FROM public.chat_messages
        WHERE sender_id = user_uuid OR receiver_id = user_uuid
    ),
    latest_messages AS (
        SELECT DISTINCT ON (
            CASE 
                WHEN sender_id = user_uuid THEN receiver_id
                ELSE sender_id
            END
        )
            CASE 
                WHEN sender_id = user_uuid THEN receiver_id
                ELSE sender_id
            END as other_id,
            message,
            created_at
        FROM public.chat_messages
        WHERE sender_id = user_uuid OR receiver_id = user_uuid
        ORDER BY 
            CASE 
                WHEN sender_id = user_uuid THEN receiver_id
                ELSE sender_id
            END,
            created_at DESC
    ),
    unread_counts AS (
        SELECT 
            sender_id as other_id,
            COUNT(*) as unread_count
        FROM public.chat_messages
        WHERE receiver_id = user_uuid AND is_read = false
        GROUP BY sender_id
    )
    SELECT 
        cu.other_id,
        u.full_name,
        u.avatar_url,
        lm.message,
        lm.created_at,
        COALESCE(uc.unread_count, 0)
    FROM conversation_users cu
    JOIN public.users u ON u.id = cu.other_id
    LEFT JOIN latest_messages lm ON lm.other_id = cu.other_id
    LEFT JOIN unread_counts uc ON uc.other_id = cu.other_id
    ORDER BY lm.created_at DESC NULLS LAST;
END;
$$ LANGUAGE plpgsql;

-- Function to get trainer statistics
CREATE OR REPLACE FUNCTION get_trainer_statistics(trainer_uuid UUID)
RETURNS TABLE (
    total_trainees BIGINT,
    active_trainees BIGINT,
    total_sessions BIGINT,
    completed_sessions BIGINT,
    average_rating DECIMAL(3,2),
    total_reviews BIGINT,
    monthly_revenue DECIMAL(10,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (SELECT COUNT(DISTINCT trainee_id) 
         FROM public.trainer_assignments 
         WHERE trainer_id = trainer_uuid) as total_trainees,
        
        (SELECT COUNT(DISTINCT trainee_id) 
         FROM public.trainer_assignments 
         WHERE trainer_id = trainer_uuid AND status = 'active') as active_trainees,
        
        (SELECT COUNT(*) 
         FROM public.sessions 
         WHERE trainer_id = trainer_uuid) as total_sessions,
        
        (SELECT COUNT(*) 
         FROM public.sessions 
         WHERE trainer_id = trainer_uuid AND status = 'completed') as completed_sessions,
        
        (SELECT AVG(rating) 
         FROM public.reviews 
         WHERE trainer_id = trainer_uuid) as average_rating,
        
        (SELECT COUNT(*) 
         FROM public.reviews 
         WHERE trainer_id = trainer_uuid) as total_reviews,
        
        (SELECT COALESCE(SUM(amount), 0) 
         FROM public.payments p
         JOIN public.subscriptions s ON s.id = p.subscription_id
         WHERE s.trainer_id = trainer_uuid 
         AND p.status = 'completed'
         AND p.paid_at >= DATE_TRUNC('month', CURRENT_DATE)) as monthly_revenue;
END;
$$ LANGUAGE plpgsql;

-- Function to get trainee progress summary
CREATE OR REPLACE FUNCTION get_trainee_progress_summary(trainee_uuid UUID)
RETURNS TABLE (
    current_weight DECIMAL(5,2),
    weight_change DECIMAL(5,2),
    total_sessions BIGINT,
    completed_plans BIGINT,
    days_active BIGINT,
    latest_bmi DECIMAL(4,2)
) AS $$
DECLARE
    profile_record RECORD;
    latest_progress RECORD;
    initial_weight DECIMAL(5,2);
BEGIN
    -- Get trainee profile
    SELECT * INTO profile_record
    FROM public.trainees_profiles
    WHERE user_id = trainee_uuid;
    
    -- Get latest progress entry
    SELECT * INTO latest_progress
    FROM public.progress_tracking
    WHERE trainee_id = trainee_uuid
    ORDER BY recorded_at DESC
    LIMIT 1;
    
    -- Get initial weight
    initial_weight := profile_record.weight;
    
    RETURN QUERY
    SELECT 
        COALESCE(latest_progress.weight, profile_record.weight) as current_weight,
        COALESCE(latest_progress.weight, profile_record.weight) - initial_weight as weight_change,
        
        (SELECT COUNT(*) 
         FROM public.sessions 
         WHERE trainee_id = trainee_uuid AND status = 'completed') as total_sessions,
        
        (SELECT COUNT(*) 
         FROM public.nutrition_plans 
         WHERE trainee_id = trainee_uuid AND completed = true) +
        (SELECT COUNT(*) 
         FROM public.workout_plans 
         WHERE trainee_id = trainee_uuid AND completed = true) as completed_plans,
        
        (SELECT COUNT(DISTINCT DATE(recorded_at)) 
         FROM public.progress_tracking 
         WHERE trainee_id = trainee_uuid) as days_active,
        
        calculate_bmi(
            COALESCE(latest_progress.weight, profile_record.weight),
            profile_record.height
        ) as latest_bmi;
END;
$$ LANGUAGE plpgsql;

-- Function to check trainer availability for booking
CREATE OR REPLACE FUNCTION check_trainer_availability(
    trainer_uuid UUID,
    session_date TIMESTAMP WITH TIME ZONE,
    duration_minutes INTEGER DEFAULT 60
)
RETURNS BOOLEAN AS $$
DECLARE
    session_end TIMESTAMP WITH TIME ZONE;
    conflicting_sessions INTEGER;
BEGIN
    session_end := session_date + (duration_minutes || ' minutes')::INTERVAL;
    
    -- Check for conflicting sessions
    SELECT COUNT(*) INTO conflicting_sessions
    FROM public.sessions
    WHERE trainer_id = trainer_uuid
    AND status IN ('scheduled', 'completed')
    AND (
        (scheduled_at <= session_date AND scheduled_at + (duration_minutes || ' minutes')::INTERVAL > session_date)
        OR
        (scheduled_at < session_end AND scheduled_at >= session_date)
    );
    
    RETURN conflicting_sessions = 0;
END;
$$ LANGUAGE plpgsql;

-- Function to auto-expire subscriptions
CREATE OR REPLACE FUNCTION auto_expire_subscriptions()
RETURNS INTEGER AS $$
DECLARE
    expired_count INTEGER;
BEGIN
    UPDATE public.subscriptions
    SET status = 'expired',
        updated_at = NOW()
    WHERE status = 'active'
    AND end_date < CURRENT_DATE;
    
    GET DIAGNOSTICS expired_count = ROW_COUNT;
    
    RETURN expired_count;
END;
$$ LANGUAGE plpgsql;

-- Function to send session reminders
CREATE OR REPLACE FUNCTION send_session_reminders()
RETURNS INTEGER AS $$
DECLARE
    reminder_count INTEGER := 0;
    session_record RECORD;
BEGIN
    FOR session_record IN
        SELECT s.*, u.full_name as trainee_name
        FROM public.sessions s
        JOIN public.users u ON u.id = s.trainee_id
        WHERE s.status = 'scheduled'
        AND s.scheduled_at BETWEEN NOW() AND NOW() + INTERVAL '2 hours'
        AND NOT EXISTS (
            SELECT 1 FROM public.notifications n
            WHERE n.user_id = s.trainee_id
            AND n.type = 'session_reminder'
            AND n.data->>'session_id' = s.id::text
            AND n.created_at > NOW() - INTERVAL '1 day'
        )
    LOOP
        -- Send reminder to trainee
        PERFORM create_notification(
            session_record.trainee_id,
            'تذكير بالجلسة',
            'لديك جلسة تدريبية خلال ساعتين',
            'session_reminder',
            jsonb_build_object('session_id', session_record.id)
        );
        
        -- Send reminder to trainer
        PERFORM create_notification(
            (SELECT user_id FROM public.trainers WHERE id = session_record.trainer_id),
            'تذكير بالجلسة',
            'لديك جلسة تدريبية خلال ساعتين مع ' || session_record.trainee_name,
            'session_reminder',
            jsonb_build_object('session_id', session_record.id)
        );
        
        reminder_count := reminder_count + 1;
    END LOOP;
    
    RETURN reminder_count;
END;
$$ LANGUAGE plpgsql;
