import 'package:flutter/material.dart';
import '../../main.dart';
import '../../services/notification_service.dart';
import '../../theme/app_theme.dart';
import '../../widgets/custom_app_bar.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  late Future<List<Map<String, dynamic>>> _notificationsFuture;

  @override
  void initState() {
    super.initState();
    _loadNotifications();
  }

  void _loadNotifications() {
    final userId = supabase.auth.currentUser!.id;
    _notificationsFuture = NotificationService.getNotifications(userId: userId);
  }

  Future<void> _markAllAsRead() async {
    final userId = supabase.auth.currentUser!.id;
    await NotificationService.markAllAsRead(userId);
    setState(() {
      _loadNotifications();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'الإشعارات',
        actions: [
          IconButton(
            onPressed: _markAllAsRead,
            icon: const Icon(Icons.done_all, color: AppTheme.primaryGold),
          ),
        ],
      ),
      body: FutureBuilder<List<Map<String, dynamic>>>(
        future: _notificationsFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(
              child: CircularProgressIndicator(color: AppTheme.primaryGold),
            );
          }
          if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          }
          final notifications = snapshot.data!;
          if (notifications.isEmpty) {
            return const Center(
              child: Text(
                'لا توجد إشعارات',
                style: TextStyle(color: AppTheme.textSecondary, fontSize: 18),
              ),
            );
          }
          return ListView.builder(
            itemCount: notifications.length,
            itemBuilder: (context, index) {
              final notification = notifications[index];
              return Card(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                  side: BorderSide(
                    color: AppTheme.primaryGold.withOpacity(0.3),
                  ),
                ),
                child: ListTile(
                  leading: Icon(
                    _getNotificationIcon(notification['type']),
                    color: AppTheme.primaryGold,
                  ),
                  title: Text(
                    notification['title'],
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary,
                    ),
                  ),
                  subtitle: Text(
                    notification['message'],
                    style: const TextStyle(color: AppTheme.textSecondary),
                  ),
                  trailing: notification['is_read']
                      ? null
                      : Container(
                          width: 10,
                          height: 10,
                          decoration: const BoxDecoration(
                            color: AppTheme.primaryGold,
                            shape: BoxShape.circle,
                          ),
                        ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  IconData _getNotificationIcon(String type) {
    switch (type) {
      case 'plan_assigned':
        return Icons.assignment;
      case 'session_reminder':
        return Icons.alarm;
      case 'session_booked':
        return Icons.event;
      case 'payment_due':
        return Icons.payment;
      default:
        return Icons.notifications;
    }
  }
}
