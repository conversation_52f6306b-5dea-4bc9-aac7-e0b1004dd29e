import 'package:flutter/material.dart';
import 'dart:async';
import '../main.dart';
import 'auth/login_screen.dart';
import 'home/home_screen.dart';
import '../theme/app_theme.dart';
import '../l10n/app_localizations.dart';
import '../services/localization_service.dart';
import '../widgets/animated_widgets.dart';

class SplashScreen extends StatefulWidget {
  final Function(String) onLanguageChanged;

  const SplashScreen({super.key, required this.onLanguageChanged});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 10.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    await Future.delayed(const Duration(seconds: 4));

    final session = supabase.auth.currentSession;
    if (mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => session != null
              ? HomeScreen(onLanguageChanged: widget.onLanguageChanged)
              : LoginScreen(onLanguageChanged: widget.onLanguageChanged),
        ),
      );
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: Stack(
          children: [
            // Animated background
            WaveBackground(
              child: Container(),
              color: AppTheme.primaryGold,
              height: 300,
            ),
            // Floating particles
            ...List.generate(6, (index) {
              return Positioned(
                left: (index * 60.0) + 20,
                top: 100.0 + (index * 90.0),
                child: FloatingWidget(
                  duration: Duration(milliseconds: 2000 + (index * 300)),
                  offsetY: 15.0 + (index * 5.0),
                  child: Container(
                    width: 8 + (index * 2.0),
                    height: 8 + (index * 2.0),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: AppTheme.primaryGold
                          .withValues(alpha: 0.3 - (index * 0.05)),
                    ),
                  ),
                ),
              );
            }),
            // Main content
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Logo with premium animations
                  ScaleInAnimation(
                    duration: const Duration(milliseconds: 800),
                    curve: Curves.elasticOut,
                    child: FloatingWidget(
                      child: Container(
                        width: 140,
                        height: 140,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: AppTheme.primaryGradient,
                          boxShadow: [
                            ...AppTheme.primaryShadow,
                            BoxShadow(
                              color:
                                  AppTheme.primaryGold.withValues(alpha: 0.4),
                              blurRadius: 40,
                              spreadRadius: 10,
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.fitness_center,
                          size: 70,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 40),
                  // App name with slide animation
                  SlideInAnimation(
                    delay: 400,
                    begin: const Offset(0.0, 1.0),
                    child: ShaderMask(
                      shaderCallback: (bounds) =>
                          AppTheme.primaryGradient.createShader(bounds),
                      child: Text(
                        'BALANCE',
                        style:
                            Theme.of(context).textTheme.headlineLarge?.copyWith(
                                  fontSize: 36,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                  letterSpacing: 2.0,
                                ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  // Subtitle with fade animation
                  SlideInAnimation(
                    delay: 600,
                    begin: const Offset(0.0, 0.5),
                    child: Text(
                      'Balance your life, balance your health',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: AppTheme.textSecondary,
                            fontSize: 16,
                            letterSpacing: 1.0,
                          ),
                    ),
                  ),
                  const SizedBox(height: 60),
                  // Loading indicator
                  SlideInAnimation(
                    delay: 800,
                    child: Column(
                      children: [
                        RotatingWidget(
                          duration: const Duration(seconds: 2),
                          child: Container(
                            width: 50,
                            height: 50,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(25),
                              gradient: AppTheme.primaryGradient,
                            ),
                            child: const Icon(
                              Icons.refresh,
                              color: Colors.black,
                              size: 24,
                            ),
                          ),
                        ),
                        const SizedBox(height: 20),
                        Text(
                          l10n?.loading ?? 'جاري التحميل...',
                          style: const TextStyle(
                            color: AppTheme.textSecondary,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 100),
                  // Progress dots
                  SlideInAnimation(
                    delay: 1000,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(3, (index) {
                        return Container(
                          margin: const EdgeInsets.symmetric(horizontal: 4),
                          child: ScaleInAnimation(
                            delay: 1200 + (index * 100),
                            child: Container(
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: AppTheme.primaryGradient,
                              ),
                            ),
                          ),
                        );
                      }),
                    ),
                  ),
                ],
              ),
            ),
            // Language selector
            Positioned(
              top: 50,
              right: 20,
              child: SlideInAnimation(
                delay: 1500,
                begin: const Offset(1.0, 0.0),
                child: Container(
                  decoration: BoxDecoration(
                    color: AppTheme.surfaceLight.withValues(alpha: 0.9),
                    borderRadius: BorderRadius.circular(25),
                    boxShadow: AppTheme.primaryShadow,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _buildLanguageButton('ar', 'ع'),
                      _buildLanguageButton('en', 'EN'),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageButton(String languageCode, String label) {
    final isSelected = LocalizationService.currentLanguage == languageCode;

    return GestureDetector(
      onTap: () {
        if (!isSelected) {
          widget.onLanguageChanged(languageCode);
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.primaryGold : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: AppTheme.primaryGold.withOpacity(0.3),
                    blurRadius: 5,
                    spreadRadius: 1,
                  ),
                ]
              : [],
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.black : AppTheme.textPrimary,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            fontSize: 14,
          ),
        ),
      ),
    );
  }
}
