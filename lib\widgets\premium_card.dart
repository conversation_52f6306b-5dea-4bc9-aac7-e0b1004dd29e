import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

class PremiumCard extends StatefulWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? width;
  final double? height;
  final VoidCallback? onTap;
  final bool isSelected;
  final bool hasGradient;
  final List<Color>? gradientColors;
  final bool hasBorder;
  final Color? borderColor;
  final double borderWidth;
  final bool isElevated;

  const PremiumCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.onTap,
    this.isSelected = false,
    this.hasGradient = false,
    this.gradientColors,
    this.hasBorder = false,
    this.borderColor,
    this.borderWidth = 1.5,
    this.isElevated = true,
  });

  @override
  State<PremiumCard> createState() => _PremiumCardState();
}

class _PremiumCardState extends State<PremiumCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _shadowAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppTheme.fastAnimation,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _shadowAnimation = Tween<double>(
      begin: 1.0,
      end: 0.5,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    _animationController.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    _animationController.reverse();
  }

  void _handleTapCancel() {
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final effectivePadding = widget.padding ?? const EdgeInsets.all(20);
    final effectiveMargin = widget.margin ?? const EdgeInsets.all(8);

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Container(
          margin: effectiveMargin,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: GestureDetector(
              onTapDown: widget.onTap != null ? _handleTapDown : null,
              onTapUp: widget.onTap != null ? _handleTapUp : null,
              onTapCancel: widget.onTap != null ? _handleTapCancel : null,
              onTap: widget.onTap,
              child: AnimatedContainer(
                duration: AppTheme.normalAnimation,
                width: widget.width,
                height: widget.height,
                decoration: BoxDecoration(
                  gradient: widget.hasGradient
                      ? LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: widget.gradientColors ??
                              [
                                AppTheme.primaryGoldLight,
                                AppTheme.primaryGold,
                                AppTheme.primaryGoldDark,
                              ],
                        )
                      : null,
                  color: widget.hasGradient ? null : AppTheme.surfaceLight,
                  borderRadius: BorderRadius.circular(24),
                  border: widget.hasBorder || widget.isSelected
                      ? Border.all(
                          color: widget.isSelected
                              ? AppTheme.primaryGold
                              : (widget.borderColor ??
                                  AppTheme.textMuted.withValues(alpha: 0.3)),
                          width: widget.isSelected ? 2.5 : widget.borderWidth,
                        )
                      : null,
                  boxShadow: widget.isElevated
                      ? [
                          BoxShadow(
                            color: Colors.black.withValues(
                                alpha: 0.08 * _shadowAnimation.value),
                            blurRadius: 16 * _shadowAnimation.value,
                            offset: Offset(0, 4 * _shadowAnimation.value),
                            spreadRadius: 0,
                          ),
                          BoxShadow(
                            color: Colors.black.withValues(
                                alpha: 0.04 * _shadowAnimation.value),
                            blurRadius: 4 * _shadowAnimation.value,
                            offset: Offset(0, 2 * _shadowAnimation.value),
                            spreadRadius: 0,
                          ),
                        ]
                      : null,
                ),
                child: Material(
                  color: Colors.transparent,
                  child: Padding(
                    padding: effectivePadding,
                    child: widget.child,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class PremiumIconCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final String? subtitle;
  final VoidCallback? onTap;
  final Color? iconColor;
  final bool isSelected;
  final bool hasGradient;

  const PremiumIconCard({
    super.key,
    required this.icon,
    required this.title,
    this.subtitle,
    this.onTap,
    this.iconColor,
    this.isSelected = false,
    this.hasGradient = false,
  });

  @override
  Widget build(BuildContext context) {
    return PremiumCard(
      onTap: onTap,
      isSelected: isSelected,
      hasGradient: hasGradient,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 64,
            height: 64,
            decoration: BoxDecoration(
              color:
                  (iconColor ?? AppTheme.primaryGold).withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              icon,
              size: 32,
              color: iconColor ?? AppTheme.primaryGold,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color:
                      hasGradient ? AppTheme.textLight : AppTheme.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
            textAlign: TextAlign.center,
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 8),
            Text(
              subtitle!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: hasGradient
                        ? AppTheme.textLight.withValues(alpha: 0.8)
                        : AppTheme.textSecondary,
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

class PremiumStatsCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color? color;
  final String? subtitle;
  final VoidCallback? onTap;

  const PremiumStatsCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    this.color,
    this.subtitle,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveColor = color ?? AppTheme.primaryGold;

    return PremiumCard(
      onTap: onTap,
      child: Row(
        children: [
          Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
              color: effectiveColor.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Icon(
              icon,
              size: 28,
              color: effectiveColor,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  value,
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        color: AppTheme.textPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 4),
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppTheme.textSecondary,
                      ),
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 2),
                  Text(
                    subtitle!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.textMuted,
                        ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}
