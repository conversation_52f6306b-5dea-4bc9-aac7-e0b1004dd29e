import 'package:flutter/foundation.dart';
import 'dart:typed_data';
import '../main.dart';

class StorageService {
  static Future<void> initialize() async {
    if (kDebugMode) {
      print('Storage service initialized');
    }
  }

  static Future<String?> uploadFile({
    required String bucket,
    required String path,
    required Uint8List fileBytes,
    String? contentType,
  }) async {
    try {
      await supabase.storage.from(bucket).uploadBinary(
            path,
            fileBytes,
          );

      final publicUrl = supabase.storage.from(bucket).getPublicUrl(path);
      return publicUrl;
    } catch (error) {
      if (kDebugMode) {
        print('Error uploading file: $error');
      }
      return null;
    }
  }

  static Future<bool> deleteFile({
    required String bucket,
    required String path,
  }) async {
    try {
      await supabase.storage.from(bucket).remove([path]);
      return true;
    } catch (error) {
      if (kDebugMode) {
        print('Error deleting file: $error');
      }
      return false;
    }
  }

  static String getPublicUrl({
    required String bucket,
    required String path,
  }) {
    return supabase.storage.from(bucket).getPublicUrl(path);
  }

  static Future<List<int>?> downloadFile({
    required String bucket,
    required String path,
  }) async {
    try {
      final response = await supabase.storage.from(bucket).download(path);
      return response;
    } catch (error) {
      if (kDebugMode) {
        print('Error downloading file: $error');
      }
      return null;
    }
  }
}
