import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/review_model.dart';
import '../../widgets/custom_app_bar.dart';
import 'add_review_screen.dart';

class ReviewsScreen extends StatefulWidget {
  final String trainerId;

  const ReviewsScreen({
    super.key,
    required this.trainerId,
  });

  @override
  State<ReviewsScreen> createState() => _ReviewsScreenState();
}

class _ReviewsScreenState extends State<ReviewsScreen> {
  List<Review> _reviews = [];
  ReviewStats? _stats;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadReviews();
  }

  Future<void> _loadReviews() async {
    setState(() => _isLoading = true);

    try {
      // تحميل التقييمات من Supabase مع أسماء المتدربين
      final response = await Supabase.instance.client
          .from('reviews')
          .select('*, users!reviews_trainee_id_fkey(full_name, avatar_url)')
          .eq('trainer_id', widget.trainerId)
          .order('created_at', ascending: false);

      // تحويل البيانات إلى نماذج
      _reviews =
          (response as List).map((data) => Review.fromJson(data)).toList();

      // إذا لم توجد بيانات، استخدم بيانات تجريبية
      if (_reviews.isEmpty) {
        _reviews = _generateSampleReviews();
      }

      _stats = ReviewStats.fromReviews(_reviews);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل التقييمات: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  List<Review> _generateSampleReviews() {
    final now = DateTime.now();
    return [
      Review(
        id: 'review_1',
        trainerId: widget.trainerId,
        traineeId: 'trainee_1',
        traineeName: 'أحمد محمد',
        rating: 5,
        comment: 'مدرب ممتاز، ساعدني كثيراً في تحقيق أهدافي. أنصح به بشدة!',
        isAnonymous: false,
        sessionId: 'session_1',
        createdAt: now.subtract(const Duration(days: 2)),
        updatedAt: now.subtract(const Duration(days: 2)),
      ),
      Review(
        id: 'review_2',
        trainerId: widget.trainerId,
        traineeId: 'trainee_2',
        traineeName: null,
        rating: 4,
        comment: 'تجربة جيدة جداً، المدرب محترف ويهتم بالتفاصيل.',
        isAnonymous: true,
        sessionId: null,
        createdAt: now.subtract(const Duration(days: 5)),
        updatedAt: now.subtract(const Duration(days: 5)),
      ),
      Review(
        id: 'review_3',
        trainerId: widget.trainerId,
        traineeId: 'trainee_3',
        traineeName: 'فاطمة علي',
        rating: 5,
        comment: 'أفضل مدرب تعاملت معه! النتائج واضحة والتعامل راقي.',
        isAnonymous: false,
        sessionId: 'session_2',
        createdAt: now.subtract(const Duration(days: 7)),
        updatedAt: now.subtract(const Duration(days: 7)),
      ),
      Review(
        id: 'review_4',
        trainerId: widget.trainerId,
        traineeId: 'trainee_4',
        traineeName: null,
        rating: 3,
        comment: 'جيد بشكل عام، لكن يمكن تحسين التواصل أكثر.',
        isAnonymous: true,
        sessionId: null,
        createdAt: now.subtract(const Duration(days: 10)),
        updatedAt: now.subtract(const Duration(days: 10)),
      ),
      Review(
        id: 'review_5',
        trainerId: widget.trainerId,
        traineeId: 'trainee_5',
        traineeName: 'خالد أحمد',
        rating: 4,
        comment: 'مدرب جيد ومتفهم، ساعدني في تطوير لياقتي البدنية.',
        isAnonymous: false,
        sessionId: 'session_3',
        createdAt: now.subtract(const Duration(days: 14)),
        updatedAt: now.subtract(const Duration(days: 14)),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: const CustomAppBar(
        title: 'التقييمات والمراجعات',
        showBackButton: true,
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addNewReview,
        backgroundColor: Colors.blue,
        child: const Icon(Icons.add, color: Colors.white),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                if (_stats != null) _buildStatsCard(),
                Expanded(
                  child: _reviews.isEmpty
                      ? _buildEmptyState()
                      : _buildReviewsList(),
                ),
              ],
            ),
    );
  }

  Widget _buildStatsCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Column(
                children: [
                  Text(
                    _stats!.averageRating.toStringAsFixed(1),
                    style: const TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: Colors.orange,
                    ),
                  ),
                  _buildStarRating(_stats!.averageRating),
                  const SizedBox(height: 4),
                  Text(
                    '${_stats!.totalReviews} تقييم',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildRatingDistribution(),
        ],
      ),
    );
  }

  Widget _buildStarRating(double rating) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        return Icon(
          index < rating.floor()
              ? Icons.star
              : index < rating
                  ? Icons.star_half
                  : Icons.star_border,
          color: Colors.orange,
          size: 20,
        );
      }),
    );
  }

  Widget _buildRatingDistribution() {
    return Column(
      children: [
        for (int i = 5; i >= 1; i--)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 2),
            child: Row(
              children: [
                Text('$i'),
                const SizedBox(width: 8),
                Icon(Icons.star, color: Colors.orange, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: LinearProgressIndicator(
                    value:
                        _stats!.ratingDistribution[i]! / _stats!.totalReviews,
                    backgroundColor: Colors.grey[300],
                    valueColor:
                        const AlwaysStoppedAnimation<Color>(Colors.orange),
                  ),
                ),
                const SizedBox(width: 8),
                Text('${_stats!.ratingDistribution[i]}'),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildReviewsList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _reviews.length,
      itemBuilder: (context, index) {
        final review = _reviews[index];
        return _buildReviewCard(review);
      },
    );
  }

  Widget _buildReviewCard(Review review) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                backgroundColor: Colors.blue.withValues(alpha: 0.1),
                backgroundImage:
                    review.traineeAvatar != null && !review.isAnonymous
                        ? NetworkImage(review.traineeAvatar!)
                        : null,
                child: review.traineeAvatar == null || review.isAnonymous
                    ? Icon(
                        review.isAnonymous
                            ? Icons.person_outline
                            : Icons.person,
                        color: Colors.blue,
                      )
                    : null,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      review.displayName,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        _buildStarRating(review.rating.toDouble()),
                        const SizedBox(width: 8),
                        Text(
                          _formatDate(review.createdAt),
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (review.comment != null) ...[
            const SizedBox(height: 12),
            Text(
              review.comment!,
              style: const TextStyle(
                fontSize: 14,
                height: 1.4,
              ),
            ),
          ],
          if (review.sessionId != null) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'تقييم جلسة',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.blue[700],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.star_outline,
            size: 64,
            color: Colors.grey.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          const Text(
            'لا توجد تقييمات بعد',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          const Text(
            'ستظهر التقييمات هنا عندما يقوم المتدربون بتقييم الخدمات',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'اليوم';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return 'منذ $weeks ${weeks == 1 ? 'أسبوع' : 'أسابيع'}';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  void _addNewReview() {
    final user = Supabase.instance.client.auth.currentUser;
    if (user != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => AddReviewScreen(
            trainerId: widget.trainerId,
            traineeId: user.id,
            onReviewAdded: _loadReviews,
          ),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يجب تسجيل الدخول أولاً')),
      );
    }
  }
}
