import 'package:flutter/foundation.dart';
import '../main.dart';

class NotificationService {
  static Future<void> initialize() async {
    // Initialize push notifications
    if (kDebugMode) {
      print('Notification service initialized');
    }
  }

  static Future<void> sendNotification({
    required String userId,
    required String title,
    required String message,
    String type = 'general',
    Map<String, dynamic>? data,
  }) async {
    try {
      await supabase.from('notifications').insert({
        'user_id': userId,
        'title': title,
        'message': message,
        'type': type,
        'data': data ?? {},
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (error) {
      if (kDebugMode) {
        print('Error sending notification: $error');
      }
    }
  }

  static Future<List<Map<String, dynamic>>> getNotifications({
    required String userId,
    bool unreadOnly = false,
    int limit = 50,
  }) async {
    try {
      var query = supabase
          .from('notifications')
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false)
          .limit(limit);

      if (unreadOnly) {
        // إعادة تنفيذ الاستعلام مع شرط is_read فقط إذا كان unreadOnly
        query = supabase
            .from('notifications')
            .select()
            .eq('user_id', userId)
            .eq('is_read', false)
            .order('created_at', ascending: false)
            .limit(limit);
      }

      final response = await query;
      return List<Map<String, dynamic>>.from(response);
    } catch (error) {
      if (kDebugMode) {
        print('Error fetching notifications: $error');
      }
      return [];
    }
  }

  static Future<void> markAsRead(String notificationId) async {
    try {
      await supabase
          .from('notifications')
          .update({'is_read': true}).eq('id', notificationId);
    } catch (error) {
      if (kDebugMode) {
        print('Error marking notification as read: $error');
      }
    }
  }

  static Future<void> markAllAsRead(String userId) async {
    try {
      await supabase
          .from('notifications')
          .update({'is_read': true})
          .eq('user_id', userId)
          .eq('is_read', false);
    } catch (error) {
      if (kDebugMode) {
        print('Error marking all notifications as read: $error');
      }
    }
  }

  static Future<int> getUnreadCount(String userId) async {
    try {
      final response = await supabase
          .from('notifications')
          .select()
          .eq('user_id', userId)
          .eq('is_read', false);
      return response.length;
    } catch (error) {
      if (kDebugMode) {
        print('Error getting unread count: $error');
      }
      return 0;
    }
  }
}
