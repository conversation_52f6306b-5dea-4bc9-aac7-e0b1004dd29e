import 'package:flutter/material.dart';

class SubscriptionPlan {
  final String id;
  final String trainerId;
  final String name;
  final String? description;
  final int durationDays;
  final double price;
  final List<String> features;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  SubscriptionPlan({
    required this.id,
    required this.trainerId,
    required this.name,
    this.description,
    required this.durationDays,
    required this.price,
    this.features = const [],
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SubscriptionPlan.fromJson(Map<String, dynamic> json) {
    return SubscriptionPlan(
      id: json['id'],
      trainerId: json['trainer_id'],
      name: json['name'],
      description: json['description'],
      durationDays: json['duration_days'],
      price: json['price'].toDouble(),
      features: _parseFeatures(json['features']),
      isActive: json['is_active'] ?? true,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  static List<String> _parseFeatures(dynamic features) {
    if (features == null) return [];

    try {
      // First, try to handle as iterable (covers List, JSArray, etc.)
      try {
        if (features is Iterable) {
          return features
              .map((e) => e?.toString() ?? '')
              .where((e) => e.isNotEmpty)
              .toList();
        }
      } catch (e) {
        // If iterable check fails, continue to other methods
      }

      // Check if it has a length property (JSArray characteristic)
      try {
        final dynamic length = features.length;
        if (length != null && length is int && length >= 0) {
          final List<String> result = [];
          for (int i = 0; i < length; i++) {
            try {
              final item = features[i];
              if (item != null) {
                final str = item.toString();
                if (str.isNotEmpty) {
                  result.add(str);
                }
              }
            } catch (e) {
              // Skip invalid items
            }
          }
          return result;
        }
      } catch (e) {
        // If length access fails, continue to other methods
      }

      // Handle String types
      if (features is String) {
        if (features.isEmpty) return [];
        try {
          // Handle comma-separated values
          return features
              .split(',')
              .map((e) => e.trim())
              .where((e) => e.isNotEmpty)
              .toList();
        } catch (e) {
          return [features];
        }
      }

      // Handle other types by converting to string first
      final String stringData = features.toString();

      // Check if the string representation looks like an array
      if (stringData.contains('[') && stringData.contains(']')) {
        final String cleanData = stringData
            .replaceAll('[', '')
            .replaceAll(']', '')
            .replaceAll('"', '')
            .replaceAll("'", '')
            .trim();

        if (cleanData.isEmpty) return [];

        return cleanData
            .split(',')
            .map((e) => e.trim())
            .where((e) => e.isNotEmpty)
            .toList();
      }

      // Last resort: return as single item if not empty
      if (stringData.isNotEmpty && stringData != 'null') {
        return [stringData];
      }

      return [];
    } catch (e) {
      // Silent fail and return empty list
      return [];
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'trainer_id': trainerId,
      'name': name,
      'description': description,
      'duration_days': durationDays,
      'price': price,
      'features': features,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // الحصول على اللون حسب نوع الخطة
  static Color getPlanColor(String planName) {
    switch (planName.toLowerCase()) {
      case 'basic':
        return const Color(0xFF4CAF50); // أخضر
      case 'standard':
        return const Color(0xFF2196F3); // أزرق
      case 'premium':
        return const Color(0xFFFF9800); // برتقالي/ذهبي
      default:
        return const Color(0xFF9E9E9E); // رمادي
    }
  }

  // الحصول على الأيقونة حسب نوع الخطة
  static IconData getPlanIcon(String planName) {
    switch (planName.toLowerCase()) {
      case 'basic':
        return Icons.star;
      case 'standard':
        return Icons.star_half;
      case 'premium':
        return Icons.stars;
      default:
        return Icons.info;
    }
  }
}
