import 'package:flutter/material.dart';

class SubscriptionPlan {
  final String id;
  final String trainerId;
  final String name;
  final String? description;
  final int durationDays;
  final double price;
  final List<String> features;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  SubscriptionPlan({
    required this.id,
    required this.trainerId,
    required this.name,
    this.description,
    required this.durationDays,
    required this.price,
    this.features = const [],
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SubscriptionPlan.fromJson(Map<String, dynamic> json) {
    return SubscriptionPlan(
      id: json['id'],
      trainerId: json['trainer_id'],
      name: json['name'],
      description: json['description'],
      durationDays: json['duration_days'],
      price: json['price'].toDouble(),
      features:
          json['features'] != null ? List<String>.from(json['features']) : [],
      isActive: json['is_active'] ?? true,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'trainer_id': trainerId,
      'name': name,
      'description': description,
      'duration_days': durationDays,
      'price': price,
      'features': features,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // الحصول على اللون حسب نوع الخطة
  static Color getPlanColor(String planName) {
    switch (planName.toLowerCase()) {
      case 'basic':
        return const Color(0xFF4CAF50); // أخضر
      case 'standard':
        return const Color(0xFF2196F3); // أزرق
      case 'premium':
        return const Color(0xFFFF9800); // برتقالي/ذهبي
      default:
        return const Color(0xFF9E9E9E); // رمادي
    }
  }

  // الحصول على الأيقونة حسب نوع الخطة
  static IconData getPlanIcon(String planName) {
    switch (planName.toLowerCase()) {
      case 'basic':
        return Icons.star;
      case 'standard':
        return Icons.star_half;
      case 'premium':
        return Icons.stars;
      default:
        return Icons.info;
    }
  }
}
