import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../theme/app_theme.dart';
import '../../widgets/premium_card.dart';
import '../../widgets/premium_button.dart';
import '../../widgets/animated_widgets.dart';
import '../payments/payment_screen.dart';

class SubscriptionDetailsScreen extends StatefulWidget {
  final Map<String, dynamic> subscription;

  const SubscriptionDetailsScreen({
    super.key,
    required this.subscription,
  });

  @override
  State<SubscriptionDetailsScreen> createState() =>
      _SubscriptionDetailsScreenState();
}

class _SubscriptionDetailsScreenState extends State<SubscriptionDetailsScreen> {
  final supabase = Supabase.instance.client;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final subscription = widget.subscription;
    final trainer = subscription['trainers'];
    final user = trainer?['users'];
    final trainerName = user?['full_name'] ?? 'مدرب غير محدد';
    final trainerAvatar = user?['avatar_url'];
    final status = subscription['status'] ?? 'pending';
    final planType = subscription['plan_type'] ?? 'monthly';
    final price = subscription['price']?.toDouble() ?? 0.0;
    final startDate = subscription['start_date'];
    final endDate = subscription['end_date'];
    final autoRenew = subscription['auto_renew'] ?? false;

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SlideInAnimation(
                        child: _buildStatusCard(status),
                      ),
                      const SizedBox(height: 24),
                      SlideInAnimation(
                        delay: 200,
                        child: _buildTrainerCard(trainerName, trainerAvatar),
                      ),
                      const SizedBox(height: 24),
                      SlideInAnimation(
                        delay: 400,
                        child: _buildSubscriptionInfo(
                            planType, price, startDate, endDate, autoRenew),
                      ),
                      const SizedBox(height: 24),
                      SlideInAnimation(
                        delay: 600,
                        child: _buildPaymentHistory(),
                      ),
                      const SizedBox(height: 32),
                      SlideInAnimation(
                        delay: 800,
                        child: _buildActionButtons(status, planType, price),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.arrow_back, color: AppTheme.textPrimary),
          ),
          const Expanded(
            child: Text(
              'تفاصيل الاشتراك',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(width: 48),
        ],
      ),
    );
  }

  Widget _buildStatusCard(String status) {
    Color statusColor;
    String statusText;
    IconData statusIcon;

    switch (status) {
      case 'active':
        statusColor = Colors.green;
        statusText = 'نشط';
        statusIcon = Icons.check_circle;
        break;
      case 'pending':
        statusColor = Colors.orange;
        statusText = 'في الانتظار';
        statusIcon = Icons.pending;
        break;
      case 'expired':
        statusColor = Colors.red;
        statusText = 'منتهي الصلاحية';
        statusIcon = Icons.schedule;
        break;
      case 'cancelled':
        statusColor = Colors.grey;
        statusText = 'ملغي';
        statusIcon = Icons.cancel;
        break;
      default:
        statusColor = Colors.grey;
        statusText = 'غير محدد';
        statusIcon = Icons.help;
    }

    return PremiumCard(
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: statusColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              statusIcon,
              color: statusColor,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'حالة الاشتراك',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppTheme.textSecondary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  statusText,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: statusColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrainerCard(String name, String? avatar) {
    return PremiumCard(
      child: Row(
        children: [
          CircleAvatar(
            radius: 30,
            backgroundColor: AppTheme.primaryGold.withValues(alpha: 0.2),
            backgroundImage: avatar != null ? NetworkImage(avatar) : null,
            child: avatar == null
                ? const Icon(Icons.person,
                    size: 30, color: AppTheme.primaryGold)
                : null,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'المدرب',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppTheme.textSecondary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  name,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              // Navigate to trainer profile
            },
            icon: const Icon(
              Icons.arrow_forward_ios,
              color: AppTheme.primaryGold,
              size: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubscriptionInfo(String planType, double price,
      String? startDate, String? endDate, bool autoRenew) {
    final planName = planType == 'monthly' ? 'الخطة الشهرية' : 'جلسة واحدة';

    return PremiumCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.info_outline, color: AppTheme.primaryGold, size: 20),
              SizedBox(width: 8),
              Text(
                'معلومات الاشتراك',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoRow('نوع الخطة', planName),
          const SizedBox(height: 12),
          _buildInfoRow('السعر', '${price.toStringAsFixed(0)} ريال'),
          const SizedBox(height: 12),
          if (startDate != null)
            _buildInfoRow('تاريخ البداية', _formatDate(startDate)),
          if (startDate != null) const SizedBox(height: 12),
          if (endDate != null)
            _buildInfoRow('تاريخ الانتهاء', _formatDate(endDate)),
          if (endDate != null) const SizedBox(height: 12),
          _buildInfoRow('التجديد التلقائي', autoRenew ? 'مفعل' : 'غير مفعل'),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            color: AppTheme.textSecondary,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentHistory() {
    return PremiumCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.history, color: AppTheme.primaryGold, size: 20),
              SizedBox(width: 8),
              Text(
                'سجل المدفوعات',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Placeholder for payment history
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.textSecondary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Row(
              children: [
                Icon(Icons.receipt, color: AppTheme.textSecondary, size: 20),
                SizedBox(width: 12),
                Text(
                  'لا توجد مدفوعات سابقة',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppTheme.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(String status, String planType, double price) {
    return Column(
      children: [
        if (status == 'active') ...[
          PremiumButton(
            text: 'تجديد الاشتراك',
            onPressed:
                _isLoading ? null : () => _renewSubscription(planType, price),
            icon: Icons.refresh,
            hasGradient: true,
            height: 50,
            width: double.infinity,
          ),
          const SizedBox(height: 12),
          PremiumButton(
            text: 'إلغاء الاشتراك',
            onPressed: _isLoading ? null : _cancelSubscription,
            icon: Icons.cancel,
            hasGradient: false,
            backgroundColor: Colors.red.withValues(alpha: 0.1),
            textColor: Colors.red,
            height: 50,
            width: double.infinity,
          ),
        ] else if (status == 'expired' || status == 'cancelled') ...[
          PremiumButton(
            text: 'إعادة تفعيل الاشتراك',
            onPressed: _isLoading
                ? null
                : () => _reactivateSubscription(planType, price),
            icon: Icons.restart_alt,
            hasGradient: true,
            height: 50,
            width: double.infinity,
          ),
        ] else if (status == 'pending') ...[
          PremiumButton(
            text: 'إكمال الدفع',
            onPressed:
                _isLoading ? null : () => _completePayment(planType, price),
            icon: Icons.payment,
            hasGradient: true,
            height: 50,
            width: double.infinity,
          ),
          const SizedBox(height: 12),
          PremiumButton(
            text: 'إلغاء الاشتراك',
            onPressed: _isLoading ? null : _cancelSubscription,
            icon: Icons.cancel,
            hasGradient: false,
            backgroundColor: Colors.red.withValues(alpha: 0.1),
            textColor: Colors.red,
            height: 50,
            width: double.infinity,
          ),
        ],
      ],
    );
  }

  Future<void> _renewSubscription(String planType, double price) async {
    final confirmed = await _showConfirmationDialog(
      'تجديد الاشتراك',
      'هل أنت متأكد من رغبتك في تجديد هذا الاشتراك؟',
    );

    if (confirmed) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => PaymentScreen(
            trainer: widget.subscription['trainers'] ?? {},
            planType: planType,
            amount: price,
            isRenewal: true,
            subscriptionId: widget.subscription['id'],
          ),
        ),
      );
    }
  }

  Future<void> _cancelSubscription() async {
    final confirmed = await _showConfirmationDialog(
      'إلغاء الاشتراك',
      'هل أنت متأكد من رغبتك في إلغاء هذا الاشتراك؟ لن تتمكن من استخدام الخدمات بعد الإلغاء.',
    );

    if (confirmed) {
      setState(() {
        _isLoading = true;
      });

      try {
        await supabase.from('subscriptions').update({
          'status': 'cancelled',
          'updated_at': DateTime.now().toIso8601String(),
        }).eq('id', widget.subscription['id']);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إلغاء الاشتراك بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context, true); // Return true to indicate changes
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('حدث خطأ: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  Future<void> _reactivateSubscription(String planType, double price) async {
    final confirmed = await _showConfirmationDialog(
      'إعادة تفعيل الاشتراك',
      'هل أنت متأكد من رغبتك في إعادة تفعيل هذا الاشتراك؟',
    );

    if (confirmed && mounted) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => PaymentScreen(
            trainer: widget.subscription['trainers'] ?? {},
            planType: planType,
            amount: price,
            isReactivation: true,
            subscriptionId: widget.subscription['id'],
          ),
        ),
      );
    }
  }

  Future<void> _completePayment(String planType, double price) async {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PaymentScreen(
          trainer: widget.subscription['trainers'] ?? {},
          planType: planType,
          amount: price,
          subscriptionId: widget.subscription['id'],
        ),
      ),
    );
  }

  Future<bool> _showConfirmationDialog(String title, String content) async {
    return await showDialog<bool>(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              title: Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimary,
                ),
              ),
              content: Text(
                content,
                style: const TextStyle(
                  color: AppTheme.textSecondary,
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text(
                    'إلغاء',
                    style: TextStyle(color: AppTheme.textSecondary),
                  ),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryGold,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('تأكيد'),
                ),
              ],
            );
          },
        ) ??
        false;
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }
}
