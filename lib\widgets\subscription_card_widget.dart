import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'premium_button.dart';
import 'premium_card.dart';

class SubscriptionCardWidget extends StatelessWidget {
  final Map<String, dynamic> subscription;
  final VoidCallback? onTap;

  const SubscriptionCardWidget({
    super.key,
    required this.subscription,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final trainer = subscription['trainers'];
    final user = trainer['users'];
    final plan = subscription['subscription_plans'];
    final status = subscription['status'] ?? 'unknown';
    final endDate = subscription['end_date'];
    final startDate = subscription['start_date'];
    final price = subscription['price'] ?? plan?['price'] ?? 0;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: PremiumCard(
        hasGradient: true,
        gradientColors: [
          _getStatusColor(status).withValues(alpha: 0.1),
          AppTheme.primaryGold.withValues(alpha: 0.05),
        ],
        onTap: onTap,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات المدرب والخطة
            Row(
              children: [
                _buildTrainerAvatar(user),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user['full_name'] ?? 'مدرب غير معروف',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                      if (plan != null)
                        Text(
                          'خطة ${plan['name']}',
                          style: const TextStyle(
                            color: AppTheme.textSecondary,
                            fontSize: 14,
                          ),
                        ),
                    ],
                  ),
                ),
                _buildStatusBadge(status),
              ],
            ),

            const SizedBox(height: 16),

            // تفاصيل الاشتراك
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    Icons.schedule,
                    'بدء الاشتراك',
                    _formatDate(startDate),
                    AppTheme.lightBlue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildInfoItem(
                    Icons.event,
                    'تاريخ الانتهاء',
                    _formatDate(endDate),
                    AppTheme.warmOrange,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    Icons.attach_money,
                    'المبلغ المدفوع',
                    '$price ريال',
                    AppTheme.emeraldGreen,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildInfoItem(
                    Icons.access_time,
                    'الأيام المتبقية',
                    _calculateRemainingDays(endDate),
                    _getStatusColor(status),
                  ),
                ),
              ],
            ),

            // إجراءات إضافية حسب الحالة
            if (status == 'active') ...[
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: PremiumButton(
                      text: 'إدارة الاشتراك',
                      onPressed: () {
                        // فتح شاشة إدارة الاشتراك
                      },
                      icon: Icons.settings,
                      height: 40,
                      hasGradient: false,
                      backgroundColor: AppTheme.surfaceLight,
                      textColor: AppTheme.textPrimary,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: PremiumButton(
                      text: 'تجديد',
                      onPressed: () {
                        // فتح شاشة التجديد
                      },
                      icon: Icons.refresh,
                      height: 40,
                    ),
                  ),
                ],
              ),
            ],

            if (status == 'expired') ...[
              const SizedBox(height: 16),
              PremiumButton(
                text: 'تجديد الاشتراك',
                onPressed: () {
                  // فتح شاشة التجديد
                },
                icon: Icons.restart_alt,
                height: 40,
                width: double.infinity,
                gradientColors: [
                  AppTheme.coralRed,
                  AppTheme.warmOrange,
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTrainerAvatar(Map<String, dynamic> user) {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryGold.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(15),
        child: user['avatar_url'] != null
            ? Image.network(
                user['avatar_url'],
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildDefaultAvatar(user['full_name']);
                },
              )
            : _buildDefaultAvatar(user['full_name']),
      ),
    );
  }

  Widget _buildDefaultAvatar(String? name) {
    return Container(
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: BorderRadius.circular(15),
      ),
      child: Center(
        child: Text(
          name != null && name.isNotEmpty ? name[0].toUpperCase() : '؟',
          style: const TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String status) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: _getStatusColor(status).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getStatusColor(status).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Text(
        _getStatusText(status),
        style: TextStyle(
          color: _getStatusColor(status),
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildInfoItem(
      IconData icon, String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 16,
                color: color,
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  label,
                  style: TextStyle(
                    color: color,
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return AppTheme.emeraldGreen;
      case 'expired':
        return AppTheme.coralRed;
      case 'cancelled':
        return AppTheme.textMuted;
      case 'pending':
        return AppTheme.warmOrange;
      default:
        return AppTheme.primaryGold;
    }
  }

  String _getStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return 'نشط';
      case 'expired':
        return 'منتهي';
      case 'cancelled':
        return 'ملغي';
      case 'pending':
        return 'معلق';
      default:
        return status;
    }
  }

  String _formatDate(String? dateStr) {
    if (dateStr == null) return 'غير محدد';
    try {
      final date = DateTime.parse(dateStr);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return 'غير محدد';
    }
  }

  String _calculateRemainingDays(String? endDateStr) {
    if (endDateStr == null) return 'غير محدد';
    try {
      final endDate = DateTime.parse(endDateStr);
      final now = DateTime.now();
      final remaining = endDate.difference(now).inDays;

      if (remaining < 0) {
        return 'منتهي';
      } else if (remaining == 0) {
        return 'ينتهي اليوم';
      } else {
        return '$remaining يوم';
      }
    } catch (e) {
      return 'غير محدد';
    }
  }
}
