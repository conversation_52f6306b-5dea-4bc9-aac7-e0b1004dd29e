import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';
import '../../widgets/premium_card.dart';
import '../../widgets/premium_button.dart';
import '../../widgets/animated_widgets.dart';
import 'new_subscription_screen.dart';
import 'subscription_plans_screen.dart';

class SubscriptionTypeSelectionScreen extends StatefulWidget {
  final Map<String, dynamic> trainer;
  final String trainerId;

  const SubscriptionTypeSelectionScreen({
    super.key,
    required this.trainer,
    required this.trainerId,
  });

  @override
  State<SubscriptionTypeSelectionScreen> createState() =>
      _SubscriptionTypeSelectionScreenState();
}

class _SubscriptionTypeSelectionScreenState
    extends State<SubscriptionTypeSelectionScreen> {
  @override
  Widget build(BuildContext context) {
    final trainerName = _getSafeString(widget.trainer, 'full_name', 'مدرب غير محدد');
    final avatarUrl = _getSafeString(widget.trainer, 'avatar_url', '');
    final rating = _getSafeDouble(widget.trainer, 'rating', 0.0);
    final experience = _getSafeInt(widget.trainer, 'experience_years', 0);

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      SlideInAnimation(
                        child: _buildTrainerCard(trainerName, avatarUrl, rating, experience),
                      ),
                      const SizedBox(height: 32),
                      SlideInAnimation(
                        delay: 200,
                        child: _buildSelectionTitle(),
                      ),
                      const SizedBox(height: 24),
                      SlideInAnimation(
                        delay: 400,
                        child: _buildSessionOption(),
                      ),
                      const SizedBox(height: 16),
                      SlideInAnimation(
                        delay: 600,
                        child: _buildMonthlyOption(),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.arrow_back, color: AppTheme.textPrimary),
          ),
          const Expanded(
            child: Text(
              'اختر نوع الاشتراك',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(width: 48),
        ],
      ),
    );
  }

  Widget _buildTrainerCard(String name, String avatarUrl, double rating, int experience) {
    return PremiumCard(
      hasGradient: true,
      gradientColors: [
        AppTheme.primaryGold.withValues(alpha: 0.1),
        AppTheme.emeraldGreen.withValues(alpha: 0.05),
      ],
      child: Row(
        children: [
          // صورة المدرب
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(40),
              gradient: const LinearGradient(
                colors: [AppTheme.primaryGold, AppTheme.emeraldGreen],
              ),
            ),
            child: Container(
              margin: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(38),
                color: AppTheme.surfaceLight,
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(38),
                child: avatarUrl.isNotEmpty
                    ? Image.network(
                        avatarUrl,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return const Icon(
                            Icons.person,
                            size: 40,
                            color: AppTheme.textSecondary,
                          );
                        },
                      )
                    : const Icon(
                        Icons.person,
                        size: 40,
                        color: AppTheme.textSecondary,
                      ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          // معلومات المدرب
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(
                      Icons.star,
                      color: AppTheme.primaryGold,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      rating.toStringAsFixed(1),
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                    const SizedBox(width: 16),
                    const Icon(
                      Icons.work_outline,
                      color: AppTheme.emeraldGreen,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '$experience سنوات',
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppTheme.textSecondary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectionTitle() {
    return Column(
      children: [
        const Icon(
          Icons.fitness_center,
          size: 48,
          color: AppTheme.primaryGold,
        ),
        const SizedBox(height: 16),
        const Text(
          'اختر نوع الاشتراك المناسب لك',
          style: TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          'يمكنك الاختيار بين الجلسات المنفردة أو الخطط الشهرية',
          style: TextStyle(
            fontSize: 16,
            color: AppTheme.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSessionOption() {
    return PremiumCard(
      hasGradient: true,
      gradientColors: [
        AppTheme.emeraldGreen.withValues(alpha: 0.1),
        AppTheme.emeraldGreen.withValues(alpha: 0.05),
      ],
      child: Column(
        children: [
          Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: AppTheme.emeraldGreen.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: const Icon(
                  Icons.fitness_center,
                  color: AppTheme.emeraldGreen,
                  size: 30,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'جلسة واحدة',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    const Text(
                      'مثالي للتجربة أو الجلسات المتقطعة',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          PremiumButton(
            text: 'حجز جلسة واحدة',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => NewSubscriptionScreen(
                    trainer: widget.trainer,
                    trainerId: widget.trainerId,
                  ),
                ),
              );
            },
            hasGradient: true,
            gradientColors: [
              AppTheme.emeraldGreen,
              AppTheme.emeraldGreen.withValues(alpha: 0.8),
            ],
            height: 50,
            icon: Icons.play_arrow,
          ),
        ],
      ),
    );
  }

  Widget _buildMonthlyOption() {
    return PremiumCard(
      hasGradient: true,
      gradientColors: [
        AppTheme.primaryGold.withValues(alpha: 0.1),
        AppTheme.primaryGold.withValues(alpha: 0.05),
      ],
      child: Column(
        children: [
          Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: AppTheme.primaryGold.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: const Icon(
                  Icons.calendar_month,
                  color: AppTheme.primaryGold,
                  size: 30,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'خطة شهرية',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    const Text(
                      'خطط متنوعة مع خصومات وميزات إضافية',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          PremiumButton(
            text: 'تصفح الخطط الشهرية',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SubscriptionPlansScreen(),
                ),
              );
            },
            hasGradient: true,
            gradientColors: [
              AppTheme.primaryGold,
              AppTheme.primaryGold.withValues(alpha: 0.8),
            ],
            height: 50,
            icon: Icons.explore,
          ),
        ],
      ),
    );
  }

  // دوال مساعدة آمنة
  String _getSafeString(dynamic data, String key, String defaultValue) {
    if (data == null) return defaultValue;
    final value = data[key];
    if (value == null) return defaultValue;
    return value.toString();
  }

  double _getSafeDouble(dynamic data, String key, double defaultValue) {
    if (data == null) return defaultValue;
    final value = data[key];
    if (value == null) return defaultValue;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? defaultValue;
    }
    return defaultValue;
  }

  int _getSafeInt(dynamic data, String key, int defaultValue) {
    if (data == null) return defaultValue;
    final value = data[key];
    if (value == null) return defaultValue;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      return int.tryParse(value) ?? defaultValue;
    }
    return defaultValue;
  }
}
