import 'package:flutter/material.dart';
import '../../models/chat_message_model.dart';
import '../../widgets/custom_app_bar.dart';

class ChatScreen extends StatefulWidget {
  final String conversationId;
  final String otherUserId;
  final String otherUserName;
  final String currentUserId;

  const ChatScreen({
    super.key,
    required this.conversationId,
    required this.otherUserId,
    required this.otherUserName,
    required this.currentUserId,
  });

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final _messageController = TextEditingController();
  final _scrollController = ScrollController();
  List<ChatMessage> _messages = [];
  bool _isLoading = true;
  bool _isSending = false;

  @override
  void initState() {
    super.initState();
    _loadMessages();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadMessages() async {
    setState(() => _isLoading = true);

    try {
      // TODO: تحميل الرسائل من Supabase
      // final response = await Supabase.instance.client
      //     .from('chat_messages')
      //     .select()
      //     .eq('conversation_id', widget.conversationId)
      //     .order('created_at', ascending: true);

      // بيانات تجريبية
      _messages = _generateSampleMessages();

      // التمرير لآخر رسالة
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل الرسائل: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  List<ChatMessage> _generateSampleMessages() {
    final now = DateTime.now();
    return [
      ChatMessage(
        id: 'msg_1',
        senderId: widget.otherUserId,
        receiverId: widget.currentUserId,
        message: 'مرحباً! كيف يمكنني مساعدتك اليوم؟',
        messageType: MessageType.text,
        createdAt: now.subtract(const Duration(hours: 2)),
      ),
      ChatMessage(
        id: 'msg_2',
        senderId: widget.currentUserId,
        receiverId: widget.otherUserId,
        message: 'مرحباً، أريد الاستفسار عن برنامج التدريب الجديد',
        messageType: MessageType.text,
        createdAt: now.subtract(const Duration(hours: 1, minutes: 45)),
      ),
      ChatMessage(
        id: 'msg_3',
        senderId: widget.otherUserId,
        receiverId: widget.currentUserId,
        message:
            'بالطبع! سأرسل لك تفاصيل البرنامج. هل لديك أهداف محددة تريد تحقيقها؟',
        messageType: MessageType.text,
        createdAt: now.subtract(const Duration(hours: 1, minutes: 30)),
      ),
      ChatMessage(
        id: 'msg_4',
        senderId: widget.currentUserId,
        receiverId: widget.otherUserId,
        message: 'نعم، أريد زيادة الكتلة العضلية وتحسين اللياقة البدنية',
        messageType: MessageType.text,
        createdAt: now.subtract(const Duration(hours: 1, minutes: 15)),
      ),
      ChatMessage(
        id: 'msg_5',
        senderId: widget.otherUserId,
        receiverId: widget.currentUserId,
        message: 'ممتاز! سأعد لك برنامج مخصص يناسب أهدافك. متى يمكنك البدء؟',
        messageType: MessageType.text,
        createdAt: now.subtract(const Duration(minutes: 30)),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: CustomAppBar(
        title: widget.otherUserName,
        showBackButton: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.call),
            onPressed: () {
              // TODO: إضافة وظيفة المكالمة
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('قريباً - المكالمات الصوتية')),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.videocam),
            onPressed: () {
              // TODO: إضافة وظيفة المكالمة المرئية
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('قريباً - المكالمات المرئية')),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _buildMessagesList(),
          ),
          _buildMessageInput(),
        ],
      ),
    );
  }

  Widget _buildMessagesList() {
    if (_messages.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد رسائل بعد\nابدأ المحادثة!',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: _messages.length,
      itemBuilder: (context, index) {
        final message = _messages[index];
        final isMe = message.senderId == widget.currentUserId;
        return _buildMessageBubble(message, isMe);
      },
    );
  }

  Widget _buildMessageBubble(ChatMessage message, bool isMe) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment:
            isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          if (!isMe) ...[
            CircleAvatar(
              backgroundColor: Colors.blue.withValues(alpha: 0.1),
              child: const Icon(Icons.person, color: Colors.blue),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: isMe ? Colors.blue : Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 5,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message.message,
                    style: TextStyle(
                      fontSize: 16,
                      color: isMe ? Colors.white : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _formatMessageTime(message.createdAt),
                    style: TextStyle(
                      fontSize: 12,
                      color: isMe ? Colors.white70 : Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (isMe) ...[
            const SizedBox(width: 8),
            CircleAvatar(
              backgroundColor: Colors.green.withValues(alpha: 0.1),
              child: const Icon(Icons.person, color: Colors.green),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMessageInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 5,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.attach_file, color: Colors.grey),
            onPressed: () {
              // TODO: إضافة وظيفة إرفاق الملفات
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('قريباً - إرفاق الملفات')),
              );
            },
          ),
          Expanded(
            child: TextField(
              controller: _messageController,
              decoration: const InputDecoration(
                hintText: 'اكتب رسالتك...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(25)),
                ),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              maxLines: null,
              textInputAction: TextInputAction.send,
              onSubmitted: (_) => _sendMessage(),
            ),
          ),
          const SizedBox(width: 8),
          Container(
            decoration: const BoxDecoration(
              color: Colors.blue,
              shape: BoxShape.circle,
            ),
            child: IconButton(
              icon: _isSending
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : const Icon(Icons.send, color: Colors.white),
              onPressed: _isSending ? null : _sendMessage,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _sendMessage() async {
    final text = _messageController.text.trim();
    if (text.isEmpty || _isSending) return;

    setState(() => _isSending = true);
    _messageController.clear();

    try {
      final message = ChatMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        senderId: widget.currentUserId,
        receiverId: widget.otherUserId,
        message: text,
        messageType: MessageType.text,
        createdAt: DateTime.now(),
      );

      // TODO: إرسال الرسالة إلى Supabase
      // await Supabase.instance.client
      //     .from('chat_messages')
      //     .insert(message.toJson());

      // إضافة الرسالة محلياً
      setState(() {
        _messages.add(message);
      });

      // التمرير لآخر رسالة
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إرسال الرسالة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSending = false);
      }
    }
  }

  String _formatMessageTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inDays == 0) {
      return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else {
      return '${time.day}/${time.month}';
    }
  }
}
