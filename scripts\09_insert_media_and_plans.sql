-- إدخال خطط اشتراك تجريبية
INSERT INTO public.subscription_plans (trainer_id, name, description, duration_days, price, features) VALUES
-- للمدرب الأول
((SELECT id FROM public.trainers LIMIT 1), 'Basic', 'خطة أساسية للمبتدئين', 30, 299.00, 
 ARRAY['خطة تمرين أساسية', 'متابعة أسبوعية', 'استشارة غذائية واحدة']),
((SELECT id FROM public.trainers LIMIT 1), 'Standard', 'خطة متوسطة للمهتمين بالتطوير', 30, 499.00, 
 ARRAY['خطة تمرين متقدمة', 'خطة غذائية شاملة', 'متابعة يومية', 'جلسات فيديو']),
((SELECT id FROM public.trainers LIMIT 1), 'Premium', 'خطة شاملة للمحترفين', 30, 799.00, 
 ARRAY['خطة تمرين مخصوصة', 'خطة غذائية مفصلة', 'متابعة مباشرة 24/7', 'جلسات شخصية', 'تحليل شامل للجسم']);

-- إدخال ميديا للخطط الغذائية (إذا كانت موجودة)
INSERT INTO public.nutrition_media (nutrition_plan_id, media_type, media_url, title, description)
SELECT 
    id as nutrition_plan_id,
    'image' as media_type,
    'https://images.unsplash.com/photo-1490645935967-10de6ba17061?w=500' as media_url,
    'وجبة صحية متوازنة' as title,
    'مثال على وجبة صحية غنية بالبروتين والخضروات' as description
FROM public.nutrition_plans 
WHERE EXISTS (SELECT 1 FROM public.nutrition_plans LIMIT 1);

INSERT INTO public.nutrition_media (nutrition_plan_id, media_type, media_url, title, description)
SELECT 
    id as nutrition_plan_id,
    'image' as media_type,
    'https://images.unsplash.com/photo-1498837167922-ddd27525d352?w=500' as media_url,
    'سلطة خضراء طازجة' as title,
    'سلطة غنية بالفيتامينات والمعادن' as description
FROM public.nutrition_plans 
WHERE EXISTS (SELECT 1 FROM public.nutrition_plans LIMIT 1);

-- إدخال ميديا للخطط الرياضية (إذا كانت موجودة)
INSERT INTO public.workout_media (workout_plan_id, media_type, media_url, title, description)
SELECT 
    id as workout_plan_id,
    'image' as media_type,
    'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=500' as media_url,
    'تمرين القوة' as title,
    'تمارين لتقوية العضلات الأساسية' as description
FROM public.workout_plans 
WHERE EXISTS (SELECT 1 FROM public.workout_plans LIMIT 1);

INSERT INTO public.workout_media (workout_plan_id, media_type, media_url, title, description)
SELECT 
    id as workout_plan_id,
    'image' as media_type,
    'https://images.unsplash.com/photo-1581009146145-b5ef050c2e1e?w=500' as media_url,
    'تمرين الكارديو' as title,
    'تمارين لتحسين اللياقة القلبية' as description
FROM public.workout_plans 
WHERE EXISTS (SELECT 1 FROM public.workout_plans LIMIT 1);

-- إدخال فيديو تجريبي للتمارين
INSERT INTO public.workout_media (workout_plan_id, media_type, media_url, title, description)
SELECT 
    id as workout_plan_id,
    'video' as media_type,
    'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4' as media_url,
    'شرح التمرين' as title,
    'فيديو توضيحي لكيفية أداء التمرين بالشكل الصحيح' as description
FROM public.workout_plans 
WHERE EXISTS (SELECT 1 FROM public.workout_plans LIMIT 1);

-- إدخال بيانات تجريبية للخطط الغذائية (إذا لم تكن موجودة)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM public.nutrition_plans LIMIT 1) THEN
        INSERT INTO public.nutrition_plans (
            trainee_id, trainer_id, title, description, start_date, end_date,
            daily_calories, daily_protein, daily_carbs, daily_fats,
            meals, supplements, instructions
        ) VALUES (
            (SELECT id FROM public.users WHERE user_type = 'trainee' LIMIT 1),
            (SELECT id FROM public.trainers LIMIT 1),
            'خطة غذائية متوازنة للمبتدئين',
            'خطة غذائية شاملة ومتوازنة مصممة خصيصاً للمبتدئين في عالم اللياقة البدنية',
            CURRENT_DATE,
            CURRENT_DATE + INTERVAL '30 days',
            2000,
            120.5,
            250.0,
            65.0,
            '[
                {
                    "name": "الإفطار",
                    "items": ["2 بيضة مسلوقة", "شريحة خبز أسمر", "كوب حليب قليل الدسم", "حبة تفاح"]
                },
                {
                    "name": "الغداء", 
                    "items": ["150جم دجاج مشوي", "كوب أرز بني", "سلطة خضراء", "كوب عصير طبيعي"]
                },
                {
                    "name": "العشاء",
                    "items": ["سمك مشوي 120جم", "خضروات مطبوخة", "كوب زبادي"]
                }
            ]'::jsonb,
            '[
                "فيتامين د 1000 وحدة يومياً",
                "أوميجا 3 كبسولة واحدة",
                "مكمل البروتين عند الحاجة"
            ]'::jsonb,
            'يُنصح بشرب 2-3 لتر من الماء يومياً، وتناول الوجبات في أوقات منتظمة، وتجنب الأطعمة المقلية والمشروبات الغازية.'
        );
    END IF;
END $$;

-- إدخال بيانات تجريبية للخطط الرياضية (إذا لم تكن موجودة)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM public.workout_plans LIMIT 1) THEN
        INSERT INTO public.workout_plans (
            trainee_id, trainer_id, title, description, start_date, end_date,
            difficulty_level, exercises, rest_days, instructions
        ) VALUES (
            (SELECT id FROM public.users WHERE user_type = 'trainee' LIMIT 1),
            (SELECT id FROM public.trainers LIMIT 1),
            'برنامج تدريبي للمبتدئين',
            'برنامج تدريبي شامل مصمم للمبتدئين يهدف إلى بناء القوة واللياقة العامة',
            CURRENT_DATE,
            CURRENT_DATE + INTERVAL '30 days',
            2,
            '[
                {
                    "name": "تمرين الضغط",
                    "sets": "3",
                    "reps": "10-15",
                    "rest": "60 ثانية"
                },
                {
                    "name": "تمرين القرفصاء",
                    "sets": "3", 
                    "reps": "12-18",
                    "rest": "60 ثانية"
                },
                {
                    "name": "تمرين العقلة",
                    "sets": "3",
                    "reps": "5-8",
                    "rest": "90 ثانية"
                },
                {
                    "name": "تمرين الجري في المكان",
                    "duration": "10 دقائق",
                    "rest": "2 دقيقة"
                }
            ]'::jsonb,
            ARRAY['Friday', 'Sunday'],
            'يُنصح بالإحماء لمدة 10 دقائق قبل التمرين، والتبريد لمدة 5 دقائق بعد التمرين. تأكد من أداء التمارين بالشكل الصحيح لتجنب الإصابات.'
        );
    END IF;
END $$;

-- الآن إدخال الميديا للخطط الجديدة
INSERT INTO public.nutrition_media (nutrition_plan_id, media_type, media_url, title, description)
SELECT 
    id as nutrition_plan_id,
    'image' as media_type,
    'https://images.unsplash.com/photo-1490645935967-10de6ba17061?w=500' as media_url,
    'وجبة صحية متوازنة' as title,
    'مثال على وجبة صحية غنية بالبروتين والخضروات' as description
FROM public.nutrition_plans 
WHERE title = 'خطة غذائية متوازنة للمبتدئين';

INSERT INTO public.workout_media (workout_plan_id, media_type, media_url, title, description)
SELECT 
    id as workout_plan_id,
    'image' as media_type,
    'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=500' as media_url,
    'تمرين القوة' as title,
    'تمارين لتقوية العضلات الأساسية' as description
FROM public.workout_plans 
WHERE title = 'برنامج تدريبي للمبتدئين';