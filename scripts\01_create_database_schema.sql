-- FitGold Database Schema
-- قاعدة بيانات مشتركة بين تطبيق المتدرب والمدرب

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- <PERSON><PERSON> custom types
CREATE TYPE user_type AS ENUM ('trainee', 'trainer', 'admin');
CREATE TYPE gender_type AS ENUM ('male', 'female');
CREATE TYPE fitness_goal AS ENUM ('lose_weight', 'gain_weight', 'maintain_weight', 'build_muscle', 'improve_endurance', 'general_fitness');
CREATE TYPE session_status AS ENUM ('scheduled', 'completed', 'cancelled', 'reschedule_requested', 'no_show');
CREATE TYPE assignment_status AS ENUM ('pending', 'active', 'completed', 'cancelled');
CREATE TYPE subscription_status AS ENUM ('active', 'expired', 'cancelled', 'pending');
CREATE TYPE payment_status AS ENUM ('pending', 'completed', 'failed', 'refunded');
CREATE TYPE notification_type AS ENUM ('plan_assigned', 'session_reminder', 'session_booked', 'payment_due', 'general');

-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    phone TEXT,
    avatar_url TEXT,
    user_type user_type NOT NULL DEFAULT 'trainee',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trainers table
CREATE TABLE public.trainers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE UNIQUE NOT NULL,
    specialization TEXT[] DEFAULT '{}',
    bio TEXT,
    experience_years INTEGER DEFAULT 0,
    certifications TEXT[] DEFAULT '{}',
    languages TEXT[] DEFAULT '{"Arabic"}',
    rating DECIMAL(3,2) DEFAULT 0.00,
    total_reviews INTEGER DEFAULT 0,
    price_per_session DECIMAL(10,2) DEFAULT 0.00,
    price_per_month DECIMAL(10,2) DEFAULT 0.00,
    availability JSONB DEFAULT '{}',
    is_verified BOOLEAN DEFAULT false,
    is_available BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trainees profiles table
CREATE TABLE public.trainees_profiles (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE UNIQUE NOT NULL,
    age INTEGER NOT NULL CHECK (age >= 16 AND age <= 80),
    gender gender_type NOT NULL,
    weight DECIMAL(5,2) NOT NULL CHECK (weight > 0),
    height DECIMAL(5,2) NOT NULL CHECK (height > 0),
    fitness_goal fitness_goal NOT NULL,
    activity_level INTEGER DEFAULT 1 CHECK (activity_level >= 1 AND activity_level <= 5),
    health_conditions TEXT[] DEFAULT '{}',
    dietary_preferences TEXT[] DEFAULT '{}',
    allergies TEXT[] DEFAULT '{}',
    medications TEXT[] DEFAULT '{}',
    emergency_contact_name TEXT,
    emergency_contact_phone TEXT,
    target_weight DECIMAL(5,2),
    target_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trainer assignments (linking trainees to trainers)
CREATE TABLE public.trainer_assignments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainee_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    status assignment_status DEFAULT 'pending',
    start_date DATE DEFAULT CURRENT_DATE,
    end_date DATE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(trainee_id, trainer_id, status) -- Prevent duplicate active assignments
);

-- Subscriptions table
CREATE TABLE public.subscriptions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainee_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    plan_type TEXT NOT NULL, -- 'weekly', 'monthly', 'quarterly'
    price DECIMAL(10,2) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status subscription_status DEFAULT 'pending',
    auto_renew BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Payments table
CREATE TABLE public.payments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    subscription_id UUID REFERENCES public.subscriptions(id) ON DELETE CASCADE NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency TEXT DEFAULT 'SAR',
    payment_method TEXT, -- 'card', 'bank_transfer', 'wallet'
    payment_gateway TEXT, -- 'stripe', 'tap', 'paypal'
    transaction_id TEXT UNIQUE,
    status payment_status DEFAULT 'pending',
    paid_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Sessions table
CREATE TABLE public.sessions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainee_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    session_type TEXT DEFAULT 'training', -- 'training', 'consultation', 'assessment'
    scheduled_at TIMESTAMP WITH TIME ZONE NOT NULL,
    duration_minutes INTEGER DEFAULT 60,
    location TEXT,
    meeting_link TEXT,
    status session_status DEFAULT 'scheduled',
    notes TEXT,
    trainee_feedback TEXT,
    trainer_feedback TEXT,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Nutrition plans table
CREATE TABLE public.nutrition_plans (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainee_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    start_date DATE NOT NULL,
    end_date DATE,
    daily_calories INTEGER,
    daily_protein DECIMAL(5,2),
    daily_carbs DECIMAL(5,2),
    daily_fats DECIMAL(5,2),
    meals JSONB DEFAULT '[]',
    supplements JSONB DEFAULT '[]',
    instructions TEXT,
    is_active BOOLEAN DEFAULT true,
    completed BOOLEAN DEFAULT false,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Workout plans table
CREATE TABLE public.workout_plans (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainee_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    start_date DATE NOT NULL,
    end_date DATE,
    difficulty_level INTEGER DEFAULT 1 CHECK (difficulty_level >= 1 AND difficulty_level <= 5),
    exercises JSONB DEFAULT '[]',
    rest_days TEXT[] DEFAULT '{}',
    instructions TEXT,
    is_active BOOLEAN DEFAULT true,
    completed BOOLEAN DEFAULT false,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Progress tracking table
CREATE TABLE public.progress_tracking (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainee_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    weight DECIMAL(5,2),
    body_fat_percentage DECIMAL(4,2),
    muscle_mass DECIMAL(5,2),
    measurements JSONB DEFAULT '{}', -- chest, waist, hips, etc.
    photos TEXT[] DEFAULT '{}',
    notes TEXT,
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Reviews table
CREATE TABLE public.reviews (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trainee_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    trainer_id UUID REFERENCES public.trainers(id) ON DELETE CASCADE NOT NULL,
    session_id UUID REFERENCES public.sessions(id) ON DELETE SET NULL,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    is_anonymous BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(trainee_id, trainer_id, session_id)
);

-- Notifications table
CREATE TABLE public.notifications (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type notification_type DEFAULT 'general',
    data JSONB DEFAULT '{}',
    is_read BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Chat messages table
CREATE TABLE public.chat_messages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    sender_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    receiver_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    message TEXT NOT NULL,
    message_type TEXT DEFAULT 'text', -- 'text', 'image', 'file'
    file_url TEXT,
    is_read BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- System settings table
CREATE TABLE public.system_settings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    key TEXT UNIQUE NOT NULL,
    value JSONB NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
