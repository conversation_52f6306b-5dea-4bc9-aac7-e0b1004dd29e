import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../theme/app_theme.dart';

class PaymentsHistoryScreen extends StatefulWidget {
  const PaymentsHistoryScreen({super.key});

  @override
  State<PaymentsHistoryScreen> createState() => _PaymentsHistoryScreenState();
}

class _PaymentsHistoryScreenState extends State<PaymentsHistoryScreen> {
  List<dynamic> _payments = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPayments();
  }

  Future<void> _loadPayments() async {
    setState(() => _isLoading = true);
    final user = Supabase.instance.client.auth.currentUser;
    if (user == null) return;
    // أولاً: جلب جميع اشتراكات المستخدم
    final subs = await Supabase.instance.client
        .from('subscriptions')
        .select('id')
        .eq('trainee_id', user.id);
    final subIds = subs.map((s) => s['id']).toList();
    // ثانياً: جلب جميع المدفوعات المرتبطة بهذه الاشتراكات
    final response = await Supabase.instance.client
        .from('payments')
        .select('*, subscription:subscription_id(plan_type)')
        .inFilter('subscription_id', subIds)
        .order('created_at', ascending: false);
    setState(() {
      _payments = response;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('سجل المدفوعات')),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: AppTheme.primaryGold))
          : _payments.isEmpty
              ? const Center(child: Text('لا يوجد مدفوعات بعد'))
              : ListView.separated(
                  padding: const EdgeInsets.all(16),
                  itemCount: _payments.length,
                  separatorBuilder: (_, __) => const SizedBox(height: 16),
                  itemBuilder: (context, i) {
                    final pay = _payments[i];
                    return Card(
                      child: ListTile(
                        leading: const Icon(Icons.payment,
                            color: AppTheme.primaryGold),
                        title: Text(
                            'الخطة: ${pay['subscription']?['plan_type'] ?? '-'}'),
                        subtitle: Text(
                            'المبلغ: ${pay['amount']} ${pay['currency']}\nالحالة: ${pay['status']}'),
                        trailing: Text(pay['paid_at'] != null
                            ? pay['paid_at'].toString().substring(0, 16)
                            : '-'),
                      ),
                    );
                  },
                ),
    );
  }
}
