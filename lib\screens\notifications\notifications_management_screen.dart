import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/notification_model.dart';
import '../../widgets/custom_app_bar.dart';

class NotificationsManagementScreen extends StatefulWidget {
  final String userId;

  const NotificationsManagementScreen({
    super.key,
    required this.userId,
  });

  @override
  State<NotificationsManagementScreen> createState() =>
      _NotificationsManagementScreenState();
}

class _NotificationsManagementScreenState
    extends State<NotificationsManagementScreen> {
  List<AppNotification> _notifications = [];
  bool _isLoading = true;
  String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    _loadNotifications();
  }

  Future<void> _loadNotifications() async {
    setState(() => _isLoading = true);

    try {
      // تحميل الإشعارات من Supabase
      final response = await Supabase.instance.client
          .from('notifications')
          .select()
          .eq('user_id', widget.userId)
          .order('created_at', ascending: false);

      // تحويل البيانات إلى نماذج
      _notifications = (response as List)
          .map((data) => AppNotification.fromJson(data))
          .toList();

      // إذا لم توجد إشعارات، استخدم بيانات تجريبية
      if (_notifications.isEmpty) {
        _notifications = _generateSampleNotifications();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل الإشعارات: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  List<AppNotification> _generateSampleNotifications() {
    final now = DateTime.now();
    return [
      AppNotification(
        id: 'notif_1',
        userId: widget.userId,
        type: NotificationType.sessionReminder,
        title: 'تذكير بالجلسة',
        message: 'لديك جلسة تدريبية مع المدرب أحمد خلال ساعة',
        isRead: false,
        createdAt: now.subtract(const Duration(minutes: 30)),
        data: {'session_id': 'session_1', 'trainer_name': 'أحمد المدرب'},
      ),
      AppNotification(
        id: 'notif_2',
        userId: widget.userId,
        type: NotificationType.chatMessage,
        title: 'رسالة جديدة',
        message: 'وصلتك رسالة جديدة من المدرب محمد',
        isRead: false,
        createdAt: now.subtract(const Duration(hours: 2)),
        data: {'conversation_id': 'conv_1', 'sender_name': 'محمد الخبير'},
      ),
      AppNotification(
        id: 'notif_3',
        userId: widget.userId,
        type: NotificationType.planAssigned,
        title: 'خطة جديدة',
        message: 'تم إضافة خطة تغذية جديدة لك',
        isRead: true,
        createdAt: now.subtract(const Duration(hours: 5)),
        data: {'plan_id': 'plan_1', 'plan_name': 'خطة التضخيم'},
      ),
      AppNotification(
        id: 'notif_4',
        userId: widget.userId,
        type: NotificationType.paymentDue,
        title: 'انتهاء الاشتراك',
        message: 'سينتهي اشتراكك خلال 3 أيام',
        isRead: true,
        createdAt: now.subtract(const Duration(days: 1)),
        data: {'subscription_id': 'sub_1', 'expires_at': '2024-01-15'},
      ),
      AppNotification(
        id: 'notif_5',
        userId: widget.userId,
        type: NotificationType.general,
        title: 'تحديث التطبيق',
        message: 'متوفر إصدار جديد من التطبيق مع ميزات محسنة',
        isRead: true,
        createdAt: now.subtract(const Duration(days: 2)),
        data: {'version': '1.1.0'},
      ),
    ];
  }

  List<AppNotification> get _filteredNotifications {
    switch (_selectedFilter) {
      case 'unread':
        return _notifications.where((n) => !n.isRead).toList();
      case 'read':
        return _notifications.where((n) => n.isRead).toList();
      default:
        return _notifications;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: CustomAppBar(
        title: 'إدارة الإشعارات',
        showBackButton: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.mark_email_read),
            onPressed: _markAllAsRead,
            tooltip: 'تعليم الكل كمقروء',
          ),
          IconButton(
            icon: const Icon(Icons.delete_sweep),
            onPressed: _clearAllNotifications,
            tooltip: 'حذف جميع الإشعارات',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFilterTabs(),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredNotifications.isEmpty
                    ? _buildEmptyState()
                    : _buildNotificationsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterTabs() {
    return Container(
      color: Colors.white,
      child: Row(
        children: [
          _buildFilterTab('all', 'الكل', _notifications.length),
          _buildFilterTab('unread', 'غير مقروءة',
              _notifications.where((n) => !n.isRead).length),
          _buildFilterTab(
              'read', 'مقروءة', _notifications.where((n) => n.isRead).length),
        ],
      ),
    );
  }

  Widget _buildFilterTab(String filter, String label, int count) {
    final isSelected = _selectedFilter == filter;
    return Expanded(
      child: InkWell(
        onTap: () => setState(() => _selectedFilter = filter),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 16),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: isSelected ? Colors.blue : Colors.transparent,
                width: 2,
              ),
            ),
          ),
          child: Column(
            children: [
              Text(
                label,
                style: TextStyle(
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  color: isSelected ? Colors.blue : Colors.grey[600],
                ),
              ),
              const SizedBox(height: 4),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: isSelected ? Colors.blue : Colors.grey[300],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '$count',
                  style: TextStyle(
                    fontSize: 12,
                    color: isSelected ? Colors.white : Colors.grey[600],
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_off,
            size: 64,
            color: Colors.grey.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            _selectedFilter == 'unread'
                ? 'لا توجد إشعارات غير مقروءة'
                : _selectedFilter == 'read'
                    ? 'لا توجد إشعارات مقروءة'
                    : 'لا توجد إشعارات',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredNotifications.length,
      itemBuilder: (context, index) {
        final notification = _filteredNotifications[index];
        return _buildNotificationCard(notification);
      },
    );
  }

  Widget _buildNotificationCard(AppNotification notification) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color:
            notification.isRead ? Colors.white : Colors.blue.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        elevation: 2,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => _handleNotificationTap(notification),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                _buildNotificationIcon(notification.type),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              notification.title,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: notification.isRead
                                    ? FontWeight.normal
                                    : FontWeight.bold,
                              ),
                            ),
                          ),
                          Text(
                            _formatTime(notification.createdAt),
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        notification.message,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                PopupMenuButton<String>(
                  onSelected: (value) =>
                      _handleNotificationAction(notification, value),
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: notification.isRead ? 'mark_unread' : 'mark_read',
                      child: Text(notification.isRead
                          ? 'تعليم كغير مقروء'
                          : 'تعليم كمقروء'),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Text('حذف'),
                    ),
                  ],
                  child: const Icon(Icons.more_vert, color: Colors.grey),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationIcon(NotificationType type) {
    IconData icon;
    Color color;

    switch (type) {
      case NotificationType.planAssigned:
        icon = Icons.restaurant_menu;
        color = Colors.orange;
        break;
      case NotificationType.sessionReminder:
        icon = Icons.fitness_center;
        color = Colors.blue;
        break;
      case NotificationType.sessionBooked:
        icon = Icons.calendar_today;
        color = Colors.teal;
        break;
      case NotificationType.paymentDue:
        icon = Icons.card_membership;
        color = Colors.purple;
        break;
      case NotificationType.chatMessage:
        icon = Icons.message;
        color = Colors.green;
        break;
      case NotificationType.reviewReceived:
        icon = Icons.star;
        color = Colors.amber;
        break;
      case NotificationType.general:
      default:
        icon = Icons.info;
        color = Colors.grey;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        shape: BoxShape.circle,
      ),
      child: Icon(icon, color: color, size: 20),
    );
  }

  void _handleNotificationTap(AppNotification notification) {
    if (!notification.isRead) {
      _markAsRead(notification);
    }

    // TODO: التنقل حسب نوع الإشعار
    // مثال: إذا كان إشعار جلسة، انتقل لشاشة الجلسات
    // إذا كان إشعار رسالة، انتقل للمحادثة
  }

  void _handleNotificationAction(AppNotification notification, String action) {
    switch (action) {
      case 'mark_read':
        _markAsRead(notification);
        break;
      case 'mark_unread':
        _markAsUnread(notification);
        break;
      case 'delete':
        _deleteNotification(notification);
        break;
    }
  }

  Future<void> _markAsRead(AppNotification notification) async {
    setState(() {
      final index = _notifications.indexWhere((n) => n.id == notification.id);
      if (index != -1) {
        _notifications[index] = notification.copyWith(isRead: true);
      }
    });

    // تحديث في قاعدة البيانات
    try {
      await Supabase.instance.client.from('notifications').update({
        'is_read': true,
        'read_at': DateTime.now().toIso8601String()
      }).eq('id', notification.id);
    } catch (e) {
      // في حالة الخطأ، إرجاع الحالة
      setState(() {
        final index = _notifications.indexWhere((n) => n.id == notification.id);
        if (index != -1) {
          _notifications[index] = notification.copyWith(isRead: false);
        }
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحديث الإشعار: $e')),
        );
      }
    }
  }

  void _markAsUnread(AppNotification notification) {
    setState(() {
      final index = _notifications.indexWhere((n) => n.id == notification.id);
      if (index != -1) {
        _notifications[index] = notification.copyWith(isRead: false);
      }
    });
    // TODO: تحديث في قاعدة البيانات
  }

  void _deleteNotification(AppNotification notification) {
    setState(() {
      _notifications.removeWhere((n) => n.id == notification.id);
    });
    // TODO: حذف من قاعدة البيانات

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم حذف الإشعار')),
    );
  }

  void _markAllAsRead() {
    setState(() {
      _notifications =
          _notifications.map((n) => n.copyWith(isRead: true)).toList();
    });
    // TODO: تحديث جميع الإشعارات في قاعدة البيانات

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تعليم جميع الإشعارات كمقروءة')),
    );
  }

  void _clearAllNotifications() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف جميع الإشعارات؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _notifications.clear();
              });
              // TODO: حذف جميع الإشعارات من قاعدة البيانات
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم حذف جميع الإشعارات')),
              );
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else {
      return '${time.day}/${time.month}';
    }
  }
}
