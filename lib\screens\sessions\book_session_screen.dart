import 'package:flutter/material.dart';
import '../../main.dart';
import '../../theme/app_theme.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/loading_widget.dart';
import '../../widgets/premium_card.dart';
import '../../widgets/premium_button.dart';

class BookSessionScreen extends StatefulWidget {
  const BookSessionScreen({super.key});

  @override
  State<BookSessionScreen> createState() => _BookSessionScreenState();
}

class _BookSessionScreenState extends State<BookSessionScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _locationController = TextEditingController();

  String? _selectedTrainerId;
  DateTime? _selectedDate;
  TimeOfDay? _selectedTime;
  int _duration = 60;
  String _sessionType = 'training';
  bool _isLoading = false;

  List<Map<String, dynamic>> _trainers = [];

  @override
  void initState() {
    super.initState();
    _loadTrainers();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _locationController.dispose();
    super.dispose();
  }

  Future<void> _loadTrainers() async {
    try {
      final response = await supabase.from('trainers').select('''
            id,
            price_per_session,
            users:user_id (
              full_name,
              avatar_url
            )
          ''').eq('is_available', true);

      setState(() {
        _trainers = response;
      });
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل المدربين: $error'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 90)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppTheme.primaryGold,
            ),
          ),
          child: child!,
        );
      },
    );

    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
    }
  }

  Future<void> _selectTime() async {
    final time = await showTimePicker(
      context: context,
      initialTime: const TimeOfDay(hour: 9, minute: 0),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppTheme.primaryGold,
            ),
          ),
          child: child!,
        );
      },
    );

    if (time != null) {
      setState(() {
        _selectedTime = time;
      });
    }
  }

  Future<void> _bookSession() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedTrainerId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('الرجاء اختيار المدرب'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }
    if (_selectedDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('الرجاء اختيار التاريخ'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }
    if (_selectedTime == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('الرجاء اختيار الوقت'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final scheduledAt = DateTime(
        _selectedDate!.year,
        _selectedDate!.month,
        _selectedDate!.day,
        _selectedTime!.hour,
        _selectedTime!.minute,
      );

      final traineeId = supabase.auth.currentUser!.id;

      await supabase.from('sessions').insert({
        'trainee_id': traineeId,
        'trainer_id': _selectedTrainerId,
        'title': _titleController.text.trim(),
        'description': _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        'session_type': _sessionType,
        'scheduled_at': scheduledAt.toIso8601String(),
        'duration_minutes': _duration,
        'location': _locationController.text.trim().isEmpty
            ? null
            : _locationController.text.trim(),
        'status': 'scheduled',
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حجز الجلسة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حجز الجلسة: $error'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: Column(
          children: [
            _buildPremiumHeader(),
            Expanded(
              child: _isLoading
                  ? const LoadingWidget()
                  : SingleChildScrollView(
                      padding: const EdgeInsets.all(20),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildTrainerSelectionSection(),
                            const SizedBox(height: 24),
                            _buildSessionDetailsSection(),
                            const SizedBox(height: 24),
                            _buildDateTimeSection(),
                            const SizedBox(height: 32),
                            PremiumButton(
                              text: 'حجز الجلسة',
                              onPressed: _bookSession,
                              isLoading: _isLoading,
                              width: double.infinity,
                              height: 60,
                              icon: Icons.calendar_today,
                            ),
                            const SizedBox(height: 20),
                          ],
                        ),
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPremiumHeader() {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
      child: Row(
        children: [
          PremiumIconButton(
            icon: Icons.arrow_back_ios,
            onPressed: () => Navigator.pop(context),
            backgroundColor: AppTheme.surfaceLight,
            iconColor: AppTheme.primaryGold,
            size: 48,
            hasGradient: false,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'حجز جلسة جديدة',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        color: AppTheme.textPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 4),
                Text(
                  'احجز جلستك التدريبية مع أفضل المدربين',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppTheme.textSecondary,
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 24,
            decoration: BoxDecoration(
              gradient: AppTheme.primaryGradient,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 12),
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimary,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrainerSelectionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('اختيار المدرب'),
        _buildTrainerSelection(),
      ],
    );
  }

  Widget _buildSessionDetailsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('تفاصيل الجلسة'),
        _buildSessionDetailsForm(),
      ],
    );
  }

  Widget _buildDateTimeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('التوقيت'),
        _buildDateTimeSelection(),
      ],
    );
  }

  Widget _buildTrainerSelection() {
    if (_trainers.isEmpty) {
      return PremiumCard(
        child: Column(
          children: [
            Icon(
              Icons.search_off,
              size: 48,
              color: AppTheme.textMuted,
            ),
            const SizedBox(height: 16),
            Text(
              'لا يوجد مدربون متاحون حالياً',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppTheme.textSecondary,
                  ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: _trainers.map((trainer) {
        final isSelected = _selectedTrainerId == trainer['id'];
        final trainerName = trainer['users']['full_name'];
        final trainerAvatar = trainer['users']['avatar_url'];
        final price = trainer['price_per_session'];

        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: PremiumCard(
            onTap: () {
              setState(() {
                _selectedTrainerId = trainer['id'];
              });
            },
            isSelected: isSelected,
            hasBorder: true,
            borderColor: isSelected ? AppTheme.primaryGold : null,
            child: Row(
              children: [
                Hero(
                  tag: 'trainer_${trainer['id']}',
                  child: Container(
                    width: 64,
                    height: 64,
                    decoration: BoxDecoration(
                      gradient: isSelected
                          ? AppTheme.primaryGradient
                          : LinearGradient(
                              colors: [
                                AppTheme.primaryGold.withValues(alpha: 0.1),
                                AppTheme.primaryGold.withValues(alpha: 0.05),
                              ],
                            ),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: isSelected
                            ? Colors.white
                            : AppTheme.primaryGold.withValues(alpha: 0.3),
                        width: 2,
                      ),
                    ),
                    child: trainerAvatar != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(14),
                            child: Image.network(
                              trainerAvatar,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  Icon(
                                Icons.person,
                                color: isSelected
                                    ? Colors.white
                                    : AppTheme.primaryGold,
                                size: 32,
                              ),
                            ),
                          )
                        : Icon(
                            Icons.person,
                            color: isSelected
                                ? Colors.white
                                : AppTheme.primaryGold,
                            size: 32,
                          ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        trainerName,
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: isSelected
                                      ? AppTheme.primaryGold
                                      : AppTheme.textPrimary,
                                ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            Icons.attach_money,
                            size: 16,
                            color: AppTheme.emeraldGreen,
                          ),
                          Text(
                            '${price.toStringAsFixed(0)} ريال للجلسة',
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  color: AppTheme.textSecondary,
                                  fontWeight: FontWeight.w500,
                                ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            Icons.star,
                            size: 14,
                            color: AppTheme.warmOrange,
                          ),
                          Text(
                            ' 4.8 (127 تقييم)',
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: AppTheme.textMuted,
                                    ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                if (isSelected)
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: AppTheme.emeraldGreen,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildSessionDetailsForm() {
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            TextFormField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'عنوان الجلسة',
                hintText: 'مثال: جلسة تدريب كمال أجسام',
                prefixIcon: Icon(Icons.title, color: AppTheme.primaryGold),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'الرجاء إدخال عنوان الجلسة';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _sessionType,
              decoration: const InputDecoration(
                labelText: 'نوع الجلسة',
                prefixIcon: Icon(Icons.category, color: AppTheme.primaryGold),
              ),
              items: const [
                DropdownMenuItem(value: 'training', child: Text('تدريب')),
                DropdownMenuItem(value: 'consultation', child: Text('استشارة')),
                DropdownMenuItem(value: 'assessment', child: Text('تقييم')),
              ],
              onChanged: (value) {
                setState(() {
                  _sessionType = value!;
                });
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'وصف الجلسة (اختياري)',
                hintText: 'اكتب تفاصيل إضافية عن الجلسة',
                prefixIcon:
                    Icon(Icons.description, color: AppTheme.primaryGold),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<int>(
              value: _duration,
              decoration: const InputDecoration(
                labelText: 'مدة الجلسة',
                prefixIcon: Icon(Icons.timer, color: AppTheme.primaryGold),
              ),
              items: const [
                DropdownMenuItem(value: 30, child: Text('30 دقيقة')),
                DropdownMenuItem(value: 60, child: Text('60 دقيقة')),
                DropdownMenuItem(value: 90, child: Text('90 دقيقة')),
                DropdownMenuItem(value: 120, child: Text('120 دقيقة')),
              ],
              onChanged: (value) {
                setState(() {
                  _duration = value!;
                });
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _locationController,
              decoration: const InputDecoration(
                labelText: 'مكان الجلسة (اختياري)',
                hintText: 'الصالة الرياضية، المنزل، أونلاين',
                prefixIcon:
                    Icon(Icons.location_on, color: AppTheme.primaryGold),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateTimeSelection() {
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            ListTile(
              leading:
                  const Icon(Icons.calendar_today, color: AppTheme.primaryGold),
              title: const Text('التاريخ'),
              subtitle: Text(
                _selectedDate != null
                    ? '${_selectedDate!.day}/${_selectedDate!.month}/${_selectedDate!.year}'
                    : 'اختر التاريخ',
                style: TextStyle(
                  color: _selectedDate != null
                      ? AppTheme.textPrimary
                      : AppTheme.textSecondary,
                ),
              ),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: _selectDate,
            ),
            const Divider(),
            ListTile(
              leading:
                  const Icon(Icons.access_time, color: AppTheme.primaryGold),
              title: const Text('الوقت'),
              subtitle: Text(
                _selectedTime != null
                    ? _formatTime(_selectedTime!)
                    : 'اختر الوقت',
                style: TextStyle(
                  color: _selectedTime != null
                      ? AppTheme.textPrimary
                      : AppTheme.textSecondary,
                ),
              ),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: _selectTime,
            ),
          ],
        ),
      ),
    );
  }

  String _formatTime(TimeOfDay time) {
    final hour = time.hour;
    final minute = time.minute.toString().padLeft(2, '0');
    final period = hour >= 12 ? 'م' : 'ص';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
    return '$displayHour:$minute $period';
  }
}
