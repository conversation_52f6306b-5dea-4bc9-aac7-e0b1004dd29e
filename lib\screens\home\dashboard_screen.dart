import 'package:flutter/material.dart';
import '../../main.dart';
import '../../theme/app_theme.dart';
import '../../widgets/premium_card.dart' hide PremiumIconCard;
import '../../widgets/premium_widgets.dart';
import '../../widgets/animated_counter.dart' show PulsingWidget, GlowingWidget;
import '../trainers/trainers_screen.dart';
import '../plans/plans_screen.dart';
import '../sessions/sessions_screen.dart';
import 'notifications_screen.dart';
import '../subscriptions/subscriptions_screen.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen>
    with TickerProviderStateMixin {
  int _unreadNotifications = 3;
  List<Map<String, dynamic>> _recentSessions = [];
  Map<String, int> _userStats = {};

  late AnimationController _slideController;
  late AnimationController _fadeController;

  @override
  void initState() {
    super.initState();
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _loadUserData();
    _startAnimations();
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  void _startAnimations() {
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        _slideController.forward();
        _fadeController.forward();
      }
    });
  }

  Future<void> _loadUserData() async {
    try {
      // Load user sessions and stats
      final response = await supabase
          .from('sessions')
          .select('*, trainers(users(full_name))')
          .eq('trainee_id', supabase.auth.currentUser!.id)
          .order('created_at', ascending: false)
          .limit(3);

      if (mounted) {
        setState(() {
          _recentSessions = List<Map<String, dynamic>>.from(response);
          _userStats = {
            'sessions_this_month': 12,
            'calories_burned': 2450,
            'goals_achieved': 8,
            'active_days': 24,
          };
        });
      }
    } catch (error) {
      debugPrint('Error loading user data: $error');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildPremiumHeader(),
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildWelcomeSection(),
                      const SizedBox(height: 24),
                      _buildStatsGrid(),
                      const SizedBox(height: 24),
                      _buildQuickActionsGrid(),
                      const SizedBox(height: 24),
                      _buildRecentActivitySection(),
                      const SizedBox(height: 24),
                      _buildMotivationalSection(),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPremiumHeader() {
    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(0, -1),
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: _slideController,
        curve: Curves.easeOutBack,
      )),
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            GlowingWidget(
              child: Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: AppTheme.primaryShadow,
                ),
                child: const Icon(
                  Icons.fitness_center,
                  color: Colors.black,
                  size: 26,
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'FitGold',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimary,
                        ),
                  ),
                  Text(
                    'رحلتك نحو اللياقة المثالية',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textSecondary,
                        ),
                  ),
                ],
              ),
            ),
            PremiumCard(
              padding: const EdgeInsets.all(12),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const NotificationsScreen(),
                  ),
                );
              },
              child: Stack(
                children: [
                  const Icon(
                    Icons.notifications_outlined,
                    color: AppTheme.primaryGold,
                    size: 24,
                  ),
                  if (_unreadNotifications > 0)
                    Positioned(
                      top: 0,
                      right: 0,
                      child: Container(
                        width: 8,
                        height: 8,
                        decoration: const BoxDecoration(
                          color: AppTheme.coralRed,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return FadeTransition(
      opacity: _fadeController,
      child: PremiumCard(
        hasGradient: true,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'مرحباً بك!',
                        style:
                            Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  color: AppTheme.textLight,
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'لنبدأ تحقيق أهدافك اليوم',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color: AppTheme.textLight.withOpacity(0.9),
                            ),
                      ),
                    ],
                  ),
                ),
                PulsingWidget(
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: const Icon(
                      Icons.emoji_events,
                      color: Colors.white,
                      size: 40,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsGrid() {
    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(-1, 0),
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: _slideController,
        curve: Curves.easeOutCubic,
      )),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إحصائياتك',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimary,
                ),
          ),
          const SizedBox(height: 16),
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            mainAxisSpacing: 16,
            crossAxisSpacing: 16,
            childAspectRatio: 1.4,
            children: [
              PremiumStatCard(
                title: 'الجلسات هذا الشهر',
                value: '${_userStats['sessions_this_month'] ?? 0}',
                icon: Icons.fitness_center,
                color: AppTheme.lightBlue,
              ),
              PremiumStatCard(
                title: 'السعرات المحروقة',
                value: '${_userStats['calories_burned'] ?? 0}',
                icon: Icons.local_fire_department,
                color: AppTheme.warmOrange,
              ),
              PremiumStatCard(
                title: 'الأهداف المحققة',
                value: '${_userStats['goals_achieved'] ?? 0}/10',
                icon: Icons.emoji_events,
                color: AppTheme.emeraldGreen,
              ),
              PremiumStatCard(
                title: 'أيام النشاط',
                value: '${_userStats['active_days'] ?? 0}',
                icon: Icons.calendar_today,
                color: AppTheme.primaryGold,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionsGrid() {
    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(1, 0),
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: _slideController,
        curve: Curves.easeOutCubic,
      )),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الإجراءات السريعة',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimary,
                ),
          ),
          const SizedBox(height: 16),
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            mainAxisSpacing: 16,
            crossAxisSpacing: 16,
            childAspectRatio: 1.2,
            children: [
              PremiumIconCard(
                icon: Icons.calendar_today,
                title: 'جلساتي',
                subtitle: 'إدارة جلساتك التدريبية',
                onTap: () => Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => const SessionsScreen()),
                ),
              ),
              PremiumIconCard(
                icon: Icons.person_outline,
                title: 'المدربين',
                subtitle: 'اختر مدربك المفضل',
                onTap: () => Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => const TrainersScreen()),
                ),
              ),
              PremiumIconCard(
                icon: Icons.restaurant_menu,
                title: 'خطط التغذية',
                subtitle: 'خطط غذائية متوازنة',
                onTap: () => Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const PlansScreen()),
                ),
              ),
              PremiumIconCard(
                icon: Icons.card_membership,
                title: 'الاشتراكات',
                subtitle: 'إدارة اشتراكاتك',
                onTap: () => Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => const SubscriptionsScreen()),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivitySection() {
    return FadeTransition(
      opacity: _fadeController,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'النشاط الأخير',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary,
                    ),
              ),
              TextButton(
                onPressed: () => Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => const SessionsScreen()),
                ),
                child: const Text(
                  'عرض الكل',
                  style: TextStyle(color: AppTheme.primaryGold),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (_recentSessions.isEmpty)
            PremiumCard(
              child: Column(
                children: [
                  Icon(
                    Icons.hourglass_empty,
                    size: 48,
                    color: AppTheme.textMuted,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد جلسات حديثة',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: AppTheme.textSecondary,
                        ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'احجز جلستك الأولى الآن!',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textMuted,
                        ),
                  ),
                ],
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _recentSessions.length,
              itemBuilder: (context, index) {
                final session = _recentSessions[index];
                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: PremiumCard(
                    child: ListTile(
                      leading: Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          gradient: AppTheme.primaryGradient,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.fitness_center,
                          color: Colors.black,
                          size: 24,
                        ),
                      ),
                      title: Text(
                        session['title'] ?? 'جلسة تدريبية',
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                      subtitle: Text(
                        'مع ${session['trainers']?['users']?['full_name'] ?? 'مدرب'}',
                        style: const TextStyle(
                          color: AppTheme.textSecondary,
                        ),
                      ),
                      trailing: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: AppTheme.emeraldGreen.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Text(
                          'مكتملة',
                          style: TextStyle(
                            color: AppTheme.emeraldGreen,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
        ],
      ),
    );
  }

  Widget _buildMotivationalSection() {
    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(0, 1),
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: _slideController,
        curve: Curves.easeOutBack,
      )),
      child: PremiumCard(
        hasGradient: true,
        gradientColors: [
          AppTheme.lightBlue.withOpacity(0.8),
          AppTheme.primaryGold.withOpacity(0.6),
          AppTheme.emeraldGreen.withOpacity(0.8),
        ],
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '💪 استمر في التقدم!',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: AppTheme.textLight,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'أنت في طريقك لتحقيق أهدافك. كل خطوة تقربك من النجاح!',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textLight.withOpacity(0.9),
                        ),
                  ),
                ],
              ),
            ),
            PulsingWidget(
              child: Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: const Icon(
                  Icons.rocket_launch,
                  color: Colors.white,
                  size: 30,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
