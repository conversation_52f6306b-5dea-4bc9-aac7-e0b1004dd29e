enum MessageType {
  text,
  image,
  file,
  voice,
  video,
}

class ChatMessage {
  final String id;
  final String senderId;
  final String receiverId;
  final String message;
  final MessageType messageType;
  final String? fileUrl;
  final bool isRead;
  final DateTime createdAt;
  
  // بيانات إضافية للعرض
  final Map<String, dynamic>? senderData;
  final Map<String, dynamic>? receiverData;

  ChatMessage({
    required this.id,
    required this.senderId,
    required this.receiverId,
    required this.message,
    this.messageType = MessageType.text,
    this.fileUrl,
    this.isRead = false,
    required this.createdAt,
    this.senderData,
    this.receiverData,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: _getSafeString(json, 'id', ''),
      senderId: _getSafeString(json, 'sender_id', ''),
      receiverId: _getSafeString(json, 'receiver_id', ''),
      message: _getSafeString(json, 'message', ''),
      messageType: _getMessageType(json['message_type']),
      fileUrl: _getSafeString(json, 'file_url'),
      isRead: _getSafeBool(json, 'is_read', false),
      createdAt: _getSafeDateTime(json, 'created_at') ?? DateTime.now(),
      senderData: _getSafeMap(json, 'sender_data'),
      receiverData: _getSafeMap(json, 'receiver_data'),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'sender_id': senderId,
      'receiver_id': receiverId,
      'message': message,
      'message_type': messageType.name,
      'file_url': fileUrl,
      'is_read': isRead,
      'created_at': createdAt.toIso8601String(),
    };
  }

  // دوال مساعدة آمنة لتحويل البيانات
  static String _getSafeString(Map<String, dynamic> data, String key, [String? defaultValue]) {
    final value = data[key];
    if (value == null) return defaultValue ?? '';
    return value.toString();
  }

  static bool _getSafeBool(Map<String, dynamic> data, String key, bool defaultValue) {
    final value = data[key];
    if (value == null) return defaultValue;
    if (value is bool) return value;
    if (value is String) {
      return value.toLowerCase() == 'true';
    }
    return defaultValue;
  }

  static Map<String, dynamic>? _getSafeMap(Map<String, dynamic> data, String key) {
    final value = data[key];
    if (value == null) return null;
    if (value is Map<String, dynamic>) return value;
    if (value is Map) return Map<String, dynamic>.from(value);
    return null;
  }

  static DateTime? _getSafeDateTime(Map<String, dynamic> data, String key) {
    final value = data[key];
    if (value == null) return null;
    if (value is String) {
      return DateTime.tryParse(value);
    }
    return null;
  }

  static MessageType _getMessageType(dynamic value) {
    if (value == null) return MessageType.text;
    final typeString = value.toString().toLowerCase();
    
    switch (typeString) {
      case 'image':
        return MessageType.image;
      case 'file':
        return MessageType.file;
      case 'voice':
        return MessageType.voice;
      case 'video':
        return MessageType.video;
      default:
        return MessageType.text;
    }
  }

  // دالة للحصول على اسم المرسل
  String get senderName {
    if (senderData != null) {
      return _getSafeString(senderData!, 'full_name', 'مستخدم');
    }
    return 'مستخدم';
  }

  // دالة للحصول على صورة المرسل
  String? get senderAvatar {
    if (senderData != null) {
      return _getSafeString(senderData!, 'avatar_url');
    }
    return null;
  }

  // دالة للحصول على اسم المستقبل
  String get receiverName {
    if (receiverData != null) {
      return _getSafeString(receiverData!, 'full_name', 'مستخدم');
    }
    return 'مستخدم';
  }

  // دالة للحصول على صورة المستقبل
  String? get receiverAvatar {
    if (receiverData != null) {
      return _getSafeString(receiverData!, 'avatar_url');
    }
    return null;
  }

  // دالة للتحقق من كون الرسالة تحتوي على ملف
  bool get hasFile => fileUrl != null && fileUrl!.isNotEmpty;

  // دالة للحصول على نوع الملف
  String? get fileExtension {
    if (!hasFile) return null;
    return fileUrl!.split('.').last.toLowerCase();
  }

  // دالة للتحقق من كون الملف صورة
  bool get isImageFile {
    if (!hasFile) return false;
    final ext = fileExtension;
    return ext != null && ['jpg', 'jpeg', 'png', 'gif', 'webp'].contains(ext);
  }

  // دالة للتحقق من كون الملف فيديو
  bool get isVideoFile {
    if (!hasFile) return false;
    final ext = fileExtension;
    return ext != null && ['mp4', 'avi', 'mov', 'wmv', 'flv'].contains(ext);
  }

  // دالة لتنسيق وقت الرسالة
  String get formattedTime {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  // دالة لنسخ الكائن مع تعديل بعض القيم
  ChatMessage copyWith({
    String? id,
    String? senderId,
    String? receiverId,
    String? message,
    MessageType? messageType,
    String? fileUrl,
    bool? isRead,
    DateTime? createdAt,
    Map<String, dynamic>? senderData,
    Map<String, dynamic>? receiverData,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      senderId: senderId ?? this.senderId,
      receiverId: receiverId ?? this.receiverId,
      message: message ?? this.message,
      messageType: messageType ?? this.messageType,
      fileUrl: fileUrl ?? this.fileUrl,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
      senderData: senderData ?? this.senderData,
      receiverData: receiverData ?? this.receiverData,
    );
  }

  @override
  String toString() {
    return 'ChatMessage(id: $id, senderId: $senderId, message: $message, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatMessage && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// فئة لتمثيل محادثة
class ChatConversation {
  final String id;
  final String participantId;
  final String participantName;
  final String? participantAvatar;
  final ChatMessage? lastMessage;
  final int unreadCount;
  final DateTime lastActivity;

  ChatConversation({
    required this.id,
    required this.participantId,
    required this.participantName,
    this.participantAvatar,
    this.lastMessage,
    this.unreadCount = 0,
    required this.lastActivity,
  });

  factory ChatConversation.fromMessages(
    String participantId,
    String participantName,
    String? participantAvatar,
    List<ChatMessage> messages,
    String currentUserId,
  ) {
    final unreadMessages = messages.where((msg) => 
      !msg.isRead && msg.senderId != currentUserId
    ).length;

    return ChatConversation(
      id: participantId,
      participantId: participantId,
      participantName: participantName,
      participantAvatar: participantAvatar,
      lastMessage: messages.isNotEmpty ? messages.first : null,
      unreadCount: unreadMessages,
      lastActivity: messages.isNotEmpty ? messages.first.createdAt : DateTime.now(),
    );
  }
}
