enum NotificationType {
  planAssigned,
  sessionReminder,
  sessionBooked,
  paymentDue,
  general,
  chatMessage,
  reviewReceived,
}

class AppNotification {
  final String id;
  final String userId;
  final String title;
  final String message;
  final NotificationType type;
  final Map<String, dynamic> data;
  final bool isRead;
  final DateTime createdAt;

  AppNotification({
    required this.id,
    required this.userId,
    required this.title,
    required this.message,
    required this.type,
    this.data = const {},
    this.isRead = false,
    required this.createdAt,
  });

  factory AppNotification.fromJson(Map<String, dynamic> json) {
    return AppNotification(
      id: _getSafeString(json, 'id', ''),
      userId: _getSafeString(json, 'user_id', ''),
      title: _getSafeString(json, 'title', ''),
      message: _getSafeString(json, 'message', ''),
      type: _getNotificationType(json['type']),
      data: _getSafeMap(json, 'data'),
      isRead: _getSafeBool(json, 'is_read', false),
      createdAt: _getSafeDateTime(json, 'created_at') ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'title': title,
      'message': message,
      'type': type.name,
      'data': data,
      'is_read': isRead,
      'created_at': createdAt.toIso8601String(),
    };
  }

  // دوال مساعدة آمنة لتحويل البيانات
  static String _getSafeString(Map<String, dynamic> data, String key, [String? defaultValue]) {
    final value = data[key];
    if (value == null) return defaultValue ?? '';
    return value.toString();
  }

  static bool _getSafeBool(Map<String, dynamic> data, String key, bool defaultValue) {
    final value = data[key];
    if (value == null) return defaultValue;
    if (value is bool) return value;
    if (value is String) {
      return value.toLowerCase() == 'true';
    }
    return defaultValue;
  }

  static Map<String, dynamic> _getSafeMap(Map<String, dynamic> data, String key) {
    final value = data[key];
    if (value == null) return {};
    if (value is Map<String, dynamic>) return value;
    if (value is Map) return Map<String, dynamic>.from(value);
    return {};
  }

  static DateTime? _getSafeDateTime(Map<String, dynamic> data, String key) {
    final value = data[key];
    if (value == null) return null;
    if (value is String) {
      return DateTime.tryParse(value);
    }
    return null;
  }

  static NotificationType _getNotificationType(dynamic value) {
    if (value == null) return NotificationType.general;
    final typeString = value.toString().toLowerCase();
    
    switch (typeString) {
      case 'plan_assigned':
        return NotificationType.planAssigned;
      case 'session_reminder':
        return NotificationType.sessionReminder;
      case 'session_booked':
        return NotificationType.sessionBooked;
      case 'payment_due':
        return NotificationType.paymentDue;
      case 'chat_message':
        return NotificationType.chatMessage;
      case 'review_received':
        return NotificationType.reviewReceived;
      default:
        return NotificationType.general;
    }
  }

  // دالة للحصول على أيقونة الإشعار
  String get iconPath {
    switch (type) {
      case NotificationType.planAssigned:
        return 'assets/icons/plan.png';
      case NotificationType.sessionReminder:
        return 'assets/icons/session.png';
      case NotificationType.sessionBooked:
        return 'assets/icons/calendar.png';
      case NotificationType.paymentDue:
        return 'assets/icons/payment.png';
      case NotificationType.chatMessage:
        return 'assets/icons/chat.png';
      case NotificationType.reviewReceived:
        return 'assets/icons/star.png';
      default:
        return 'assets/icons/notification.png';
    }
  }

  // دالة للحصول على لون الإشعار
  String get colorHex {
    switch (type) {
      case NotificationType.planAssigned:
        return '#4CAF50'; // أخضر
      case NotificationType.sessionReminder:
        return '#FF9800'; // برتقالي
      case NotificationType.sessionBooked:
        return '#2196F3'; // أزرق
      case NotificationType.paymentDue:
        return '#F44336'; // أحمر
      case NotificationType.chatMessage:
        return '#9C27B0'; // بنفسجي
      case NotificationType.reviewReceived:
        return '#FFD700'; // ذهبي
      default:
        return '#757575'; // رمادي
    }
  }

  // دالة لتنسيق وقت الإشعار
  String get formattedTime {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  // دالة لنسخ الكائن مع تعديل بعض القيم
  AppNotification copyWith({
    String? id,
    String? userId,
    String? title,
    String? message,
    NotificationType? type,
    Map<String, dynamic>? data,
    bool? isRead,
    DateTime? createdAt,
  }) {
    return AppNotification(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      data: data ?? this.data,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'AppNotification(id: $id, title: $title, type: $type, isRead: $isRead, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppNotification && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// فئة لإنشاء إشعارات جديدة
class NotificationBuilder {
  static AppNotification createPlanAssignedNotification({
    required String userId,
    required String planTitle,
    required String trainerName,
  }) {
    return AppNotification(
      id: '', // سيتم إنشاؤه في قاعدة البيانات
      userId: userId,
      title: 'تم تعيين خطة جديدة',
      message: 'تم تعيين خطة "$planTitle" من المدرب $trainerName',
      type: NotificationType.planAssigned,
      data: {
        'plan_title': planTitle,
        'trainer_name': trainerName,
      },
      createdAt: DateTime.now(),
    );
  }

  static AppNotification createSessionReminderNotification({
    required String userId,
    required String sessionTitle,
    required DateTime sessionTime,
  }) {
    return AppNotification(
      id: '',
      userId: userId,
      title: 'تذكير بالجلسة',
      message: 'لديك جلسة "$sessionTitle" خلال ساعة',
      type: NotificationType.sessionReminder,
      data: {
        'session_title': sessionTitle,
        'session_time': sessionTime.toIso8601String(),
      },
      createdAt: DateTime.now(),
    );
  }

  static AppNotification createChatMessageNotification({
    required String userId,
    required String senderName,
    required String messagePreview,
  }) {
    return AppNotification(
      id: '',
      userId: userId,
      title: 'رسالة جديدة',
      message: '$senderName: $messagePreview',
      type: NotificationType.chatMessage,
      data: {
        'sender_name': senderName,
        'message_preview': messagePreview,
      },
      createdAt: DateTime.now(),
    );
  }

  static AppNotification createReviewReceivedNotification({
    required String userId,
    required String reviewerName,
    required int rating,
  }) {
    return AppNotification(
      id: '',
      userId: userId,
      title: 'تقييم جديد',
      message: 'تلقيت تقييم $rating نجوم من $reviewerName',
      type: NotificationType.reviewReceived,
      data: {
        'reviewer_name': reviewerName,
        'rating': rating,
      },
      createdAt: DateTime.now(),
    );
  }
}
