import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import '../../main.dart';
import '../../theme/app_theme.dart';
import '../../models/workout_plan_model.dart';
import '../../widgets/premium_widgets.dart';
import '../../widgets/animated_widgets.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/media_viewer_widget.dart';

class WorkoutPlanDetailsScreen extends StatefulWidget {
  final String planId;

  const WorkoutPlanDetailsScreen({
    super.key,
    required this.planId,
  });

  @override
  State<WorkoutPlanDetailsScreen> createState() =>
      _WorkoutPlanDetailsScreenState();
}

class _WorkoutPlanDetailsScreenState extends State<WorkoutPlanDetailsScreen> {
  late Future<WorkoutPlan> _planFuture;
  Map<String, VideoPlayerController> _videoControllers = {};
  PageController _mediaPageController = PageController();
  int _currentMediaIndex = 0;

  @override
  void initState() {
    super.initState();
    _loadPlan();
  }

  @override
  void dispose() {
    // تنظيف مشغلات الفيديو
    _videoControllers.values.forEach((controller) {
      controller.dispose();
    });
    _mediaPageController.dispose();
    super.dispose();
  }

  void _loadPlan() {
    _planFuture = _fetchPlan();
  }

  Future<WorkoutPlan> _fetchPlan() async {
    final response = await supabase.from('workout_plans').select('''
          *,
          workout_media (*)
        ''').eq('id', widget.planId).single();

    return WorkoutPlan.fromJson(response);
  }

  Future<void> _initializeVideoController(String url) async {
    if (_videoControllers.containsKey(url)) return;

    final controller = VideoPlayerController.networkUrl(Uri.parse(url));
    await controller.initialize();
    _videoControllers[url] = controller;
    if (mounted) setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: SafeArea(
          child: FutureBuilder<WorkoutPlan>(
            future: _planFuture,
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return _buildLoadingState();
              }
              if (snapshot.hasError) {
                return _buildErrorState();
              }

              final plan = snapshot.data!;
              return _buildPlanDetails(plan);
            },
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryGold),
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: AppTheme.coralRed,
          ),
          const SizedBox(height: 16),
          const Text(
            'حدث خطأ في تحميل الخطة',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimary,
            ),
          ),
          const SizedBox(height: 24),
          CustomButton(
            text: 'إعادة المحاولة',
            onPressed: _loadPlan,
            backgroundColor: AppTheme.primaryGold,
            textColor: Colors.black,
          ),
        ],
      ),
    );
  }

  Widget _buildPlanDetails(WorkoutPlan plan) {
    return CustomScrollView(
      slivers: [
        _buildSliverAppBar(plan),
        SliverToBoxAdapter(
          child: Column(
            children: [
              if (plan.media.isNotEmpty) _buildMediaSectionNew(plan.media),
              _buildWorkoutInfo(plan),
              _buildExercisesSection(plan),
              if (plan.instructions != null) _buildInstructionsSection(plan),
              const SizedBox(height: 100),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSliverAppBar(WorkoutPlan plan) {
    return SliverAppBar(
      expandedHeight: 200,
      floating: false,
      pinned: true,
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: PremiumIconButton(
        icon: Icons.arrow_back_ios,
        onPressed: () => Navigator.pop(context),
        hasGradient: false,
        backgroundColor: AppTheme.surfaceLight.withValues(alpha: 0.9),
        iconColor: AppTheme.primaryGold,
      ),
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          plan.title,
          style: const TextStyle(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppTheme.warmOrange.withValues(alpha: 0.3),
                AppTheme.primaryGold.withValues(alpha: 0.1),
              ],
            ),
          ),
          child: const Center(
            child: Icon(
              Icons.fitness_center,
              size: 80,
              color: AppTheme.warmOrange,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMediaSection(List<WorkoutMedia> media) {
    return SlideInAnimation(
      child: Container(
        height: 300,
        margin: const EdgeInsets.all(20),
        child: Stack(
          children: [
            PageView.builder(
              controller: _mediaPageController,
              onPageChanged: (index) {
                setState(() {
                  _currentMediaIndex = index;
                });
              },
              itemCount: media.length,
              itemBuilder: (context, index) {
                final mediaItem = media[index];
                return _buildMediaItem(mediaItem);
              },
            ),
            if (media.length > 1) _buildMediaIndicators(media.length),
            if (media.length > 1) _buildMediaNavigation(media.length),
          ],
        ),
      ),
    );
  }

  Widget _buildMediaItem(WorkoutMedia media) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryGold.withValues(alpha: 0.2),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child:
            media.isVideo ? _buildVideoPlayer(media) : _buildImageViewer(media),
      ),
    );
  }

  Widget _buildVideoPlayer(WorkoutMedia media) {
    if (!_videoControllers.containsKey(media.mediaUrl)) {
      _initializeVideoController(media.mediaUrl);
      return Container(
        color: Colors.black,
        child: const Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryGold),
          ),
        ),
      );
    }

    final controller = _videoControllers[media.mediaUrl]!;
    return Stack(
      alignment: Alignment.center,
      children: [
        AspectRatio(
          aspectRatio: controller.value.aspectRatio,
          child: VideoPlayer(controller),
        ),
        GestureDetector(
          onTap: () {
            setState(() {
              controller.value.isPlaying
                  ? controller.pause()
                  : controller.play();
            });
          },
          child: Container(
            width: 70,
            height: 70,
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.7),
              shape: BoxShape.circle,
            ),
            child: Icon(
              controller.value.isPlaying ? Icons.pause : Icons.play_arrow,
              color: Colors.white,
              size: 40,
            ),
          ),
        ),
        if (media.title != null)
          Positioned(
            bottom: 16,
            left: 16,
            right: 16,
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                media.title!,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildImageViewer(WorkoutMedia media) {
    return Stack(
      fit: StackFit.expand,
      children: [
        Image.network(
          media.mediaUrl,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              color: AppTheme.surfaceLight,
              child: const Center(
                child: Icon(
                  Icons.image_not_supported,
                  size: 50,
                  color: AppTheme.textMuted,
                ),
              ),
            );
          },
        ),
        if (media.title != null)
          Positioned(
            bottom: 16,
            left: 16,
            right: 16,
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                media.title!,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildMediaIndicators(int count) {
    return Positioned(
      bottom: 16,
      left: 0,
      right: 0,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(
          count,
          (index) => Container(
            margin: const EdgeInsets.symmetric(horizontal: 4),
            width: _currentMediaIndex == index ? 12 : 8,
            height: 8,
            decoration: BoxDecoration(
              color: _currentMediaIndex == index
                  ? AppTheme.primaryGold
                  : Colors.white.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMediaNavigation(int mediaCount) {
    return Positioned(
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 16),
            child: PremiumIconButton(
              icon: Icons.arrow_back_ios,
              onPressed: _currentMediaIndex > 0
                  ? () {
                      _mediaPageController.previousPage(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    }
                  : null,
              hasGradient: false,
              backgroundColor: Colors.black.withValues(alpha: 0.5),
              iconColor: Colors.white,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(right: 16),
            child: PremiumIconButton(
              icon: Icons.arrow_forward_ios,
              onPressed: _currentMediaIndex < mediaCount - 1
                  ? () {
                      _mediaPageController.nextPage(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    }
                  : null,
              hasGradient: false,
              backgroundColor: Colors.black.withValues(alpha: 0.5),
              iconColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMediaSectionNew(List<WorkoutMedia> media) {
    final mediaItems = media
        .map((m) => MediaItem(
              url: m.mediaUrl,
              type: m.isVideo ? MediaType.video : MediaType.image,
              title: m.title,
              description: m.description,
              createdAt: m.createdAt,
            ))
        .toList();

    return SlideInAnimation(
      child: Container(
        margin: const EdgeInsets.all(20),
        child: MediaViewerWidget(
          mediaItems: mediaItems,
          height: 300,
        ),
      ),
    );
  }

  Widget _buildWorkoutInfo(WorkoutPlan plan) {
    return SlideInAnimation(
      delay: 200,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: AppTheme.surfaceLight,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: AppTheme.primaryGold.withValues(alpha: 0.1),
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: AppTheme.primaryGradient,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.info_outline,
                    color: Colors.black,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                const Text(
                  'معلومات التمرين',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildWorkoutInfoItem(
              'مستوى الصعوبة',
              plan.difficultyText,
              Icons.trending_up,
              _getDifficultyColor(plan.difficultyLevel),
            ),
            _buildWorkoutInfoItem(
              'عدد التمارين',
              '${plan.exercises.length} تمرين',
              Icons.fitness_center,
              AppTheme.warmOrange,
            ),
            if (plan.restDays.isNotEmpty)
              _buildWorkoutInfoItem(
                'أيام الراحة',
                plan.restDays.join(', '),
                Icons.weekend,
                AppTheme.lightBlue,
              ),
            if (plan.description != null)
              _buildWorkoutInfoItem(
                'الوصف',
                plan.description!,
                Icons.description,
                AppTheme.emeraldGreen,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildWorkoutInfoItem(
      String title, String value, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: AppTheme.textSecondary,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: const TextStyle(
                    color: AppTheme.textPrimary,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExercisesSection(WorkoutPlan plan) {
    if (plan.exercises.isEmpty) return const SizedBox.shrink();

    return SlideInAnimation(
      delay: 300,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [AppTheme.warmOrange, AppTheme.coralRed],
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.fitness_center,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                const Text(
                  'التمارين',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...plan.exercises.asMap().entries.map((entry) {
              return _buildExerciseCard(entry.value, entry.key + 1);
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildExerciseCard(dynamic exercise, int index) {
    final exerciseName = exercise['name'] ?? 'تمرين $index';
    final sets = exercise['sets']?.toString() ?? '';
    final reps = exercise['reps']?.toString() ?? '';
    final duration = exercise['duration']?.toString() ?? '';
    final rest = exercise['rest']?.toString() ?? '';

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceLight,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryGold.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Text(
                    '$index',
                    style: const TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  exerciseName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              if (sets.isNotEmpty)
                _buildExerciseDetail('المجموعات', sets, Icons.repeat),
              if (reps.isNotEmpty)
                _buildExerciseDetail('التكرارات', reps, Icons.numbers),
            ],
          ),
          if (duration.isNotEmpty || rest.isNotEmpty) const SizedBox(height: 8),
          Row(
            children: [
              if (duration.isNotEmpty)
                _buildExerciseDetail('المدة', duration, Icons.timer),
              if (rest.isNotEmpty)
                _buildExerciseDetail('الراحة', rest, Icons.pause),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildExerciseDetail(String label, String value, IconData icon) {
    return Expanded(
      child: Container(
        margin: const EdgeInsets.only(right: 8),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: AppTheme.primaryGold.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: AppTheme.primaryGold,
            ),
            const SizedBox(width: 4),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppTheme.textSecondary,
                    ),
                  ),
                  Text(
                    value,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionsSection(WorkoutPlan plan) {
    return SlideInAnimation(
      delay: 400,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: AppTheme.surfaceLight,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: AppTheme.primaryGold.withValues(alpha: 0.1),
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: AppTheme.primaryGradient,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.description,
                    color: Colors.black,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                const Text(
                  'التعليمات',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              plan.instructions!,
              style: const TextStyle(
                color: AppTheme.textPrimary,
                fontSize: 16,
                height: 1.6,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getDifficultyColor(int level) {
    switch (level) {
      case 1:
      case 2:
        return AppTheme.emeraldGreen;
      case 3:
        return AppTheme.warmOrange;
      case 4:
      case 5:
        return AppTheme.coralRed;
      default:
        return AppTheme.primaryGold;
    }
  }
}
