class AppConstants {
  // App Info
  static const String appName = 'FitGold';
  static const String appNameArabic = 'رشاقة ذهبية';
  static const String appVersion = '1.0.0';
  
  // Supabase Storage Buckets
  static const String avatarsBucket = 'avatars';
  static const String progressPhotosBucket = 'progress_photos';
  static const String chatFilesBucket = 'chat_files';
  
  // User Types
  static const String traineeType = 'trainee';
  static const String trainerType = 'trainer';
  static const String adminType = 'admin';
  
  // Subscription Plans
  static const String weeklyPlan = 'weekly';
  static const String monthlyPlan = 'monthly';
  static const String quarterlyPlan = 'quarterly';
  
  // Session Types
  static const String trainingSession = 'training';
  static const String consultationSession = 'consultation';
  static const String assessmentSession = 'assessment';
  
  // Notification Types
  static const String planAssignedNotification = 'plan_assigned';
  static const String sessionReminderNotification = 'session_reminder';
  static const String sessionBookedNotification = 'session_booked';
  static const String paymentDueNotification = 'payment_due';
  static const String generalNotification = 'general';
  
  // Fitness Goals
  static const Map<String, String> fitnessGoals = {
    'lose_weight': 'خسارة الوزن',
    'gain_weight': 'زيادة الوزن',
    'maintain_weight': 'الحفاظ على الوزن',
    'build_muscle': 'بناء العضلات',
    'improve_endurance': 'تحسين التحمل',
    'general_fitness': 'لياقة عامة',
  };
  
  // Activity Levels
  static const Map<int, String> activityLevels = {
    1: 'قليل النشاط (لا توجد تمارين)',
    2: 'نشاط خفيف (تمارين خفيفة 1-3 أيام/أسبوع)',
    3: 'نشاط متوسط (تمارين متوسطة 3-5 أيام/أسبوع)',
    4: 'نشاط عالي (تمارين قوية 6-7 أيام/أسبوع)',
    5: 'نشاط عالي جداً (تمارين قوية يومياً + عمل بدني)',
  };
  
  // Common Health Conditions
  static const List<String> commonHealthConditions = [
    'السكري',
    'ضغط الدم',
    'أمراض القلب',
    'التهاب المفاصل',
    'هشاشة العظام',
    'الربو',
    'مشاكل الظهر',
    'إصابات رياضية',
  ];
  
  // Common Dietary Preferences
  static const List<String> commonDietaryPreferences = [
    'نباتي',
    'عالي البروتين',
    'قليل الكربوهيدرات',
    'خالي من الجلوتين',
    'كيتو',
    'متوسطي',
    'خالي من اللاكتوز',
    'حلال',
  ];
  
  // Trainer Specializations
  static const List<String> trainerSpecializations = [
    'خسارة الوزن',
    'بناء العضل',
    'تمارين القلب',
    'التغذية الرياضية',
    'إعادة التأهيل',
    'اللياقة العامة',
    'تدريب كبار السن',
    'تدريب الأطفال',
  ];
  
  // Validation Constants
  static const int minAge = 16;
  static const int maxAge = 80;
  static const double minWeight = 30.0;
  static const double maxWeight = 300.0;
  static const double minHeight = 120.0;
  static const double maxHeight = 250.0;
  
  // Session Duration Options (in minutes)
  static const List<int> sessionDurations = [30, 45, 60, 90, 120];
  
  // Default Values
  static const int defaultSessionDuration = 60;
  static const int defaultActivityLevel = 1;
  static const String defaultCurrency = 'SAR';
  static const String defaultLanguage = 'Arabic';
}
