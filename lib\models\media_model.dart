enum MediaType {
  image,
  video,
}

// نموذج أساسي للميديا
abstract class BaseMedia {
  final String id;
  final MediaType mediaType;
  final String mediaUrl;
  final String? title;
  final String? description;
  final DateTime createdAt;

  BaseMedia({
    required this.id,
    required this.mediaType,
    required this.mediaUrl,
    this.title,
    this.description,
    required this.createdAt,
  });

  // دوال مساعدة آمنة لتحويل البيانات
  static String _getSafeString(Map<String, dynamic> data, String key, [String? defaultValue]) {
    final value = data[key];
    if (value == null) return defaultValue ?? '';
    return value.toString();
  }

  static MediaType _getMediaType(dynamic value) {
    if (value == null) return MediaType.image;
    final typeString = value.toString().toLowerCase();
    
    switch (typeString) {
      case 'video':
        return MediaType.video;
      default:
        return MediaType.image;
    }
  }

  static DateTime? _getSafeDateTime(Map<String, dynamic> data, String key) {
    final value = data[key];
    if (value == null) return null;
    if (value is String) {
      return DateTime.tryParse(value);
    }
    return null;
  }

  // دالة للتحقق من كون الملف صورة
  bool get isImage => mediaType == MediaType.image;

  // دالة للتحقق من كون الملف فيديو
  bool get isVideo => mediaType == MediaType.video;

  // دالة للحصول على امتداد الملف
  String? get fileExtension {
    if (mediaUrl.isEmpty) return null;
    return mediaUrl.split('.').last.toLowerCase();
  }

  Map<String, dynamic> toJson();
}

// نموذج ميديا الخطط الرياضية
class WorkoutMedia extends BaseMedia {
  final String workoutPlanId;

  WorkoutMedia({
    required super.id,
    required super.mediaType,
    required super.mediaUrl,
    super.title,
    super.description,
    required super.createdAt,
    required this.workoutPlanId,
  });

  factory WorkoutMedia.fromJson(Map<String, dynamic> json) {
    return WorkoutMedia(
      id: BaseMedia._getSafeString(json, 'id', ''),
      workoutPlanId: BaseMedia._getSafeString(json, 'workout_plan_id', ''),
      mediaType: BaseMedia._getMediaType(json['media_type']),
      mediaUrl: BaseMedia._getSafeString(json, 'media_url', ''),
      title: BaseMedia._getSafeString(json, 'title'),
      description: BaseMedia._getSafeString(json, 'description'),
      createdAt: BaseMedia._getSafeDateTime(json, 'created_at') ?? DateTime.now(),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'workout_plan_id': workoutPlanId,
      'media_type': mediaType.name,
      'media_url': mediaUrl,
      'title': title,
      'description': description,
      'created_at': createdAt.toIso8601String(),
    };
  }

  WorkoutMedia copyWith({
    String? id,
    String? workoutPlanId,
    MediaType? mediaType,
    String? mediaUrl,
    String? title,
    String? description,
    DateTime? createdAt,
  }) {
    return WorkoutMedia(
      id: id ?? this.id,
      workoutPlanId: workoutPlanId ?? this.workoutPlanId,
      mediaType: mediaType ?? this.mediaType,
      mediaUrl: mediaUrl ?? this.mediaUrl,
      title: title ?? this.title,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'WorkoutMedia(id: $id, workoutPlanId: $workoutPlanId, mediaType: $mediaType, mediaUrl: $mediaUrl)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WorkoutMedia && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// نموذج ميديا الخطط الغذائية
class NutritionMedia extends BaseMedia {
  final String nutritionPlanId;

  NutritionMedia({
    required super.id,
    required super.mediaType,
    required super.mediaUrl,
    super.title,
    super.description,
    required super.createdAt,
    required this.nutritionPlanId,
  });

  factory NutritionMedia.fromJson(Map<String, dynamic> json) {
    return NutritionMedia(
      id: BaseMedia._getSafeString(json, 'id', ''),
      nutritionPlanId: BaseMedia._getSafeString(json, 'nutrition_plan_id', ''),
      mediaType: BaseMedia._getMediaType(json['media_type']),
      mediaUrl: BaseMedia._getSafeString(json, 'media_url', ''),
      title: BaseMedia._getSafeString(json, 'title'),
      description: BaseMedia._getSafeString(json, 'description'),
      createdAt: BaseMedia._getSafeDateTime(json, 'created_at') ?? DateTime.now(),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nutrition_plan_id': nutritionPlanId,
      'media_type': mediaType.name,
      'media_url': mediaUrl,
      'title': title,
      'description': description,
      'created_at': createdAt.toIso8601String(),
    };
  }

  NutritionMedia copyWith({
    String? id,
    String? nutritionPlanId,
    MediaType? mediaType,
    String? mediaUrl,
    String? title,
    String? description,
    DateTime? createdAt,
  }) {
    return NutritionMedia(
      id: id ?? this.id,
      nutritionPlanId: nutritionPlanId ?? this.nutritionPlanId,
      mediaType: mediaType ?? this.mediaType,
      mediaUrl: mediaUrl ?? this.mediaUrl,
      title: title ?? this.title,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'NutritionMedia(id: $id, nutritionPlanId: $nutritionPlanId, mediaType: $mediaType, mediaUrl: $mediaUrl)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NutritionMedia && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// فئة مساعدة لتجميع الميديا
class MediaCollection {
  final List<BaseMedia> images;
  final List<BaseMedia> videos;

  MediaCollection({
    this.images = const [],
    this.videos = const [],
  });

  factory MediaCollection.fromMediaList(List<BaseMedia> mediaList) {
    final images = mediaList.where((media) => media.isImage).toList();
    final videos = mediaList.where((media) => media.isVideo).toList();

    return MediaCollection(
      images: images,
      videos: videos,
    );
  }

  List<BaseMedia> get allMedia => [...images, ...videos];

  int get totalCount => images.length + videos.length;

  bool get isEmpty => totalCount == 0;

  bool get isNotEmpty => totalCount > 0;

  bool get hasImages => images.isNotEmpty;

  bool get hasVideos => videos.isNotEmpty;
}
