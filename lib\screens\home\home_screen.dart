import 'package:flutter/material.dart';
import '../../main.dart';
import '../../theme/app_theme.dart';
import '../../widgets/premium_card.dart' hide PremiumIconCard;
import '../../widgets/premium_widgets.dart';
import '../../widgets/animated_counter.dart';
import '../../widgets/premium_bottom_nav.dart';
import '../trainers/trainers_screen.dart';
import '../plans/plans_screen.dart';
import '../sessions/sessions_screen.dart';
import '../profile/profile_screen.dart';
import '../../services/notification_service.dart';
import 'notifications_screen.dart';
import '../subscriptions/subscriptions_screen.dart';
import '../progress/progress_tracking_screen.dart';
import '../reviews/reviews_screen.dart';
import '../chat/conversations_screen.dart';

class HomeScreen extends StatefulWidget {
  final Function(String) onLanguageChanged;
  const HomeScreen({super.key, required this.onLanguageChanged});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;

  late final List<Widget> _screens;

  @override
  void initState() {
    super.initState();
    _screens = [
      const HomeTab(),
      const PlansScreen(),
      const SessionsScreen(),
      ProfileScreen(onLanguageChanged: widget.onLanguageChanged),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_currentIndex],
      bottomNavigationBar: PremiumBottomNavBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        items: const [
          PremiumNavItem(
            icon: Icons.home_rounded,
            label: 'الرئيسية',
          ),
          PremiumNavItem(
            icon: Icons.restaurant_menu_rounded,
            label: 'الخطط',
          ),
          PremiumNavItem(
            icon: Icons.calendar_today_rounded,
            label: 'الجلسات',
          ),
          PremiumNavItem(
            icon: Icons.person_rounded,
            label: 'الملف',
          ),
        ],
      ),
    );
  }
}

class HomeTab extends StatefulWidget {
  const HomeTab({super.key});

  @override
  State<HomeTab> createState() => _HomeTabState();
}

class _HomeTabState extends State<HomeTab> with TickerProviderStateMixin {
  Map<String, dynamic>? _userProfile;
  Map<String, dynamic>? _currentTrainer;
  Map<String, dynamic>? _nextSession;
  bool _isLoading = true;
  int _unreadNotifications = 0;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // متغيرات الإحصائيات
  int _completedSessions = 0;
  double _weightProgress = 0.0;
  int _activeConversations = 0;
  double _averageRating = 0.0;
  int _caloriesBurned = 0;
  int _activeDays = 0;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
    _loadUserData();
    _loadNotifications();
    _loadStatistics();
  }

  Future<void> _loadNotifications() async {
    try {
      final user = supabase.auth.currentUser;
      if (user != null) {
        final count = await NotificationService.getUnreadCount(user.id);
        setState(() {
          _unreadNotifications = count;
        });
      }
    } catch (_) {}
  }

  Future<void> _loadStatistics() async {
    try {
      final user = supabase.auth.currentUser;
      if (user != null) {
        // جلب عدد الجلسات المكتملة هذا الشهر
        final now = DateTime.now();
        final startOfMonth = DateTime(now.year, now.month, 1);

        final completedSessionsResponse = await supabase
            .from('sessions')
            .select('id')
            .eq('trainee_id', user.id)
            .eq('status', 'completed')
            .gte('scheduled_at', startOfMonth.toIso8601String())
            .lte('scheduled_at', now.toIso8601String());

        // جلب تقدم الوزن (آخر قياسين)
        final weightProgressResponse = await supabase
            .from('progress_tracking')
            .select('weight')
            .eq('user_id', user.id)
            .eq('metric_type', 'weight')
            .order('recorded_at', ascending: false)
            .limit(2);

        double weightProgress = 0.0;
        if (weightProgressResponse.length >= 2) {
          final latest = weightProgressResponse[0]['weight'] as double;
          final previous = weightProgressResponse[1]['weight'] as double;
          weightProgress = latest - previous;
        }

        // جلب عدد المحادثات النشطة
        final conversationsResponse = await supabase
            .from('chat_messages')
            .select('conversation_id')
            .eq('sender_id', user.id)
            .gte(
                'created_at',
                DateTime.now()
                    .subtract(const Duration(days: 7))
                    .toIso8601String());

        final uniqueConversations = <String>{};
        for (final message in conversationsResponse) {
          uniqueConversations.add(message['conversation_id']);
        }

        // جلب متوسط التقييم للمستخدم (إذا كان مدرباً)
        final reviewsResponse = await supabase
            .from('reviews')
            .select('rating')
            .eq('trainee_id', user.id);

        double averageRating = 0.0;
        if (reviewsResponse.isNotEmpty) {
          final totalRating = reviewsResponse.fold<double>(
              0.0, (sum, review) => sum + (review['rating'] as int).toDouble());
          averageRating = totalRating / reviewsResponse.length;
        }

        // جلب السعرات المحروقة هذا الشهر
        final caloriesResponse = await supabase
            .from('progress_tracking')
            .select('value')
            .eq('user_id', user.id)
            .eq('metric_type', 'calories_burned')
            .gte('recorded_at', startOfMonth.toIso8601String())
            .lte('recorded_at', now.toIso8601String());

        int totalCalories = 0;
        for (final record in caloriesResponse) {
          totalCalories += (record['value'] as double).toInt();
        }

        // جلب أيام النشاط هذا الشهر
        final activityResponse = await supabase
            .from('progress_tracking')
            .select('recorded_at')
            .eq('user_id', user.id)
            .gte('recorded_at', startOfMonth.toIso8601String())
            .lte('recorded_at', now.toIso8601String());

        final uniqueDays = <String>{};
        for (final record in activityResponse) {
          final date = DateTime.parse(record['recorded_at']);
          uniqueDays.add('${date.year}-${date.month}-${date.day}');
        }

        setState(() {
          _completedSessions = completedSessionsResponse.length;
          _weightProgress = weightProgress;
          _activeConversations = uniqueConversations.length;
          _averageRating = averageRating;
          _caloriesBurned = totalCalories;
          _activeDays = uniqueDays.length;
        });
      }
    } catch (error) {
      print('Error loading statistics: $error');
    }
  }

  Future<void> _loadUserData() async {
    try {
      final user = supabase.auth.currentUser;
      if (user != null) {
        // Load user profile
        final profileResponse = await supabase
            .from('trainees_profiles')
            .select()
            .eq('user_id', user.id)
            .single();

        setState(() {
          _userProfile = profileResponse;
        });

        // Load current trainer if assigned
        final trainerResponse = await supabase
            .from('trainer_assignments')
            .select('*, trainers(*)')
            .eq('trainee_id', user.id)
            .eq('status', 'active')
            .maybeSingle();

        if (trainerResponse != null) {
          setState(() {
            _currentTrainer = trainerResponse['trainers'];
          });
        }

        // Load next session
        final sessionResponse = await supabase
            .from('sessions')
            .select()
            .eq('trainee_id', user.id)
            .gte('scheduled_at', DateTime.now().toIso8601String())
            .order('scheduled_at')
            .limit(1)
            .maybeSingle();

        if (sessionResponse != null) {
          setState(() {
            _nextSession = sessionResponse;
          });
        }
      }
    } catch (error) {
      print('Error loading user data: $error');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _refreshData() async {
    await Future.wait([
      _loadUserData(),
      _loadNotifications(),
      _loadStatistics(),
    ]);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: SafeArea(
          child: _isLoading
              ? const Center(
                  child: CircularProgressIndicator(
                    color: AppTheme.primaryGold,
                  ),
                )
              : RefreshIndicator(
                  onRefresh: _refreshData,
                  color: AppTheme.primaryGold,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildPremiumHeader(),
                        const SizedBox(height: 24),
                        _buildWelcomeCard(),
                        const SizedBox(height: 24),
                        _buildStatsGrid(),
                        const SizedBox(height: 24),
                        _buildQuickActions(),
                        const SizedBox(height: 24),
                        _buildUpcomingSession(),
                        const SizedBox(height: 24),
                        _buildMotivationalCard(),
                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildPremiumHeader() {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, 30 * (1 - _fadeAnimation.value)),
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Row(
              children: [
                GlowingWidget(
                  child: Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      gradient: AppTheme.primaryGradient,
                      borderRadius: BorderRadius.circular(15),
                      boxShadow: AppTheme.primaryShadow,
                    ),
                    child: const Icon(
                      Icons.fitness_center,
                      color: Colors.black,
                      size: 26,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'مرحباً، ${supabase.auth.currentUser?.userMetadata?['full_name'] ?? 'المتدرب'}',
                        style:
                            Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: AppTheme.textPrimary,
                                ),
                      ),
                      Text(
                        'لنبدأ يومك بنشاط! 💪',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: AppTheme.textSecondary,
                            ),
                      ),
                    ],
                  ),
                ),
                PremiumIconButton(
                  icon: Icons.notifications_outlined,
                  onPressed: () async {
                    await Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const NotificationsScreen(),
                      ),
                    );
                    _loadNotifications();
                  },
                  hasGradient: false,
                  backgroundColor: AppTheme.surfaceLight,
                  iconColor: _unreadNotifications > 0
                      ? AppTheme.coralRed
                      : AppTheme.primaryGold,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildWelcomeCard() {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(-50 * (1 - _fadeAnimation.value), 0),
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: PremiumCard(
              hasGradient: true,
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'أهلاً بك في FitGold',
                          style:
                              Theme.of(context).textTheme.titleLarge?.copyWith(
                                    color: AppTheme.textLight,
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'رحلتك نحو اللياقة المثالية تبدأ اليوم',
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium
                              ?.copyWith(
                                color:
                                    AppTheme.textLight.withValues(alpha: 0.9),
                              ),
                        ),
                      ],
                    ),
                  ),
                  PulsingWidget(
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: const Icon(
                        Icons.emoji_events,
                        color: Colors.white,
                        size: 40,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatsGrid() {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(50 * (1 - _fadeAnimation.value), 0),
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إحصائياتك',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimary,
                      ),
                ),
                const SizedBox(height: 16),
                GridView.count(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisCount: 2,
                  mainAxisSpacing: 12,
                  crossAxisSpacing: 12,
                  childAspectRatio: 1.3,
                  children: [
                    PremiumStatsCard(
                      title: 'الجلسات المكتملة',
                      value: _completedSessions.toString(),
                      icon: Icons.fitness_center,
                      color: AppTheme.lightBlue,
                      subtitle: 'هذا الشهر',
                    ),
                    PremiumStatsCard(
                      title: 'تقدم الوزن',
                      value: _weightProgress >= 0
                          ? '+${_weightProgress.toStringAsFixed(1)}'
                          : _weightProgress.toStringAsFixed(1),
                      icon: _weightProgress >= 0
                          ? Icons.trending_up
                          : Icons.trending_down,
                      color: _weightProgress >= 0
                          ? AppTheme.warmOrange
                          : AppTheme.emeraldGreen,
                      subtitle: 'كيلو',
                    ),
                    PremiumStatsCard(
                      title: 'المحادثات النشطة',
                      value: _activeConversations.toString(),
                      icon: Icons.chat_bubble,
                      color: AppTheme.warmOrange,
                      subtitle: 'محادثة',
                    ),
                    PremiumStatsCard(
                      title: 'متوسط التقييم',
                      value: _averageRating > 0
                          ? _averageRating.toStringAsFixed(1)
                          : '0.0',
                      icon: Icons.star,
                      color: AppTheme.primaryGold,
                      subtitle: 'نجمة',
                    ),
                    PremiumStatsCard(
                      title: 'السعرات المحروقة',
                      value: _caloriesBurned >= 1000
                          ? '${(_caloriesBurned / 1000).toStringAsFixed(1)}K'
                          : _caloriesBurned.toString(),
                      icon: Icons.local_fire_department,
                      color: AppTheme.lightBlue,
                      subtitle: 'سعرة',
                    ),
                    PremiumStatsCard(
                      title: 'أيام النشاط',
                      value: _activeDays.toString(),
                      icon: Icons.calendar_today,
                      color: AppTheme.emeraldGreen,
                      subtitle: 'يوم',
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildQuickActions() {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, 30 * (1 - _fadeAnimation.value)),
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الإجراءات السريعة',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimary,
                      ),
                ),
                const SizedBox(height: 16),
                GridView.count(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisCount: 2,
                  mainAxisSpacing: 12,
                  crossAxisSpacing: 12,
                  childAspectRatio: 1.0,
                  children: [
                    PremiumIconCard(
                      icon: Icons.calendar_today,
                      title: 'جلساتي',
                      subtitle: 'إدارة جلساتك التدريبية',
                      onTap: () => Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => const SessionsScreen()),
                      ),
                    ),
                    PremiumIconCard(
                      icon: Icons.person_outline,
                      title: 'المدربين',
                      subtitle: 'اختر مدربك المفضل',
                      onTap: () => Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => const TrainersScreen()),
                      ),
                    ),
                    PremiumIconCard(
                      icon: Icons.trending_up,
                      title: 'تتبع التقدم',
                      subtitle: 'راقب تطورك اليومي',
                      onTap: () {
                        final user = supabase.auth.currentUser;
                        if (user != null) {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => ProgressTrackingScreen(
                                      traineeId: user.id,
                                    )),
                          );
                        }
                      },
                    ),
                    PremiumIconCard(
                      icon: Icons.chat_bubble_outline,
                      title: 'المحادثات',
                      subtitle: 'تواصل مع مدربيك',
                      onTap: () {
                        final user = supabase.auth.currentUser;
                        if (user != null) {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => ConversationsScreen(
                                      currentUserId: user.id,
                                    )),
                          );
                        }
                      },
                    ),
                    PremiumIconCard(
                      icon: Icons.star_outline,
                      title: 'التقييمات',
                      subtitle: 'قيم تجربتك مع المدربين',
                      onTap: () {
                        // للتقييمات، نحتاج لمعرف المدرب المعين حالياً
                        if (_currentTrainer != null) {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => ReviewsScreen(
                                      trainerId: _currentTrainer!['id'],
                                    )),
                          );
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('لا يوجد مدرب معين حالياً'),
                            ),
                          );
                        }
                      },
                    ),
                    PremiumIconCard(
                      icon: Icons.restaurant_menu,
                      title: 'خطط التغذية',
                      subtitle: 'خطط غذائية متوازنة',
                      onTap: () => Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => const PlansScreen()),
                      ),
                    ),
                    PremiumIconCard(
                      icon: Icons.card_membership,
                      title: 'الاشتراكات',
                      subtitle: 'إدارة اشتراكاتك',
                      onTap: () => Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => const SubscriptionsScreen()),
                      ),
                    ),
                    PremiumIconCard(
                      icon: Icons.notifications_outlined,
                      title: 'الإشعارات',
                      subtitle: 'تابع آخر التحديثات',
                      onTap: () => Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => const NotificationsScreen()),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildUpcomingSession() {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(-30 * (1 - _fadeAnimation.value), 0),
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الجلسة القادمة',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimary,
                      ),
                ),
                const SizedBox(height: 16),
                _nextSession != null
                    ? PremiumCard(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  width: 50,
                                  height: 50,
                                  decoration: BoxDecoration(
                                    gradient: AppTheme.primaryGradient,
                                    borderRadius: BorderRadius.circular(15),
                                  ),
                                  child: const Icon(
                                    Icons.fitness_center,
                                    color: Colors.black,
                                    size: 24,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        _nextSession!['title'] ??
                                            'جلسة تدريبية',
                                        style: const TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: AppTheme.textPrimary,
                                        ),
                                      ),
                                      Text(
                                        'اليوم، 4:00 مساءً',
                                        style: const TextStyle(
                                          color: AppTheme.textSecondary,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 6,
                                  ),
                                  decoration: BoxDecoration(
                                    color: AppTheme.emeraldGreen
                                        .withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: const Text(
                                    'مجدولة',
                                    style: TextStyle(
                                      color: AppTheme.emeraldGreen,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      )
                    : GlassCard(
                        child: Column(
                          children: [
                            Icon(
                              Icons.calendar_month,
                              size: 48,
                              color: AppTheme.textMuted,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'لا توجد جلسات مجدولة',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyLarge
                                  ?.copyWith(
                                    color: AppTheme.textSecondary,
                                  ),
                            ),
                            const SizedBox(height: 8),
                            PremiumButton(
                              text: 'احجز جلسة الآن',
                              onPressed: () => Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const SessionsScreen(),
                                ),
                              ),
                              height: 40,
                              icon: Icons.add,
                            ),
                          ],
                        ),
                      ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildMotivationalCard() {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, 50 * (1 - _fadeAnimation.value)),
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: PremiumCard(
              hasGradient: true,
              gradientColors: [
                AppTheme.lightBlue.withValues(alpha: 0.8),
                AppTheme.primaryGold.withValues(alpha: 0.6),
                AppTheme.emeraldGreen.withValues(alpha: 0.8),
              ],
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '🚀 استمر في التقدم!',
                          style:
                              Theme.of(context).textTheme.titleLarge?.copyWith(
                                    color: AppTheme.textLight,
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'أنت في الطريق الصحيح لتحقيق أهدافك. كل خطوة تقربك من النجاح!',
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium
                              ?.copyWith(
                                color:
                                    AppTheme.textLight.withValues(alpha: 0.9),
                              ),
                        ),
                      ],
                    ),
                  ),
                  PulsingWidget(
                    child: Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: const Icon(
                        Icons.trending_up,
                        color: Colors.white,
                        size: 30,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
