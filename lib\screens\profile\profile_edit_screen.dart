import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';
import '../../widgets/premium_button.dart';
import '../../widgets/premium_card.dart';
import '../../widgets/animated_widgets.dart';
import '../../widgets/custom_text_field.dart';
import '../../main.dart';

class ProfileEditScreen extends StatefulWidget {
  const ProfileEditScreen({super.key});

  @override
  State<ProfileEditScreen> createState() => _ProfileEditScreenState();
}

class _ProfileEditScreenState extends State<ProfileEditScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _ageController = TextEditingController();
  final _heightController = TextEditingController();
  final _weightController = TextEditingController();
  final _bioController = TextEditingController();

  String _selectedGender = 'ذكر';
  String _selectedGoal = 'فقدان الوزن';
  String _selectedActivity = 'متوسط';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  void _loadUserData() {
    // تحميل بيانات المستخدم الحالية
    _nameController.text = 'أحمد محمد';
    _emailController.text = '<EMAIL>';
    _phoneController.text = '+966501234567';
    _ageController.text = '25';
    _heightController.text = '175';
    _weightController.text = '70';
    _bioController.text = 'أحب الرياضة والحياة الصحية';
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _ageController.dispose();
    _heightController.dispose();
    _weightController.dispose();
    _bioController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      children: [
                        _buildProfileImageSection(),
                        const SizedBox(height: 20),
                        _buildPersonalInfoSection(),
                        const SizedBox(height: 20),
                        _buildPhysicalInfoSection(),
                        const SizedBox(height: 20),
                        _buildGoalsSection(),
                        const SizedBox(height: 20),
                        _buildBioSection(),
                        const SizedBox(height: 30),
                        _buildSaveButton(),
                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return SlideInAnimation(
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            PremiumIconButton(
              icon: Icons.arrow_back_ios,
              onPressed: () => Navigator.pop(context),
              hasGradient: false,
              backgroundColor: AppTheme.surfaceLight,
              iconColor: AppTheme.primaryGold,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'تحديث الملف الشخصي',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimary,
                        ),
                  ),
                  Text(
                    'قم بتحديث معلوماتك الشخصية',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textSecondary,
                        ),
                  ),
                ],
              ),
            ),
            FloatingWidget(
              child: Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: AppTheme.primaryShadow,
                ),
                child: const Icon(
                  Icons.person_outline,
                  color: Colors.black,
                  size: 26,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileImageSection() {
    return SlideInAnimation(
      delay: 100,
      child: PremiumCard(
        child: Column(
          children: [
            Text(
              'صورة الملف الشخصي',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimary,
              ),
            ),
            const SizedBox(height: 20),
            Stack(
              children: [
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    gradient: AppTheme.primaryGradient,
                    borderRadius: BorderRadius.circular(60),
                    boxShadow: AppTheme.primaryShadow,
                  ),
                  child: const Icon(
                    Icons.person,
                    size: 60,
                    color: Colors.black,
                  ),
                ),
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: AppTheme.primaryGold,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: AppTheme.primaryShadow,
                    ),
                    child: IconButton(
                      onPressed: _changeProfileImage,
                      icon: const Icon(
                        Icons.camera_alt,
                        color: Colors.black,
                        size: 20,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPersonalInfoSection() {
    return SlideInAnimation(
      delay: 200,
      child: PremiumCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppTheme.lightBlue.withValues(alpha: 0.2),
                        AppTheme.lightBlue.withValues(alpha: 0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Icon(
                    Icons.person_outline,
                    color: AppTheme.lightBlue,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  'المعلومات الشخصية',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            CustomTextField(
              controller: _nameController,
              label: 'الاسم الكامل',
              prefixIcon: Icon(Icons.person),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال الاسم';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: _emailController,
              label: 'البريد الإلكتروني',
              prefixIcon: Icon(Icons.email),
              keyboardType: TextInputType.emailAddress,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال البريد الإلكتروني';
                }
                if (!value.contains('@')) {
                  return 'يرجى إدخال بريد إلكتروني صحيح';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: _phoneController,
              label: 'رقم الهاتف',
              prefixIcon: Icon(Icons.phone),
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال رقم الهاتف';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            _buildGenderSelector(),
          ],
        ),
      ),
    );
  }

  Widget _buildPhysicalInfoSection() {
    return SlideInAnimation(
      delay: 300,
      child: PremiumCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppTheme.emeraldGreen.withValues(alpha: 0.2),
                        AppTheme.emeraldGreen.withValues(alpha: 0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Icon(
                    Icons.fitness_center,
                    color: AppTheme.emeraldGreen,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  'المعلومات الجسدية',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: _ageController,
                    label: 'العمر',
                    prefixIcon: Icon(Icons.cake),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال العمر';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomTextField(
                    controller: _heightController,
                    label: 'الطول (سم)',
                    prefixIcon: Icon(Icons.height),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال الطول';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: _weightController,
              label: 'الوزن (كغ)',
              prefixIcon: Icon(Icons.monitor_weight),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال الوزن';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGoalsSection() {
    return SlideInAnimation(
      delay: 400,
      child: PremiumCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppTheme.primaryGold.withValues(alpha: 0.2),
                        AppTheme.primaryGold.withValues(alpha: 0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Icon(
                    Icons.flag,
                    color: AppTheme.primaryGold,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  'الأهداف والنشاط',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildGoalSelector(),
            const SizedBox(height: 16),
            _buildActivitySelector(),
          ],
        ),
      ),
    );
  }

  Widget _buildBioSection() {
    return SlideInAnimation(
      delay: 500,
      child: PremiumCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppTheme.warmOrange.withValues(alpha: 0.2),
                        AppTheme.warmOrange.withValues(alpha: 0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Icon(
                    Icons.description,
                    color: AppTheme.warmOrange,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  'نبذة عني',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            CustomTextField(
              controller: _bioController,
              label: 'اكتب نبذة عن نفسك',
              prefixIcon: Icon(Icons.edit),
              maxLines: 4,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGenderSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الجنس',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildGenderOption('ذكر', Icons.male),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildGenderOption('أنثى', Icons.female),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildGenderOption(String gender, IconData icon) {
    bool isSelected = _selectedGender == gender;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedGender = gender;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.primaryGold.withValues(alpha: 0.1)
              : AppTheme.surfaceLight,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? AppTheme.primaryGold : Colors.transparent,
            width: 2,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isSelected ? AppTheme.primaryGold : AppTheme.textSecondary,
            ),
            const SizedBox(width: 8),
            Text(
              gender,
              style: TextStyle(
                color: isSelected ? AppTheme.primaryGold : AppTheme.textPrimary,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGoalSelector() {
    List<String> goals = [
      'فقدان الوزن',
      'زيادة العضلات',
      'الحفاظ على الوزن',
      'تحسين اللياقة'
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الهدف الرئيسي',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: goals.map((goal) => _buildGoalChip(goal)).toList(),
        ),
      ],
    );
  }

  Widget _buildGoalChip(String goal) {
    bool isSelected = _selectedGoal == goal;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedGoal = goal;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.primaryGold.withValues(alpha: 0.1)
              : AppTheme.surfaceLight,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? AppTheme.primaryGold : Colors.transparent,
            width: 1,
          ),
        ),
        child: Text(
          goal,
          style: TextStyle(
            color: isSelected ? AppTheme.primaryGold : AppTheme.textPrimary,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  Widget _buildActivitySelector() {
    List<Map<String, dynamic>> activities = [
      {'name': 'منخفض', 'desc': 'قليل الحركة'},
      {'name': 'متوسط', 'desc': '3-4 أيام أسبوعياً'},
      {'name': 'عالي', 'desc': '5-6 أيام أسبوعياً'},
      {'name': 'عالي جداً', 'desc': 'يومياً'},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'مستوى النشاط',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        ...activities
            .map((activity) => _buildActivityOption(
                  activity['name'],
                  activity['desc'],
                ))
            .toList(),
      ],
    );
  }

  Widget _buildActivityOption(String activity, String description) {
    bool isSelected = _selectedActivity == activity;
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedActivity = activity;
          });
        },
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isSelected
                ? AppTheme.primaryGold.withValues(alpha: 0.1)
                : AppTheme.surfaceLight,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected ? AppTheme.primaryGold : Colors.transparent,
              width: 2,
            ),
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      activity,
                      style: TextStyle(
                        color: isSelected
                            ? AppTheme.primaryGold
                            : AppTheme.textPrimary,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      description,
                      style: TextStyle(
                        color: AppTheme.textSecondary,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              if (isSelected)
                Icon(
                  Icons.check_circle,
                  color: AppTheme.primaryGold,
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return SlideInAnimation(
      delay: 600,
      child: PremiumButton(
        text: _isLoading ? 'جاري الحفظ...' : 'حفظ التغييرات',
        onPressed: _isLoading ? null : _saveProfile,
        icon: _isLoading ? null : Icons.save,
        isLoading: _isLoading,
        hasGradient: true,
        gradientColors: [AppTheme.primaryGold, AppTheme.lightBlue],
        width: double.infinity,
        height: 55,
      ),
    );
  }

  void _changeProfileImage() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: AppTheme.surfaceLight,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'تغيير صورة الملف الشخصي',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimary,
              ),
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: PremiumButton(
                    text: 'الكاميرا',
                    onPressed: () {
                      Navigator.pop(context);
                      _showSnackBar('سيتم إضافة وظيفة الكاميرا قريباً');
                    },
                    icon: Icons.camera_alt,
                    hasGradient: false,
                    backgroundColor: AppTheme.primaryGold,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: PremiumButton(
                    text: 'المعرض',
                    onPressed: () {
                      Navigator.pop(context);
                      _showSnackBar('سيتم إضافة وظيفة المعرض قريباً');
                    },
                    icon: Icons.photo_library,
                    hasGradient: false,
                    backgroundColor: AppTheme.lightBlue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  void _saveProfile() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // محاكاة حفظ البيانات
    await Future.delayed(const Duration(seconds: 2));

    try {
      // هنا يمكن إضافة الكود لحفظ البيانات في قاعدة البيانات

      setState(() {
        _isLoading = false;
      });

      _showSnackBar('تم حفظ البيانات بنجاح!');
      Navigator.pop(context, true);
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showSnackBar('حدث خطأ أثناء حفظ البيانات');
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.primaryGold,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }
}
