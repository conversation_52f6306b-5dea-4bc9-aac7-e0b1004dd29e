import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../services/localization_service.dart';
import '../l10n/app_localizations.dart';

class LanguageSelector extends StatelessWidget {
  final Function(String) onLanguageChanged;
  final bool showLabels;
  final bool isCompact;

  const LanguageSelector({
    super.key,
    required this.onLanguageChanged,
    this.showLabels = true,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    
    if (isCompact) {
      return Container(
        decoration: BoxDecoration(
          color: AppTheme.cardBackground.withOpacity(0.8),
          borderRadius: BorderRadius.circular(25),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildCompactLanguageButton('ar', 'ع'),
            _buildCompactLanguageButton('en', 'EN'),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showLabels) ...[
          Text(
            l10n.language,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryGold,
            ),
          ),
          const SizedBox(height: 16),
        ],
        Container(
          decoration: BoxDecoration(
            color: AppTheme.cardBackground,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppTheme.primaryGold.withOpacity(0.3),
            ),
          ),
          child: Column(
            children: [
              _buildLanguageOption(
                context,
                'ar',
                l10n.arabic,
                Icons.language,
              ),
              Divider(
                height: 1,
                color: AppTheme.textSecondary.withOpacity(0.3),
              ),
              _buildLanguageOption(
                context,
                'en',
                l10n.english,
                Icons.language,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildLanguageOption(
    BuildContext context,
    String languageCode,
    String languageName,
    IconData icon,
  ) {
    final isSelected = LocalizationService.currentLanguage == languageCode;
    
    return ListTile(
      leading: Icon(
        icon,
        color: isSelected ? AppTheme.primaryGold : AppTheme.textSecondary,
      ),
      title: Text(
        languageName,
        style: TextStyle(
          fontSize: 16,
          color: isSelected ? AppTheme.primaryGold : AppTheme.textPrimary,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      trailing: isSelected
          ? const Icon(
              Icons.check_circle,
              color: AppTheme.primaryGold,
            )
          : null,
      onTap: () {
        if (!isSelected) {
          onLanguageChanged(languageCode);
        }
      },
    );
  }

  Widget _buildCompactLanguageButton(String languageCode, String label) {
    final isSelected = LocalizationService.currentLanguage == languageCode;
    
    return GestureDetector(
      onTap: () {
        if (!isSelected) {
          onLanguageChanged(languageCode);
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.primaryGold : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.black : AppTheme.textSecondary,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            fontSize: 14,
          ),
        ),
      ),
    );
  }
}
