class TraineeProfileModel {
  final String id;
  final String userId;
  final int age;
  final String gender;
  final double weight;
  final double height;
  final String fitnessGoal;
  final int activityLevel;
  final List<String> healthConditions;
  final List<String> dietaryPreferences;
  final List<String> allergies;
  final List<String> medications;
  final String? emergencyContactName;
  final String? emergencyContactPhone;
  final double? targetWeight;
  final DateTime? targetDate;
  final DateTime createdAt;
  final DateTime updatedAt;

  TraineeProfileModel({
    required this.id,
    required this.userId,
    required this.age,
    required this.gender,
    required this.weight,
    required this.height,
    required this.fitnessGoal,
    required this.activityLevel,
    required this.healthConditions,
    required this.dietaryPreferences,
    required this.allergies,
    required this.medications,
    this.emergencyContactName,
    this.emergencyContactPhone,
    this.targetWeight,
    this.targetDate,
    required this.createdAt,
    required this.updatedAt,
  });

  factory TraineeProfileModel.fromJson(Map<String, dynamic> json) {
    return TraineeProfileModel(
      id: json['id'],
      userId: json['user_id'],
      age: json['age'],
      gender: json['gender'],
      weight: (json['weight']).toDouble(),
      height: (json['height']).toDouble(),
      fitnessGoal: json['fitness_goal'],
      activityLevel: json['activity_level'] ?? 1,
      healthConditions: List<String>.from(json['health_conditions'] ?? []),
      dietaryPreferences: List<String>.from(json['dietary_preferences'] ?? []),
      allergies: List<String>.from(json['allergies'] ?? []),
      medications: List<String>.from(json['medications'] ?? []),
      emergencyContactName: json['emergency_contact_name'],
      emergencyContactPhone: json['emergency_contact_phone'],
      targetWeight: json['target_weight']?.toDouble(),
      targetDate: json['target_date'] != null ? DateTime.parse(json['target_date']) : null,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'age': age,
      'gender': gender,
      'weight': weight,
      'height': height,
      'fitness_goal': fitnessGoal,
      'activity_level': activityLevel,
      'health_conditions': healthConditions,
      'dietary_preferences': dietaryPreferences,
      'allergies': allergies,
      'medications': medications,
      'emergency_contact_name': emergencyContactName,
      'emergency_contact_phone': emergencyContactPhone,
      'target_weight': targetWeight,
      'target_date': targetDate?.toIso8601String().split('T')[0],
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  double get bmi {
    return weight / ((height / 100) * (height / 100));
  }

  String get bmiCategory {
    final bmiValue = bmi;
    if (bmiValue < 18.5) return 'نقص في الوزن';
    if (bmiValue < 25) return 'وزن طبيعي';
    if (bmiValue < 30) return 'زيادة في الوزن';
    return 'سمنة';
  }
}
