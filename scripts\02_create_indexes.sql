-- Create indexes for better performance

-- Users indexes
CREATE INDEX idx_users_user_type ON public.users(user_type);
CREATE INDEX idx_users_is_active ON public.users(is_active);
CREATE INDEX idx_users_created_at ON public.users(created_at);

-- Trainers indexes
CREATE INDEX idx_trainers_user_id ON public.trainers(user_id);
CREATE INDEX idx_trainers_specialization ON public.trainers USING GIN(specialization);
CREATE INDEX idx_trainers_rating ON public.trainers(rating DESC);
CREATE INDEX idx_trainers_is_available ON public.trainers(is_available);
CREATE INDEX idx_trainers_is_verified ON public.trainers(is_verified);

-- Trainees profiles indexes
CREATE INDEX idx_trainees_profiles_user_id ON public.trainees_profiles(user_id);
CREATE INDEX idx_trainees_profiles_fitness_goal ON public.trainees_profiles(fitness_goal);
CREATE INDEX idx_trainees_profiles_age ON public.trainees_profiles(age);

-- Trainer assignments indexes
CREATE INDEX idx_trainer_assignments_trainee_id ON public.trainer_assignments(trainee_id);
CREATE INDEX idx_trainer_assignments_trainer_id ON public.trainer_assignments(trainer_id);
CREATE INDEX idx_trainer_assignments_status ON public.trainer_assignments(status);
CREATE INDEX idx_trainer_assignments_dates ON public.trainer_assignments(start_date, end_date);

-- Subscriptions indexes
CREATE INDEX idx_subscriptions_trainee_id ON public.subscriptions(trainee_id);
CREATE INDEX idx_subscriptions_trainer_id ON public.subscriptions(trainer_id);
CREATE INDEX idx_subscriptions_status ON public.subscriptions(status);
CREATE INDEX idx_subscriptions_dates ON public.subscriptions(start_date, end_date);

-- Sessions indexes
CREATE INDEX idx_sessions_trainee_id ON public.sessions(trainee_id);
CREATE INDEX idx_sessions_trainer_id ON public.sessions(trainer_id);
CREATE INDEX idx_sessions_scheduled_at ON public.sessions(scheduled_at);
CREATE INDEX idx_sessions_status ON public.sessions(status);

-- Plans indexes
CREATE INDEX idx_nutrition_plans_trainee_id ON public.nutrition_plans(trainee_id);
CREATE INDEX idx_nutrition_plans_trainer_id ON public.nutrition_plans(trainer_id);
CREATE INDEX idx_nutrition_plans_active ON public.nutrition_plans(is_active);
CREATE INDEX idx_nutrition_plans_dates ON public.nutrition_plans(start_date, end_date);

CREATE INDEX idx_workout_plans_trainee_id ON public.workout_plans(trainee_id);
CREATE INDEX idx_workout_plans_trainer_id ON public.workout_plans(trainer_id);
CREATE INDEX idx_workout_plans_active ON public.workout_plans(is_active);
CREATE INDEX idx_workout_plans_dates ON public.workout_plans(start_date, end_date);

-- Progress tracking indexes
CREATE INDEX idx_progress_tracking_trainee_id ON public.progress_tracking(trainee_id);
CREATE INDEX idx_progress_tracking_recorded_at ON public.progress_tracking(recorded_at);

-- Reviews indexes
CREATE INDEX idx_reviews_trainer_id ON public.reviews(trainer_id);
CREATE INDEX idx_reviews_rating ON public.reviews(rating);
CREATE INDEX idx_reviews_created_at ON public.reviews(created_at);

-- Notifications indexes
CREATE INDEX idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX idx_notifications_is_read ON public.notifications(is_read);
CREATE INDEX idx_notifications_type ON public.notifications(type);
CREATE INDEX idx_notifications_created_at ON public.notifications(created_at);

-- Chat messages indexes
CREATE INDEX idx_chat_messages_sender_id ON public.chat_messages(sender_id);
CREATE INDEX idx_chat_messages_receiver_id ON public.chat_messages(receiver_id);
CREATE INDEX idx_chat_messages_conversation ON public.chat_messages(sender_id, receiver_id, created_at);
CREATE INDEX idx_chat_messages_is_read ON public.chat_messages(is_read);
