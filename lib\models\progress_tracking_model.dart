class ProgressTracking {
  final String id;
  final String traineeId;
  final double? weight;
  final double? bodyFatPercentage;
  final double? muscleMass;
  final Map<String, dynamic> measurements;
  final List<String> photos;
  final String? notes;
  final DateTime recordedAt;
  final DateTime createdAt;

  ProgressTracking({
    required this.id,
    required this.traineeId,
    this.weight,
    this.bodyFatPercentage,
    this.muscleMass,
    this.measurements = const {},
    this.photos = const [],
    this.notes,
    required this.recordedAt,
    required this.createdAt,
  });

  factory ProgressTracking.fromJson(Map<String, dynamic> json) {
    return ProgressTracking(
      id: _getSafeString(json, 'id', ''),
      traineeId: _getSafeString(json, 'trainee_id', ''),
      weight: _getSafeDouble(json, 'weight'),
      bodyFatPercentage: _getSafeDouble(json, 'body_fat_percentage'),
      muscleMass: _getSafeDouble(json, 'muscle_mass'),
      measurements: _getSafeMap(json, 'measurements'),
      photos: _getSafeStringList(json, 'photos'),
      notes: _getSafeString(json, 'notes'),
      recordedAt: _getSafeDateTime(json, 'recorded_at') ?? DateTime.now(),
      createdAt: _getSafeDateTime(json, 'created_at') ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'trainee_id': traineeId,
      'weight': weight,
      'body_fat_percentage': bodyFatPercentage,
      'muscle_mass': muscleMass,
      'measurements': measurements,
      'photos': photos,
      'notes': notes,
      'recorded_at': recordedAt.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
    };
  }

  // دوال مساعدة آمنة لتحويل البيانات
  static String _getSafeString(Map<String, dynamic> data, String key, [String? defaultValue]) {
    final value = data[key];
    if (value == null) return defaultValue ?? '';
    return value.toString();
  }

  static double? _getSafeDouble(Map<String, dynamic> data, String key) {
    final value = data[key];
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }
    return null;
  }

  static Map<String, dynamic> _getSafeMap(Map<String, dynamic> data, String key) {
    final value = data[key];
    if (value == null) return {};
    if (value is Map<String, dynamic>) return value;
    if (value is Map) return Map<String, dynamic>.from(value);
    return {};
  }

  static List<String> _getSafeStringList(Map<String, dynamic> data, String key) {
    final value = data[key];
    if (value == null) return [];
    if (value is List) {
      return value.map((item) => item.toString()).toList();
    }
    return [];
  }

  static DateTime? _getSafeDateTime(Map<String, dynamic> data, String key) {
    final value = data[key];
    if (value == null) return null;
    if (value is String) {
      return DateTime.tryParse(value);
    }
    return null;
  }

  // دالة لحساب BMI
  double? get bmi {
    if (weight == null) return null;
    // نحتاج للطول من ملف المتدرب
    return null; // سيتم حسابها في الشاشة مع بيانات المتدرب
  }

  // دالة لنسخ الكائن مع تعديل بعض القيم
  ProgressTracking copyWith({
    String? id,
    String? traineeId,
    double? weight,
    double? bodyFatPercentage,
    double? muscleMass,
    Map<String, dynamic>? measurements,
    List<String>? photos,
    String? notes,
    DateTime? recordedAt,
    DateTime? createdAt,
  }) {
    return ProgressTracking(
      id: id ?? this.id,
      traineeId: traineeId ?? this.traineeId,
      weight: weight ?? this.weight,
      bodyFatPercentage: bodyFatPercentage ?? this.bodyFatPercentage,
      muscleMass: muscleMass ?? this.muscleMass,
      measurements: measurements ?? this.measurements,
      photos: photos ?? this.photos,
      notes: notes ?? this.notes,
      recordedAt: recordedAt ?? this.recordedAt,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'ProgressTracking(id: $id, traineeId: $traineeId, weight: $weight, recordedAt: $recordedAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProgressTracking && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
