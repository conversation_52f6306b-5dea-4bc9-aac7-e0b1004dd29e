import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../theme/app_theme.dart';
import '../../widgets/premium_button.dart';
import '../../widgets/premium_card.dart';
import '../../widgets/animated_widgets.dart';
import '../../main.dart';
import '../profile/profile_edit_screen.dart';
import 'security_screen.dart';

class SettingsScreen extends StatefulWidget {
  final Function(String)? onLanguageChanged;
  const SettingsScreen({super.key, this.onLanguageChanged});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  // إعدادات الإشعارات
  bool _sessionNotifications = true;
  bool _nutritionNotifications = true;
  bool _updateNotifications = false;

  // إعدادات المظهر
  bool _isDarkMode = false;
  double _fontSize = 16.0;
  String _selectedTheme = 'default';

  // اللغة المحددة
  String _selectedLanguage = 'ar';

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _sessionNotifications = prefs.getBool('session_notifications') ?? true;
      _nutritionNotifications =
          prefs.getBool('nutrition_notifications') ?? true;
      _updateNotifications = prefs.getBool('update_notifications') ?? false;
      _isDarkMode = prefs.getBool('dark_mode') ?? false;
      _fontSize = prefs.getDouble('font_size') ?? 16.0;
      _selectedTheme = prefs.getString('selected_theme') ?? 'default';
      _selectedLanguage = prefs.getString('selected_language') ?? 'ar';
    });
  }

  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('session_notifications', _sessionNotifications);
    await prefs.setBool('nutrition_notifications', _nutritionNotifications);
    await prefs.setBool('update_notifications', _updateNotifications);
    await prefs.setBool('dark_mode', _isDarkMode);
    await prefs.setDouble('font_size', _fontSize);
    await prefs.setString('selected_theme', _selectedTheme);
    await prefs.setString('selected_language', _selectedLanguage);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildPremiumHeader(),
              const SizedBox(height: 20),
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      _buildLanguageSection(),
                      const SizedBox(height: 20),
                      _buildAccountSection(),
                      const SizedBox(height: 20),
                      _buildNotificationSection(),
                      const SizedBox(height: 20),
                      _buildAppearanceSection(),
                      const SizedBox(height: 20),
                      _buildAboutSection(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPremiumHeader() {
    return SlideInAnimation(
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            PremiumIconButton(
              icon: Icons.arrow_back_ios,
              onPressed: () => Navigator.pop(context),
              hasGradient: false,
              backgroundColor: AppTheme.surfaceLight,
              iconColor: AppTheme.primaryGold,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'الإعدادات',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimary,
                        ),
                  ),
                  Text(
                    'تخصيص تجربتك',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textSecondary,
                        ),
                  ),
                ],
              ),
            ),
            FloatingWidget(
              child: Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: AppTheme.primaryShadow,
                ),
                child: const Icon(
                  Icons.settings,
                  color: Colors.black,
                  size: 26,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageSection() {
    return SlideInAnimation(
      delay: 200,
      child: _buildPremiumSection(
        'اللغة',
        Icons.language,
        AppTheme.lightBlue,
        [
          _buildLanguageOption(
              'العربية', 'ar', Icons.flag, _selectedLanguage == 'ar'),
          _buildLanguageOption(
              'English', 'en', Icons.flag, _selectedLanguage == 'en'),
        ],
      ),
    );
  }

  Widget _buildAccountSection() {
    return SlideInAnimation(
      delay: 300,
      child: _buildPremiumSection(
        'الحساب',
        Icons.account_circle,
        AppTheme.emeraldGreen,
        [
          _buildSettingsOption(
              'تحديث الملف الشخصي', Icons.person_outline, _updateProfile),
          _buildSettingsOption(
              'تغيير كلمة المرور', Icons.lock_outline, _changePassword),
          _buildSettingsOption(
              'الأمان والخصوصية', Icons.security, _securitySettings),
        ],
      ),
    );
  }

  Widget _buildNotificationSection() {
    return SlideInAnimation(
      delay: 400,
      child: _buildPremiumSection(
        'الإشعارات',
        Icons.notifications,
        AppTheme.warmOrange,
        [
          _buildSwitchOption('إشعارات الجلسات', _sessionNotifications, (value) {
            setState(() {
              _sessionNotifications = value;
            });
            _saveNotificationSettings();
          }),
          _buildSwitchOption('إشعارات الخطط الغذائية', _nutritionNotifications,
              (value) {
            setState(() {
              _nutritionNotifications = value;
            });
            _saveNotificationSettings();
          }),
          _buildSwitchOption('إشعارات التحديثات', _updateNotifications,
              (value) {
            setState(() {
              _updateNotifications = value;
            });
            _saveNotificationSettings();
          }),
        ],
      ),
    );
  }

  Widget _buildAppearanceSection() {
    return SlideInAnimation(
      delay: 500,
      child: _buildPremiumSection(
        'المظهر',
        Icons.palette,
        AppTheme.primaryGold,
        [
          _buildSwitchOption('الوضع الليلي', _isDarkMode, (value) {
            setState(() {
              _isDarkMode = value;
            });
            _saveAppearanceSettings();
          }),
          _buildSettingsOption(
              'حجم الخط', Icons.font_download, _showFontSizeDialog),
          _buildSettingsOption('الألوان', Icons.color_lens, _showThemeDialog),
        ],
      ),
    );
  }

  Widget _buildAboutSection() {
    return SlideInAnimation(
      delay: 600,
      child: _buildPremiumSection(
        'حول التطبيق',
        Icons.info,
        AppTheme.textSecondary,
        [
          _buildSettingsOption(
              'الشروط والأحكام', Icons.description, _showTermsOfService),
          _buildSettingsOption(
              'سياسة الخصوصية', Icons.privacy_tip, _showPrivacyPolicy),
          _buildSettingsOption('تقييم التطبيق', Icons.star_outline, _rateApp),
          _buildSettingsOption('تواصل معنا', Icons.contact_support, _contactUs),
          _buildSettingsOption(
              'نسخة التطبيق', Icons.info_outline, _showAppVersion),
          _buildLogoutOption(),
        ],
      ),
    );
  }

  Widget _buildPremiumSection(
      String title, IconData icon, Color color, List<Widget> children) {
    return PremiumCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      color.withValues(alpha: 0.2),
                      color.withValues(alpha: 0.1),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          ...children,
        ],
      ),
    );
  }

  Widget _buildLanguageOption(
      String title, String code, IconData icon, bool isSelected) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedLanguage = code;
          });
          if (widget.onLanguageChanged != null) {
            widget.onLanguageChanged!(code);
          }
          _showLanguageChangeDialog(code);
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isSelected
                ? AppTheme.primaryGold.withValues(alpha: 0.1)
                : AppTheme.surfaceLight,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected ? AppTheme.primaryGold : Colors.transparent,
              width: 2,
            ),
          ),
          child: Row(
            children: [
              Icon(
                icon,
                color:
                    isSelected ? AppTheme.primaryGold : AppTheme.textSecondary,
                size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: TextStyle(
                  color:
                      isSelected ? AppTheme.primaryGold : AppTheme.textPrimary,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  fontSize: 16,
                ),
              ),
              const Spacer(),
              if (isSelected)
                Icon(
                  Icons.check_circle,
                  color: AppTheme.primaryGold,
                  size: 20,
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSettingsOption(String title, IconData icon, VoidCallback onTap) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppTheme.surfaceLight,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Icon(
                icon,
                color: AppTheme.textSecondary,
                size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: const TextStyle(
                  color: AppTheme.textPrimary,
                  fontSize: 16,
                ),
              ),
              const Spacer(),
              Icon(
                Icons.arrow_forward_ios,
                color: AppTheme.textMuted,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSwitchOption(
      String title, bool value, Function(bool) onChanged) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppTheme.surfaceLight,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Icon(
              Icons.notifications_outlined,
              color: AppTheme.textSecondary,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  color: AppTheme.textPrimary,
                  fontSize: 16,
                ),
              ),
            ),
            Switch(
              value: value,
              onChanged: onChanged,
              activeColor: AppTheme.primaryGold,
              inactiveThumbColor: AppTheme.textMuted,
              inactiveTrackColor: AppTheme.textMuted.withValues(alpha: 0.3),
            ),
          ],
        ),
      ),
    );
  }

  // ===== دوال الحساب =====
  void _updateProfile() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ProfileEditScreen(),
      ),
    );

    if (result == true) {
      _showSnackBar('تم تحديث الملف الشخصي بنجاح');
    }
  }

  void _changePassword() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.lock, color: AppTheme.primaryGold),
            SizedBox(width: 8),
            Text('تغيير كلمة المرور'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: InputDecoration(
                labelText: 'كلمة المرور الحالية',
                prefixIcon: Icon(Icons.lock_outline),
              ),
              obscureText: true,
            ),
            SizedBox(height: 16),
            TextField(
              decoration: InputDecoration(
                labelText: 'كلمة المرور الجديدة',
                prefixIcon: Icon(Icons.lock),
              ),
              obscureText: true,
            ),
            SizedBox(height: 16),
            TextField(
              decoration: InputDecoration(
                labelText: 'تأكيد كلمة المرور الجديدة',
                prefixIcon: Icon(Icons.lock),
              ),
              obscureText: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showSnackBar('تم تغيير كلمة المرور بنجاح');
            },
            child: Text('تغيير'),
          ),
        ],
      ),
    );
  }

  void _securitySettings() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SecurityScreen(),
      ),
    );
  }

  // ===== دوال الإشعارات =====
  void _saveNotificationSettings() async {
    await _saveSettings();
    _showSnackBar('تم حفظ إعدادات الإشعارات');
  }

  // ===== دوال المظهر =====
  Future<void> _saveAppearanceSettings() async {
    await _saveSettings();
    _showSnackBar('تم حفظ إعدادات المظهر');
  }

  void _showFontSizeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.font_download, color: AppTheme.primaryGold),
            SizedBox(width: 8),
            Text('حجم الخط'),
          ],
        ),
        content: StatefulBuilder(
          builder: (context, setState) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('حدد حجم الخط المناسب:'),
              SizedBox(height: 20),
              Text(
                'نص تجريبي',
                style: TextStyle(fontSize: _fontSize),
              ),
              SizedBox(height: 20),
              Slider(
                value: _fontSize,
                min: 12.0,
                max: 24.0,
                divisions: 6,
                label: _fontSize.round().toString(),
                onChanged: (value) {
                  setState(() {
                    _fontSize = value;
                  });
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              this.setState(() {});
              await _saveAppearanceSettings();
            },
            child: Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _showThemeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.color_lens, color: AppTheme.primaryGold),
            SizedBox(width: 8),
            Text('الألوان'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('اختر سمة الألوان:'),
            SizedBox(height: 16),
            _buildThemeOption('الافتراضي', 'default', AppTheme.primaryGold),
            _buildThemeOption('الأزرق', 'blue', AppTheme.lightBlue),
            _buildThemeOption('الأخضر', 'green', AppTheme.emeraldGreen),
            _buildThemeOption('البرتقالي', 'orange', AppTheme.warmOrange),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildThemeOption(String title, String value, Color color) {
    return ListTile(
      leading: Container(
        width: 30,
        height: 30,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(15),
        ),
      ),
      title: Text(title),
      trailing: _selectedTheme == value
          ? Icon(Icons.check, color: AppTheme.primaryGold)
          : null,
      onTap: () async {
        setState(() {
          _selectedTheme = value;
        });
        Navigator.pop(context);
        await _saveAppearanceSettings();
      },
    );
  }

  // ===== دوال اللغة =====
  void _showLanguageChangeDialog(String languageCode) {
    String languageName = languageCode == 'ar' ? 'العربية' : 'English';
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.language, color: AppTheme.primaryGold),
            SizedBox(width: 8),
            Text('تغيير اللغة'),
          ],
        ),
        content: Text('هل تريد تغيير لغة التطبيق إلى $languageName؟'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _selectedLanguage = _selectedLanguage == 'ar' ? 'ar' : 'en';
              });
            },
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _saveSettings();
              if (widget.onLanguageChanged != null) {
                widget.onLanguageChanged!(languageCode);
              }
              _showSnackBar('تم تغيير اللغة إلى $languageName');
            },
            child: Text('تغيير'),
          ),
        ],
      ),
    );
  }

  // ===== دوال حول التطبيق =====
  void _showTermsOfService() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.description, color: AppTheme.primaryGold),
            SizedBox(width: 8),
            Text('الشروط والأحكام'),
          ],
        ),
        content: SingleChildScrollView(
          child: Text(
            '''شروط وأحكام استخدام تطبيق BALANCE:

1. قبول الشروط
باستخدام هذا التطبيق، فإنك توافق على هذه الشروط والأحكام.

2. الاستخدام المسموح
يجب استخدام التطبيق للأغراض الشخصية والتدريبية فقط.

3. حقوق الملكية الفكرية
جميع المحتويات محمية بحقوق الطبع والنشر.

4. الخصوصية
نحن نحترم خصوصيتك ونحمي بياناتك الشخصية.

5. التحديثات
قد نقوم بتحديث هذه الشروط من وقت لآخر.

للمزيد من التفاصيل، يرجى زيارة موقعنا الإلكتروني.''',
            style: TextStyle(height: 1.5),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              const url = 'https://balance-app.com/terms';
              if (await canLaunchUrl(Uri.parse(url))) {
                await launchUrl(Uri.parse(url));
              }
            },
            child: Text('قراءة المزيد'),
          ),
        ],
      ),
    );
  }

  void _showPrivacyPolicy() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.privacy_tip, color: AppTheme.primaryGold),
            SizedBox(width: 8),
            Text('سياسة الخصوصية'),
          ],
        ),
        content: SingleChildScrollView(
          child: Text(
            '''سياسة خصوصية تطبيق BALANCE:

1. جمع المعلومات
نجمع المعلومات التي تقدمها لنا مباشرة عند التسجيل.

2. استخدام المعلومات
نستخدم معلوماتك لتحسين خدماتنا وتخصيص تجربتك.

3. مشاركة المعلومات
لا نشارك معلوماتك الشخصية مع أطراف ثالثة.

4. أمان البيانات
نتخذ إجراءات أمنية لحماية معلوماتك.

5. حقوقك
يمكنك طلب حذف أو تعديل بياناتك في أي وقت.

6. الاتصال بنا
لأي استفسارات حول الخصوصية، تواصل معنا.''',
            style: TextStyle(height: 1.5),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              const url = 'https://balance-app.com/privacy';
              if (await canLaunchUrl(Uri.parse(url))) {
                await launchUrl(Uri.parse(url));
              }
            },
            child: Text('قراءة المزيد'),
          ),
        ],
      ),
    );
  }

  void _rateApp() async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.star, color: AppTheme.primaryGold),
            SizedBox(width: 8),
            Text('تقييم التطبيق'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('هل تستمتع باستخدام BALANCE؟'),
            SizedBox(height: 16),
            Text('قيم التطبيق في متجر التطبيقات'),
            SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(
                  5,
                  (index) =>
                      Icon(Icons.star, color: AppTheme.primaryGold, size: 30)),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('لاحqاً'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              // فتح متجر التطبيقات
              const url =
                  'https://play.google.com/store/apps/details?id=com.balance.app';
              if (await canLaunchUrl(Uri.parse(url))) {
                await launchUrl(Uri.parse(url));
              }
              _showSnackBar('شكراً لك على تقييم التطبيق!');
            },
            child: Text('تقييم الآن'),
          ),
        ],
      ),
    );
  }

  void _contactUs() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.contact_support, color: AppTheme.primaryGold),
            SizedBox(width: 8),
            Text('تواصل معنا'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('اختر وسيلة التواصل:'),
            SizedBox(height: 16),
            _buildContactOption('البريد الإلكتروني', Icons.email, () async {
              const email = 'mailto:<EMAIL>';
              if (await canLaunchUrl(Uri.parse(email))) {
                await launchUrl(Uri.parse(email));
              }
            }),
            _buildContactOption('الواتساب', Icons.chat, () async {
              const whatsapp = 'https://wa.me/966500000000';
              if (await canLaunchUrl(Uri.parse(whatsapp))) {
                await launchUrl(Uri.parse(whatsapp));
              }
            }),
            _buildContactOption('تويتر', Icons.message, () async {
              const twitter = 'https://twitter.com/balance_app';
              if (await canLaunchUrl(Uri.parse(twitter))) {
                await launchUrl(Uri.parse(twitter));
              }
            }),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildContactOption(String title, IconData icon, VoidCallback onTap) {
    return ListTile(
      leading: Icon(icon, color: AppTheme.primaryGold),
      title: Text(title),
      trailing: Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () {
        Navigator.pop(context);
        onTap();
      },
    );
  }

  void _showAppVersion() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.info, color: AppTheme.primaryGold),
            SizedBox(width: 8),
            Text('معلومات التطبيق'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              'assets/images/logo.png',
              width: 80,
              height: 80,
              errorBuilder: (context, error, stackTrace) => Icon(
                  Icons.fitness_center,
                  size: 80,
                  color: AppTheme.primaryGold),
            ),
            SizedBox(height: 16),
            Text(
              'BALANCE',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryGold,
              ),
            ),
            SizedBox(height: 8),
            Text('الإصدار: 1.0.0'),
            Text('تاريخ البناء: 2024-01-01'),
            SizedBox(height: 16),
            Text(
              'تطبيق BALANCE هو منصتك الشاملة لتحقيق التوازن بين اللياقة البدنية والتغذية والصحة الذهنية. صُمم ليمنحك تجربة احترافية ونتائج مستدامة.',
              textAlign: TextAlign.center,
              style: TextStyle(color: AppTheme.textSecondary),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildLogoutOption() {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      child: PremiumButton(
        text: 'تسجيل الخروج',
        onPressed: _showLogoutDialog,
        hasGradient: true,
        gradientColors: [AppTheme.coralRed, AppTheme.warmOrange],
        icon: Icons.logout,
        width: double.infinity,
        height: 50,
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.logout, color: AppTheme.coralRed),
            SizedBox(width: 8),
            Text('تسجيل الخروج'),
          ],
        ),
        content: Text('هل أنت متأكد من أنك تريد تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await supabase.auth.signOut();
              if (mounted) {
                Navigator.pushNamedAndRemoveUntil(
                  context,
                  '/login',
                  (route) => false,
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.coralRed,
            ),
            child: Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.primaryGold,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }
}
