import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';
import '../../widgets/premium_button.dart';
import '../../widgets/premium_card.dart';
import '../../widgets/animated_widgets.dart';
import '../../main.dart';

class SecurityScreen extends StatefulWidget {
  const SecurityScreen({super.key});

  @override
  State<SecurityScreen> createState() => _SecurityScreenState();
}

class _SecurityScreenState extends State<SecurityScreen> {
  bool _twoFactorEnabled = false;
  bool _biometricEnabled = true;
  bool _loginNotifications = true;
  bool _securityAlerts = true;

  List<Map<String, dynamic>> _activeSessions = [
    {
      'device': 'iPhone 13 Pro',
      'location': 'الرياض، السعودية',
      'time': 'الآن',
      'current': true,
    },
    {
      'device': 'MacBook Pro',
      'location': 'الرياض، السعودية',
      'time': 'منذ ساعتين',
      'current': false,
    },
    {
      'device': 'Chrome - Windows',
      'location': 'جدة، السعودية',
      'time': 'منذ 3 أيام',
      'current': false,
    },
  ];

  List<Map<String, dynamic>> _securityLog = [
    {
      'action': 'تسجيل دخول',
      'device': 'iPhone 13 Pro',
      'time': 'اليوم 09:30',
      'success': true,
    },
    {
      'action': 'تغيير كلمة المرور',
      'device': 'MacBook Pro',
      'time': 'أمس 14:20',
      'success': true,
    },
    {
      'action': 'محاولة دخول فاشلة',
      'device': 'جهاز غير معروف',
      'time': 'منذ يومين 22:15',
      'success': false,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      _buildSecuritySettingsSection(),
                      const SizedBox(height: 20),
                      _buildTwoFactorSection(),
                      const SizedBox(height: 20),
                      _buildActiveSessionsSection(),
                      const SizedBox(height: 20),
                      _buildSecurityLogSection(),
                      const SizedBox(height: 20),
                      _buildPasswordSection(),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return SlideInAnimation(
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            PremiumIconButton(
              icon: Icons.arrow_back_ios,
              onPressed: () => Navigator.pop(context),
              hasGradient: false,
              backgroundColor: AppTheme.surfaceLight,
              iconColor: AppTheme.primaryGold,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'الأمان والخصوصية',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimary,
                        ),
                  ),
                  Text(
                    'إعدادات الحماية والأمان',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textSecondary,
                        ),
                  ),
                ],
              ),
            ),
            FloatingWidget(
              child: Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: AppTheme.primaryShadow,
                ),
                child: const Icon(
                  Icons.security,
                  color: Colors.black,
                  size: 26,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecuritySettingsSection() {
    return SlideInAnimation(
      delay: 100,
      child: PremiumCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppTheme.emeraldGreen.withValues(alpha: 0.2),
                        AppTheme.emeraldGreen.withValues(alpha: 0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Icon(
                    Icons.shield,
                    color: AppTheme.emeraldGreen,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  'إعدادات الأمان',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildSecurityOption(
              'البصمة/الوجه',
              'استخدم البصمة أو التعرف على الوجه',
              Icons.fingerprint,
              _biometricEnabled,
              (value) {
                setState(() {
                  _biometricEnabled = value;
                });
                _showSnackBar(value
                    ? 'تم تفعيل البصمة/الوجه'
                    : 'تم إلغاء تفعيل البصمة/الوجه');
              },
            ),
            _buildSecurityOption(
              'إشعارات تسجيل الدخول',
              'احصل على إشعار عند تسجيل الدخول',
              Icons.notifications_active,
              _loginNotifications,
              (value) {
                setState(() {
                  _loginNotifications = value;
                });
                _saveSecuritySettings();
              },
            ),
            _buildSecurityOption(
              'تنبيهات الأمان',
              'تنبيهات عند النشاطات المشبوهة',
              Icons.warning,
              _securityAlerts,
              (value) {
                setState(() {
                  _securityAlerts = value;
                });
                _saveSecuritySettings();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTwoFactorSection() {
    return SlideInAnimation(
      delay: 200,
      child: PremiumCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppTheme.primaryGold.withValues(alpha: 0.2),
                        AppTheme.primaryGold.withValues(alpha: 0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Icon(
                    Icons.security,
                    color: AppTheme.primaryGold,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'المصادقة الثنائية',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                      Text(
                        _twoFactorEnabled ? 'مُفعَّل' : 'غير مُفعَّل',
                        style: TextStyle(
                          fontSize: 14,
                          color: _twoFactorEnabled
                              ? AppTheme.emeraldGreen
                              : AppTheme.coralRed,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                Switch(
                  value: _twoFactorEnabled,
                  onChanged: (value) {
                    if (value) {
                      _showTwoFactorSetup();
                    } else {
                      _disableTwoFactor();
                    }
                  },
                  activeColor: AppTheme.primaryGold,
                ),
              ],
            ),
            if (!_twoFactorEnabled) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppTheme.warmOrange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppTheme.warmOrange.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info,
                      color: AppTheme.warmOrange,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'يُنصح بتفعيل المصادقة الثنائية لحماية إضافية لحسابك',
                        style: TextStyle(
                          color: AppTheme.warmOrange,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildActiveSessionsSection() {
    return SlideInAnimation(
      delay: 300,
      child: PremiumCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppTheme.lightBlue.withValues(alpha: 0.2),
                        AppTheme.lightBlue.withValues(alpha: 0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Icon(
                    Icons.devices,
                    color: AppTheme.lightBlue,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'الجلسات النشطة',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary,
                    ),
                  ),
                ),
                TextButton(
                  onPressed: _terminateAllSessions,
                  child: Text(
                    'إنهاء الكل',
                    style: TextStyle(color: AppTheme.coralRed),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            ..._activeSessions
                .map((session) => _buildSessionItem(session))
                .toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildSessionItem(Map<String, dynamic> session) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: session['current']
            ? AppTheme.primaryGold.withValues(alpha: 0.1)
            : AppTheme.surfaceLight,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: session['current']
              ? AppTheme.primaryGold.withValues(alpha: 0.3)
              : Colors.transparent,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: session['current']
                  ? AppTheme.primaryGold
                  : AppTheme.textMuted,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              _getDeviceIcon(session['device']),
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      session['device'],
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                    if (session['current']) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: AppTheme.emeraldGreen,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Text(
                          'الحالي',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  '${session['location']} • ${session['time']}',
                  style: TextStyle(
                    color: AppTheme.textSecondary,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          if (!session['current'])
            IconButton(
              onPressed: () => _terminateSession(session),
              icon: Icon(
                Icons.close,
                color: AppTheme.coralRed,
                size: 20,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSecurityLogSection() {
    return SlideInAnimation(
      delay: 400,
      child: PremiumCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppTheme.warmOrange.withValues(alpha: 0.2),
                        AppTheme.warmOrange.withValues(alpha: 0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Icon(
                    Icons.history,
                    color: AppTheme.warmOrange,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'سجل الأمان',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary,
                    ),
                  ),
                ),
                TextButton(
                  onPressed: _showFullSecurityLog,
                  child: Text(
                    'عرض الكل',
                    style: TextStyle(color: AppTheme.primaryGold),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            ..._securityLog.take(3).map((log) => _buildLogItem(log)).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildLogItem(Map<String, dynamic> log) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceLight,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: log['success'] ? AppTheme.emeraldGreen : AppTheme.coralRed,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              log['success'] ? Icons.check : Icons.close,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  log['action'],
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${log['device']} • ${log['time']}',
                  style: TextStyle(
                    color: AppTheme.textSecondary,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPasswordSection() {
    return SlideInAnimation(
      delay: 500,
      child: PremiumCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppTheme.coralRed.withValues(alpha: 0.2),
                        AppTheme.coralRed.withValues(alpha: 0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Icon(
                    Icons.lock,
                    color: AppTheme.coralRed,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  'كلمة المرور',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildPasswordOption(
              'تغيير كلمة المرور',
              'آخر تغيير: منذ شهر',
              Icons.lock_outline,
              _changePassword,
            ),
            _buildPasswordOption(
              'أسئلة الأمان',
              'إعداد أسئلة الاستعادة',
              Icons.help_outline,
              _setupSecurityQuestions,
            ),
            _buildPasswordOption(
              'مدير كلمات المرور',
              'عرض كلمات المرور المحفوظة',
              Icons.password,
              _showPasswordManager,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecurityOption(
    String title,
    String subtitle,
    IconData icon,
    bool value,
    Function(bool) onChanged,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Icon(
            icon,
            color: AppTheme.textSecondary,
            size: 24,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimary,
                  ),
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 14,
                    color: AppTheme.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppTheme.primaryGold,
          ),
        ],
      ),
    );
  }

  Widget _buildPasswordOption(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppTheme.surfaceLight,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Icon(
                icon,
                color: AppTheme.textSecondary,
                size: 24,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: AppTheme.textMuted,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getDeviceIcon(String device) {
    if (device.toLowerCase().contains('iphone') ||
        device.toLowerCase().contains('android')) {
      return Icons.phone_iphone;
    } else if (device.toLowerCase().contains('mac') ||
        device.toLowerCase().contains('windows')) {
      return Icons.laptop;
    } else if (device.toLowerCase().contains('chrome') ||
        device.toLowerCase().contains('safari')) {
      return Icons.web;
    }
    return Icons.device_unknown;
  }

  void _showTwoFactorSetup() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.security, color: AppTheme.primaryGold),
            SizedBox(width: 8),
            Text('تفعيل المصادقة الثنائية'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('اختر طريقة المصادقة الثنائية:'),
            SizedBox(height: 16),
            _buildTwoFactorMethod('رسالة نصية', Icons.sms, () {
              Navigator.pop(context);
              _setupSMSTwoFactor();
            }),
            _buildTwoFactorMethod('تطبيق المصادقة', Icons.smartphone, () {
              Navigator.pop(context);
              _setupAppTwoFactor();
            }),
            _buildTwoFactorMethod('البريد الإلكتروني', Icons.email, () {
              Navigator.pop(context);
              _setupEmailTwoFactor();
            }),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  Widget _buildTwoFactorMethod(
      String title, IconData icon, VoidCallback onTap) {
    return ListTile(
      leading: Icon(icon, color: AppTheme.primaryGold),
      title: Text(title),
      trailing: Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }

  void _setupSMSTwoFactor() {
    setState(() {
      _twoFactorEnabled = true;
    });
    _showSnackBar('تم تفعيل المصادقة الثنائية عبر الرسائل النصية');
  }

  void _setupAppTwoFactor() {
    setState(() {
      _twoFactorEnabled = true;
    });
    _showSnackBar('تم تفعيل المصادقة الثنائية عبر التطبيق');
  }

  void _setupEmailTwoFactor() {
    setState(() {
      _twoFactorEnabled = true;
    });
    _showSnackBar('تم تفعيل المصادقة الثنائية عبر البريد الإلكتروني');
  }

  void _disableTwoFactor() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إلغاء المصادقة الثنائية'),
        content: Text(
            'هل أنت متأكد من إلغاء المصادقة الثنائية؟ هذا قد يقلل من أمان حسابك.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _twoFactorEnabled = false;
              });
              _showSnackBar('تم إلغاء المصادقة الثنائية');
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppTheme.coralRed),
            child: Text('إلغاء التفعيل'),
          ),
        ],
      ),
    );
  }

  void _terminateSession(Map<String, dynamic> session) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إنهاء الجلسة'),
        content: Text('هل تريد إنهاء جلسة ${session['device']}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _activeSessions.remove(session);
              });
              _showSnackBar('تم إنهاء الجلسة');
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppTheme.coralRed),
            child: Text('إنهاء'),
          ),
        ],
      ),
    );
  }

  void _terminateAllSessions() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إنهاء جميع الجلسات'),
        content: Text(
            'هل تريد إنهاء جميع الجلسات النشطة؟ ستحتاج لتسجيل الدخول مرة أخرى.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _activeSessions.removeWhere((session) => !session['current']);
              });
              _showSnackBar('تم إنهاء جميع الجلسات الأخرى');
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppTheme.coralRed),
            child: Text('إنهاء الكل'),
          ),
        ],
      ),
    );
  }

  void _showFullSecurityLog() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => _SecurityLogScreen(logs: _securityLog),
      ),
    );
  }

  void _changePassword() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.lock, color: AppTheme.primaryGold),
            SizedBox(width: 8),
            Text('تغيير كلمة المرور'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: InputDecoration(
                labelText: 'كلمة المرور الحالية',
                prefixIcon: Icon(Icons.lock_outline),
              ),
              obscureText: true,
            ),
            SizedBox(height: 16),
            TextField(
              decoration: InputDecoration(
                labelText: 'كلمة المرور الجديدة',
                prefixIcon: Icon(Icons.lock),
              ),
              obscureText: true,
            ),
            SizedBox(height: 16),
            TextField(
              decoration: InputDecoration(
                labelText: 'تأكيد كلمة المرور الجديدة',
                prefixIcon: Icon(Icons.lock),
              ),
              obscureText: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showSnackBar('تم تغيير كلمة المرور بنجاح');
            },
            child: Text('تغيير'),
          ),
        ],
      ),
    );
  }

  void _setupSecurityQuestions() {
    _showSnackBar('سيتم إضافة أسئلة الأمان قريباً');
  }

  void _showPasswordManager() {
    _showSnackBar('سيتم إضافة مدير كلمات المرور قريباً');
  }

  void _saveSecuritySettings() {
    _showSnackBar('تم حفظ إعدادات الأمان');
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.primaryGold,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }
}

class _SecurityLogScreen extends StatelessWidget {
  final List<Map<String, dynamic>> logs;

  const _SecurityLogScreen({required this.logs});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    PremiumIconButton(
                      icon: Icons.arrow_back_ios,
                      onPressed: () => Navigator.pop(context),
                      hasGradient: false,
                      backgroundColor: AppTheme.surfaceLight,
                      iconColor: AppTheme.primaryGold,
                    ),
                    const SizedBox(width: 16),
                    Text(
                      'سجل الأمان الكامل',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.all(20),
                  itemCount: logs.length,
                  itemBuilder: (context, index) {
                    final log = logs[index];
                    return Container(
                      margin: const EdgeInsets.only(bottom: 12),
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: AppTheme.surfaceLight,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        children: [
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: log['success']
                                  ? AppTheme.emeraldGreen
                                  : AppTheme.coralRed,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Icon(
                              log['success'] ? Icons.check : Icons.close,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  log['action'],
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: AppTheme.textPrimary,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  '${log['device']} • ${log['time']}',
                                  style: TextStyle(
                                    color: AppTheme.textSecondary,
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
