import 'package:flutter/material.dart';
import '../../main.dart';
import '../../theme/app_theme.dart';
import '../../widgets/premium_card.dart';
import '../../widgets/premium_widgets.dart';
import '../../widgets/animated_widgets.dart';
import '../settings/settings_screen.dart';
import '../auth/login_screen.dart';

class PremiumProfileScreen extends StatefulWidget {
  const PremiumProfileScreen({super.key});

  @override
  State<PremiumProfileScreen> createState() => _PremiumProfileScreenState();
}

class _PremiumProfileScreenState extends State<PremiumProfileScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  Map<String, dynamic>? _userProfile;
  Map<String, dynamic>? _userStats;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _loadUserData();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    try {
      final userId = supabase.auth.currentUser!.id;

      // Load user profile
      final profileResponse = await supabase
          .from('user_profiles')
          .select()
          .eq('user_id', userId)
          .maybeSingle();

      // Load user stats
      final statsResponse = await supabase
          .from('user_stats')
          .select()
          .eq('user_id', userId)
          .maybeSingle();

      if (mounted) {
        setState(() {
          _userProfile = profileResponse;
          _userStats = statsResponse;
          _isLoading = false;
        });
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _logout() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: const Text(
          'تسجيل الخروج',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimary,
          ),
        ),
        content: const Text(
          'هل أنت متأكد من رغبتك في تسجيل الخروج؟',
          style: TextStyle(color: AppTheme.textSecondary),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text(
              'إلغاء',
              style: TextStyle(color: AppTheme.textSecondary),
            ),
          ),
          PremiumButton(
            text: 'تسجيل الخروج',
            onPressed: () => Navigator.pop(context, true),
            backgroundColor: AppTheme.coralRed,
            height: 40,
            width: 120,
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await supabase.auth.signOut();
      if (mounted) {
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(
              builder: (context) => LoginScreen(onLanguageChanged: (lang) {})),
          (route) => false,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: SafeArea(
          child: _isLoading
              ? const Center(
                  child: CircularProgressIndicator(
                    color: AppTheme.primaryGold,
                  ),
                )
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      _buildProfileHeader(),
                      const SizedBox(height: 30),
                      _buildStatsGrid(),
                      const SizedBox(height: 30),
                      _buildMenuItems(),
                      const SizedBox(height: 30),
                      _buildLogoutButton(),
                    ],
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    final user = supabase.auth.currentUser;
    final fullName = user?.userMetadata?['full_name'] ?? 'المستخدم';
    final email = user?.email ?? '';
    final avatarUrl = user?.userMetadata?['avatar_url'];

    return SlideInAnimation(
      child: PremiumCard(
        hasGradient: true,
        child: Column(
          children: [
            Row(
              children: [
                FloatingWidget(
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      gradient: AppTheme.primaryGradient,
                      boxShadow: AppTheme.primaryShadow,
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(18),
                      child: avatarUrl != null
                          ? ProgressiveImageLoader(
                              imageUrl: avatarUrl,
                              width: 76,
                              height: 76,
                              fit: BoxFit.cover,
                            )
                          : Container(
                              color: Colors.white.withOpacity(0.2),
                              child: Icon(
                                Icons.person,
                                size: 40,
                                color: Colors.white,
                              ),
                            ),
                    ),
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        fullName,
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textLight,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        email,
                        style: TextStyle(
                          color: AppTheme.textLight.withOpacity(0.9),
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Text(
                          'عضو ذهبي',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                PremiumIconButton(
                  icon: Icons.edit,
                  onPressed: () {
                    // Edit profile
                  },
                  hasGradient: false,
                  backgroundColor: Colors.white.withOpacity(0.2),
                  iconColor: Colors.white,
                  size: 44,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsGrid() {
    return SlideInAnimation(
      delay: 200,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إحصائياتك',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            mainAxisSpacing: 16,
            crossAxisSpacing: 16,
            childAspectRatio: 1.3,
            children: [
              PremiumStatsCard(
                title: 'الجلسات المكتملة',
                value: '${_userStats?['completed_sessions'] ?? 0}',
                icon: Icons.fitness_center,
                color: AppTheme.emeraldGreen,
              ),
              PremiumStatsCard(
                title: 'السعرات المحروقة',
                value: '${_userStats?['calories_burned'] ?? 0}',
                icon: Icons.local_fire_department,
                color: AppTheme.warmOrange,
              ),
              PremiumStatsCard(
                title: 'أيام النشاط',
                value: '${_userStats?['active_days'] ?? 0}',
                icon: Icons.calendar_today,
                color: AppTheme.lightBlue,
              ),
              PremiumStatsCard(
                title: 'النقاط المكتسبة',
                value: '${_userStats?['points'] ?? 0}',
                icon: Icons.star,
                color: AppTheme.primaryGold,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItems() {
    final menuItems = [
      {
        'icon': Icons.person_outline,
        'title': 'الملف الشخصي',
        'subtitle': 'تحديث معلوماتك الشخصية',
        'color': AppTheme.lightBlue,
        'onTap': () {
          // مثال: الانتقال لشاشة إعداد الملف الشخصي
          Navigator.pushNamed(context, '/profile_setup');
        },
      },
      {
        'icon': Icons.fitness_center,
        'title': 'تاريخ التمارين',
        'subtitle': 'مراجعة تاريخ جلساتك التدريبية',
        'color': AppTheme.emeraldGreen,
        'onTap': () {
          // مثال: الانتقال لشاشة الجلسات
          Navigator.pushNamed(context, '/sessions');
        },
      },
      {
        'icon': Icons.restaurant_menu,
        'title': 'خططي الغذائية',
        'subtitle': 'إدارة خططك الغذائية',
        'color': AppTheme.warmOrange,
        'onTap': () {
          // مثال: الانتقال لشاشة الخطط
          Navigator.pushNamed(context, '/plans');
        },
      },
      {
        'icon': Icons.card_membership,
        'title': 'الاشتراكات',
        'subtitle': 'إدارة اشتراكاتك',
        'color': AppTheme.primaryGold,
        'onTap': () {
          // مثال: الانتقال لشاشة الاشتراكات
          Navigator.pushNamed(context, '/subscriptions');
        },
      },
      {
        'icon': Icons.settings,
        'title': 'الإعدادات',
        'subtitle': 'تخصيص تطبيقك',
        'color': AppTheme.textSecondary,
        'onTap': () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const SettingsScreen(),
            ),
          );
        },
      },
      {
        'icon': Icons.help_outline,
        'title': 'المساعدة والدعم',
        'subtitle': 'احصل على المساعدة',
        'color': AppTheme.lightBlue,
        'onTap': () {
          // مثال: الانتقال لشاشة المساعدة
          Navigator.pushNamed(context, '/help');
        },
      },
    ];

    return SlideInAnimation(
      delay: 400,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'الإعدادات',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          ...menuItems.map((item) => Container(
                margin: const EdgeInsets.only(bottom: 12),
                child: PremiumCard(
                  child: ListTile(
                    contentPadding: const EdgeInsets.all(16),
                    leading: Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            (item['color'] as Color).withOpacity(0.2),
                            (item['color'] as Color).withOpacity(0.1),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: Icon(
                        item['icon'] as IconData,
                        color: item['color'] as Color,
                        size: 24,
                      ),
                    ),
                    title: Text(
                      item['title'] as String,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                    subtitle: Text(
                      item['subtitle'] as String,
                      style: const TextStyle(
                        color: AppTheme.textSecondary,
                        fontSize: 13,
                      ),
                    ),
                    trailing: const Icon(
                      Icons.arrow_forward_ios,
                      color: AppTheme.textMuted,
                      size: 16,
                    ),
                    onTap: item['onTap'] as VoidCallback,
                  ),
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildLogoutButton() {
    return SlideInAnimation(
      delay: 600,
      child: PremiumButton(
        text: 'تسجيل الخروج',
        onPressed: _logout,
        backgroundColor: AppTheme.coralRed,
        icon: Icons.logout,
        width: double.infinity,
        height: 56,
      ),
    );
  }
}
