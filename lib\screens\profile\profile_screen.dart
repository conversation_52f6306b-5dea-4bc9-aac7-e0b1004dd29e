import 'package:flutter/material.dart';
import '../../main.dart';
import '../../theme/app_theme.dart';
import '../../widgets/custom_app_bar.dart';
import '../auth/login_screen.dart';
import 'edit_profile_screen.dart';
import '../settings/settings_screen.dart';

class ProfileScreen extends StatefulWidget {
  final Function(String) onLanguageChanged;
  const ProfileScreen({super.key, required this.onLanguageChanged});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  Map<String, dynamic>? _userProfile;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserProfile();
  }

  Future<void> _loadUserProfile() async {
    try {
      final user = supabase.auth.currentUser;
      if (user != null) {
        final profileResponse = await supabase
            .from('trainees_profiles')
            .select()
            .eq('user_id', user.id)
            .single();
        setState(() {
          _userProfile = profileResponse;
        });
      }
    } catch (error) {
      print('Error loading user profile: $error');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'الملف الشخصي',
        showBackButton: false,
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: AppTheme.primaryGold),
            )
          : SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  CircleAvatar(
                    radius: 60,
                    backgroundColor: AppTheme.primaryGold,
                    backgroundImage: supabase.auth.currentUser
                                ?.userMetadata?['avatar_url'] !=
                            null
                        ? NetworkImage(supabase
                            .auth.currentUser!.userMetadata!['avatar_url'])
                        : null,
                    child: supabase.auth.currentUser
                                ?.userMetadata?['avatar_url'] ==
                            null
                        ? Text(
                            supabase.auth.currentUser
                                    ?.userMetadata?['full_name']
                                    ?.substring(0, 1)
                                    .toUpperCase() ??
                                'م',
                            style: const TextStyle(
                              fontSize: 50,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                            ),
                          )
                        : null,
                  ),
                  const SizedBox(height: 20),
                  Text(
                    supabase.auth.currentUser?.userMetadata?['full_name'] ??
                        'المتدرب',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    supabase.auth.currentUser?.email ?? '',
                    style: const TextStyle(
                      fontSize: 16,
                      color: AppTheme.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 30),
                  _buildProfileOption(
                    'تعديل الملف الشخصي',
                    Icons.edit,
                    () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const EditProfileScreen(),
                        ),
                      );
                    },
                  ),
                  _buildProfileOption(
                    'الإعدادات',
                    Icons.settings,
                    () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => SettingsScreen(
                            onLanguageChanged: widget.onLanguageChanged,
                          ),
                        ),
                      );
                    },
                  ),
                  _buildProfileOption(
                    'تسجيل الخروج',
                    Icons.logout,
                    () async {
                      await supabase.auth.signOut();
                      Navigator.of(context).pushAndRemoveUntil(
                        MaterialPageRoute(
                          builder: (context) => LoginScreen(
                            onLanguageChanged: widget.onLanguageChanged,
                          ),
                        ),
                        (route) => false,
                      );
                    },
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildProfileOption(String title, IconData icon, VoidCallback onTap) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
        side: BorderSide(
          color: AppTheme.primaryGold.withOpacity(0.3),
        ),
      ),
      child: ListTile(
        leading: Icon(icon, color: AppTheme.primaryGold),
        title: Text(
          title,
          style: const TextStyle(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        trailing:
            const Icon(Icons.arrow_forward_ios, color: AppTheme.primaryGold),
        onTap: onTap,
      ),
    );
  }
}
