import 'package:flutter/material.dart';
import '../../main.dart';
import '../../theme/app_theme.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/custom_button.dart';

class SessionDetailsScreen extends StatefulWidget {
  final Map<String, dynamic> session;

  const SessionDetailsScreen({
    super.key,
    required this.session,
  });

  @override
  State<SessionDetailsScreen> createState() => _SessionDetailsScreenState();
}

class _SessionDetailsScreenState extends State<SessionDetailsScreen> {
  bool _isLoading = false;
  final _feedbackController = TextEditingController();
  int _rating = 0;

  @override
  void dispose() {
    _feedbackController.dispose();
    super.dispose();
  }

  Future<void> _cancelSession() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إلغاء الجلسة'),
        content: const Text('هل أنت متأكد من رغبتك في إلغاء هذه الجلسة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('تراجع'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('إلغاء الجلسة'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _isLoading = true;
      });

      try {
        await supabase.from('sessions').update({
          'status': 'cancelled',
          'updated_at': DateTime.now().toIso8601String(),
        }).eq('id', widget.session['id']);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إلغاء الجلسة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context);
        }
      } catch (error) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في إلغاء الجلسة: $error'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  Future<void> _rescheduleSession() async {
    // Show date/time picker dialog
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 90)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppTheme.primaryGold,
            ),
          ),
          child: child!,
        );
      },
    );

    if (date == null) return;

    final time = await showTimePicker(
      context: context,
      initialTime: const TimeOfDay(hour: 9, minute: 0),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppTheme.primaryGold,
            ),
          ),
          child: child!,
        );
      },
    );

    if (time == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final newDateTime = DateTime(
        date.year,
        date.month,
        date.day,
        time.hour,
        time.minute,
      );

      await supabase.from('sessions').update({
        'scheduled_at': newDateTime.toIso8601String(),
        'status': 'scheduled',
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('id', widget.session['id']);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إعادة جدولة الجلسة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إعادة جدولة الجلسة: $error'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _submitFeedback() async {
    if (_feedbackController.text.trim().isEmpty && _rating == 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('الرجاء إضافة تقييم أو تعليق'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Update session with feedback
      await supabase.from('sessions').update({
        'trainee_feedback': _feedbackController.text.trim(),
        'rating': _rating > 0 ? _rating : null,
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('id', widget.session['id']);

      // Create review if rating is provided
      if (_rating > 0) {
        await supabase.from('reviews').upsert({
          'trainee_id': supabase.auth.currentUser!.id,
          'trainer_id': widget.session['trainer_id'],
          'session_id': widget.session['id'],
          'rating': _rating,
          'comment': _feedbackController.text.trim().isEmpty
              ? null
              : _feedbackController.text.trim(),
        });
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إرسال التقييم بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إرسال التقييم: $error'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final session = widget.session;
    final trainer = session['trainers'];
    final trainerName = trainer?['users']?['full_name'] ?? 'غير محدد';
    final scheduledAt = DateTime.parse(session['scheduled_at']);
    final status = session['status'] ?? 'scheduled';
    final isCompleted = status == 'completed';
    final isPast = scheduledAt.isBefore(DateTime.now());
    final canCancel = status == 'scheduled' && !isPast;
    final canReschedule = status == 'scheduled';

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'تفاصيل الجلسة',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeaderCard(session, trainerName, scheduledAt, status),
            const SizedBox(height: 20),
            _buildDetailsCard(session),
            const SizedBox(height: 20),
            if (trainer != null) _buildTrainerCard(trainer),
            if (trainer != null) const SizedBox(height: 20),
            if (isCompleted && session['trainee_feedback'] == null)
              _buildFeedbackCard(),
            if (isCompleted && session['trainee_feedback'] == null)
              const SizedBox(height: 20),
            if (session['trainee_feedback'] != null)
              _buildExistingFeedbackCard(session),
            if (session['trainee_feedback'] != null) const SizedBox(height: 20),
            _buildActionButtons(canCancel, canReschedule, isCompleted),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderCard(Map<String, dynamic> session, String trainerName,
      DateTime scheduledAt, String status) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: AppTheme.primaryGold.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: const Icon(
                    Icons.fitness_center,
                    color: AppTheme.primaryGold,
                    size: 32,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        session['title'] ?? 'جلسة تدريبية',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'مع المدرب: $trainerName',
                        style: const TextStyle(
                          color: AppTheme.textSecondary,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
                _buildStatusChip(status),
              ],
            ),
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 16),
            Row(
              children: [
                const Icon(Icons.schedule,
                    color: AppTheme.primaryGold, size: 20),
                const SizedBox(width: 8),
                Text(
                  _formatDateTime(scheduledAt),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.textPrimary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.timer, color: AppTheme.primaryGold, size: 20),
                const SizedBox(width: 8),
                Text(
                  '${session['duration_minutes'] ?? 60} دقيقة',
                  style: const TextStyle(
                    fontSize: 16,
                    color: AppTheme.textSecondary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailsCard(Map<String, dynamic> session) {
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل الجلسة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimary,
              ),
            ),
            const SizedBox(height: 12),
            _buildDetailRow(
                'نوع الجلسة', _getSessionTypeText(session['session_type'])),
            if (session['description'] != null)
              _buildDetailRow('الوصف', session['description']),
            if (session['location'] != null)
              _buildDetailRow('المكان', session['location']),
            if (session['meeting_link'] != null)
              _buildDetailRow('رابط الاجتماع', session['meeting_link']),
            if (session['notes'] != null)
              _buildDetailRow('ملاحظات', session['notes']),
          ],
        ),
      ),
    );
  }

  Widget _buildTrainerCard(Map<String, dynamic> trainer) {
    final trainerUser = trainer['users'];

    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات المدرب',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimary,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: AppTheme.primaryGold.withOpacity(0.1),
                  child: trainerUser['avatar_url'] != null
                      ? ClipOval(
                          child: Image.network(
                            trainerUser['avatar_url'],
                            width: 60,
                            height: 60,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) =>
                                const Icon(Icons.person,
                                    color: AppTheme.primaryGold, size: 30),
                          ),
                        )
                      : const Icon(Icons.person,
                          color: AppTheme.primaryGold, size: 30),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        trainerUser['full_name'],
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                      // Add more trainer details if available
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeedbackCard() {
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تقييم الجلسة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimary,
              ),
            ),
            const SizedBox(height: 16),
            const Text('التقييم:',
                style: TextStyle(fontWeight: FontWeight.w500)),
            const SizedBox(height: 8),
            Row(
              children: List.generate(5, (index) {
                return IconButton(
                  onPressed: () {
                    setState(() {
                      _rating = index + 1;
                    });
                  },
                  icon: Icon(
                    index < _rating ? Icons.star : Icons.star_border,
                    color: AppTheme.primaryGold,
                    size: 28,
                  ),
                );
              }),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _feedbackController,
              decoration: const InputDecoration(
                labelText: 'تعليقك على الجلسة',
                hintText: 'اكتب تجربتك مع هذه الجلسة...',
                alignLabelWithHint: true,
              ),
              maxLines: 4,
            ),
            const SizedBox(height: 16),
            CustomButton(
              text: 'إرسال التقييم',
              onPressed: _submitFeedback,
              isLoading: _isLoading,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExistingFeedbackCard(Map<String, dynamic> session) {
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تقييمك للجلسة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimary,
              ),
            ),
            const SizedBox(height: 12),
            if (session['rating'] != null) ...[
              Row(
                children: [
                  const Text('التقييم: ',
                      style: TextStyle(fontWeight: FontWeight.w500)),
                  Row(
                    children: List.generate(5, (index) {
                      return Icon(
                        index < session['rating']
                            ? Icons.star
                            : Icons.star_border,
                        color: AppTheme.primaryGold,
                        size: 20,
                      );
                    }),
                  ),
                ],
              ),
              const SizedBox(height: 8),
            ],
            Text(
              'التعليق:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 4),
            Text(
              session['trainee_feedback'] ?? 'لا يوجد تعليق',
              style: const TextStyle(color: AppTheme.textSecondary),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(
      bool canCancel, bool canReschedule, bool isCompleted) {
    return Column(
      children: [
        if (canReschedule)
          CustomButton(
            text: 'إعادة جدولة الجلسة',
            onPressed: _rescheduleSession,
            backgroundColor: Colors.blue,
            isLoading: _isLoading,
          ),
        if (canReschedule) const SizedBox(height: 12),
        if (canCancel)
          CustomButton(
            text: 'إلغاء الجلسة',
            onPressed: _cancelSession,
            backgroundColor: Colors.red,
            isLoading: _isLoading,
          ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: AppTheme.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(color: AppTheme.textPrimary),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color backgroundColor;
    Color textColor;
    String text;

    switch (status) {
      case 'scheduled':
        backgroundColor = Colors.blue.withOpacity(0.1);
        textColor = Colors.blue;
        text = 'مجدولة';
        break;
      case 'completed':
        backgroundColor = Colors.green.withOpacity(0.1);
        textColor = Colors.green;
        text = 'مكتملة';
        break;
      case 'cancelled':
        backgroundColor = Colors.red.withOpacity(0.1);
        textColor = Colors.red;
        text = 'ملغية';
        break;
      case 'reschedule_requested':
        backgroundColor = Colors.orange.withOpacity(0.1);
        textColor = Colors.orange;
        text = 'طلب إعادة جدولة';
        break;
      case 'no_show':
        backgroundColor = Colors.grey.withOpacity(0.1);
        textColor = Colors.grey;
        text = 'لم يحضر';
        break;
      default:
        backgroundColor = AppTheme.primaryGold.withOpacity(0.1);
        textColor = AppTheme.primaryGold;
        text = status;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: textColor,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  String _getSessionTypeText(String? type) {
    switch (type) {
      case 'training':
        return 'تدريب';
      case 'consultation':
        return 'استشارة';
      case 'assessment':
        return 'تقييم';
      default:
        return type ?? 'غير محدد';
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = dateTime.difference(now);

    if (difference.inDays == 0) {
      return 'اليوم ${_formatTime(dateTime)}';
    } else if (difference.inDays == 1) {
      return 'غداً ${_formatTime(dateTime)}';
    } else if (difference.inDays == -1) {
      return 'أمس ${_formatTime(dateTime)}';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${_formatTime(dateTime)}';
    }
  }

  String _formatTime(DateTime dateTime) {
    final hour = dateTime.hour;
    final minute = dateTime.minute.toString().padLeft(2, '0');
    final period = hour >= 12 ? 'م' : 'ص';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
    return '$displayHour:$minute $period';
  }
}
