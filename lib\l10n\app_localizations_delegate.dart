import 'package:flutter/material.dart';
import 'app_localizations.dart';
import 'app_localizations_ar.dart';
import 'app_localizations_en.dart';

class AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['ar', 'en'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    switch (locale.languageCode) {
      case 'ar':
        return AppLocalizationsAr();
      case 'en':
        return AppLocalizationsEn();
      default:
        return AppLocalizationsAr();
    }
  }

  @override
  bool shouldReload(AppLocalizationsDelegate old) => false;
}
