-- Row Level Security (RLS) Policies

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.trainers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.trainees_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.trainer_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.nutrition_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.workout_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.progress_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_messages ENABLE ROW LEVEL SECURITY;

-- Users policies
CREATE POLICY "Users can view their own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- Trainers policies
CREATE POLICY "Anyone can view active trainers" ON public.trainers
    FOR SELECT USING (is_available = true AND is_verified = true);

CREATE POLICY "Trainers can update their own profile" ON public.trainers
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Trainers can insert their own profile" ON public.trainers
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Trainees profiles policies
CREATE POLICY "Trainees can view their own profile" ON public.trainees_profiles
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Trainees can update their own profile" ON public.trainees_profiles
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Trainees can insert their own profile" ON public.trainees_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Trainers can view their trainees profiles" ON public.trainees_profiles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.trainer_assignments ta
            JOIN public.trainers t ON t.id = ta.trainer_id
            WHERE ta.trainee_id = user_id
            AND t.user_id = auth.uid()
            AND ta.status = 'active'
        )
    );

-- Trainer assignments policies
CREATE POLICY "Trainees can view their assignments" ON public.trainer_assignments
    FOR SELECT USING (auth.uid() = trainee_id);

CREATE POLICY "Trainers can view their assignments" ON public.trainer_assignments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.trainers t
            WHERE t.id = trainer_id AND t.user_id = auth.uid()
        )
    );

CREATE POLICY "Trainees can create assignments" ON public.trainer_assignments
    FOR INSERT WITH CHECK (auth.uid() = trainee_id);

CREATE POLICY "Trainers can update assignments" ON public.trainer_assignments
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.trainers t
            WHERE t.id = trainer_id AND t.user_id = auth.uid()
        )
    );

-- Sessions policies
CREATE POLICY "Users can view their own sessions" ON public.sessions
    FOR SELECT USING (
        auth.uid() = trainee_id OR
        EXISTS (
            SELECT 1 FROM public.trainers t
            WHERE t.id = trainer_id AND t.user_id = auth.uid()
        )
    );

CREATE POLICY "Trainers can create sessions" ON public.sessions
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.trainers t
            WHERE t.id = trainer_id AND t.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own sessions" ON public.sessions
    FOR UPDATE USING (
        auth.uid() = trainee_id OR
        EXISTS (
            SELECT 1 FROM public.trainers t
            WHERE t.id = trainer_id AND t.user_id = auth.uid()
        )
    );

-- Nutrition plans policies
CREATE POLICY "Trainees can view their nutrition plans" ON public.nutrition_plans
    FOR SELECT USING (auth.uid() = trainee_id);

CREATE POLICY "Trainers can view their assigned nutrition plans" ON public.nutrition_plans
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.trainers t
            WHERE t.id = trainer_id AND t.user_id = auth.uid()
        )
    );

CREATE POLICY "Trainers can create nutrition plans" ON public.nutrition_plans
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.trainers t
            WHERE t.id = trainer_id AND t.user_id = auth.uid()
        )
    );

CREATE POLICY "Trainers can update their nutrition plans" ON public.nutrition_plans
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.trainers t
            WHERE t.id = trainer_id AND t.user_id = auth.uid()
        )
    );

CREATE POLICY "Trainees can update completion status" ON public.nutrition_plans
    FOR UPDATE USING (auth.uid() = trainee_id)
    WITH CHECK (auth.uid() = trainee_id);

-- Workout plans policies (similar to nutrition plans)
CREATE POLICY "Trainees can view their workout plans" ON public.workout_plans
    FOR SELECT USING (auth.uid() = trainee_id);

CREATE POLICY "Trainers can view their assigned workout plans" ON public.workout_plans
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.trainers t
            WHERE t.id = trainer_id AND t.user_id = auth.uid()
        )
    );

CREATE POLICY "Trainers can create workout plans" ON public.workout_plans
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.trainers t
            WHERE t.id = trainer_id AND t.user_id = auth.uid()
        )
    );

CREATE POLICY "Trainers can update their workout plans" ON public.workout_plans
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.trainers t
            WHERE t.id = trainer_id AND t.user_id = auth.uid()
        )
    );

CREATE POLICY "Trainees can update completion status" ON public.workout_plans
    FOR UPDATE USING (auth.uid() = trainee_id)
    WITH CHECK (auth.uid() = trainee_id);

-- Progress tracking policies
CREATE POLICY "Trainees can manage their progress" ON public.progress_tracking
    FOR ALL USING (auth.uid() = trainee_id);

CREATE POLICY "Trainers can view their trainees progress" ON public.progress_tracking
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.trainer_assignments ta
            JOIN public.trainers t ON t.id = ta.trainer_id
            WHERE ta.trainee_id = trainee_id
            AND t.user_id = auth.uid()
            AND ta.status = 'active'
        )
    );

-- Reviews policies
CREATE POLICY "Trainees can create reviews" ON public.reviews
    FOR INSERT WITH CHECK (auth.uid() = trainee_id);

CREATE POLICY "Users can view reviews" ON public.reviews
    FOR SELECT USING (true); -- Reviews are public

CREATE POLICY "Trainees can update their own reviews" ON public.reviews
    FOR UPDATE USING (auth.uid() = trainee_id);

-- Notifications policies
CREATE POLICY "Users can view their own notifications" ON public.notifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" ON public.notifications
    FOR UPDATE USING (auth.uid() = user_id);

-- Chat messages policies
CREATE POLICY "Users can view their own messages" ON public.chat_messages
    FOR SELECT USING (auth.uid() = sender_id OR auth.uid() = receiver_id);

CREATE POLICY "Users can send messages" ON public.chat_messages
    FOR INSERT WITH CHECK (auth.uid() = sender_id);

CREATE POLICY "Users can update their sent messages" ON public.chat_messages
    FOR UPDATE USING (auth.uid() = sender_id OR auth.uid() = receiver_id);

-- Subscriptions policies
CREATE POLICY "Trainees can view their subscriptions" ON public.subscriptions
    FOR SELECT USING (auth.uid() = trainee_id);

CREATE POLICY "Trainers can view their subscriptions" ON public.subscriptions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.trainers t
            WHERE t.id = trainer_id AND t.user_id = auth.uid()
        )
    );

-- Payments policies
CREATE POLICY "Users can view their payments" ON public.payments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.subscriptions s
            WHERE s.id = subscription_id
            AND (s.trainee_id = auth.uid() OR EXISTS (
                SELECT 1 FROM public.trainers t
                WHERE t.id = s.trainer_id AND t.user_id = auth.uid()
            ))
        )
    );
