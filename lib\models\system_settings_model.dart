class SystemSetting {
  final String id;
  final String key;
  final Map<String, dynamic> value;
  final String? description;
  final DateTime createdAt;
  final DateTime updatedAt;

  SystemSetting({
    required this.id,
    required this.key,
    required this.value,
    this.description,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SystemSetting.fromJson(Map<String, dynamic> json) {
    return SystemSetting(
      id: _getSafeString(json, 'id', ''),
      key: _getSafeString(json, 'key', ''),
      value: _getSafeMap(json, 'value'),
      description: _getSafeString(json, 'description'),
      createdAt: _getSafeDateTime(json, 'created_at') ?? DateTime.now(),
      updatedAt: _getSafeDateTime(json, 'updated_at') ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'key': key,
      'value': value,
      'description': description,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // دوال مساعدة آمنة لتحويل البيانات
  static String _getSafeString(Map<String, dynamic> data, String key, [String? defaultValue]) {
    final value = data[key];
    if (value == null) return defaultValue ?? '';
    return value.toString();
  }

  static Map<String, dynamic> _getSafeMap(Map<String, dynamic> data, String key) {
    final value = data[key];
    if (value == null) return {};
    if (value is Map<String, dynamic>) return value;
    if (value is Map) return Map<String, dynamic>.from(value);
    return {};
  }

  static DateTime? _getSafeDateTime(Map<String, dynamic> data, String key) {
    final value = data[key];
    if (value == null) return null;
    if (value is String) {
      return DateTime.tryParse(value);
    }
    return null;
  }

  // دوال للحصول على قيم محددة من value
  String? getStringValue(String key) {
    final val = value[key];
    return val?.toString();
  }

  double? getDoubleValue(String key) {
    final val = value[key];
    if (val is double) return val;
    if (val is int) return val.toDouble();
    if (val is String) return double.tryParse(val);
    return null;
  }

  int? getIntValue(String key) {
    final val = value[key];
    if (val is int) return val;
    if (val is double) return val.toInt();
    if (val is String) return int.tryParse(val);
    return null;
  }

  bool? getBoolValue(String key) {
    final val = value[key];
    if (val is bool) return val;
    if (val is String) return val.toLowerCase() == 'true';
    return null;
  }

  List<String>? getStringListValue(String key) {
    final val = value[key];
    if (val is List) {
      return val.map((item) => item.toString()).toList();
    }
    return null;
  }

  SystemSetting copyWith({
    String? id,
    String? key,
    Map<String, dynamic>? value,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SystemSetting(
      id: id ?? this.id,
      key: key ?? this.key,
      value: value ?? this.value,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'SystemSetting(id: $id, key: $key, value: $value)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SystemSetting && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// فئة لإدارة إعدادات النظام
class SystemSettings {
  final Map<String, SystemSetting> _settings = {};

  // إضافة إعداد
  void addSetting(SystemSetting setting) {
    _settings[setting.key] = setting;
  }

  // الحصول على إعداد
  SystemSetting? getSetting(String key) {
    return _settings[key];
  }

  // الحصول على قيمة نصية
  String? getStringValue(String key, [String? defaultValue]) {
    final setting = _settings[key];
    if (setting == null) return defaultValue;
    return setting.getStringValue('value') ?? defaultValue;
  }

  // الحصول على قيمة رقمية
  double? getDoubleValue(String key, [double? defaultValue]) {
    final setting = _settings[key];
    if (setting == null) return defaultValue;
    return setting.getDoubleValue('value') ?? defaultValue;
  }

  // الحصول على قيمة صحيحة
  int? getIntValue(String key, [int? defaultValue]) {
    final setting = _settings[key];
    if (setting == null) return defaultValue;
    return setting.getIntValue('value') ?? defaultValue;
  }

  // الحصول على قيمة منطقية
  bool? getBoolValue(String key, [bool? defaultValue]) {
    final setting = _settings[key];
    if (setting == null) return defaultValue;
    return setting.getBoolValue('value') ?? defaultValue;
  }

  // إعدادات افتراضية للتطبيق
  static Map<String, Map<String, dynamic>> get defaultSettings => {
    'app_currency': {
      'value': {'value': 'SAR'},
      'description': 'العملة المستخدمة في التطبيق',
    },
    'default_session_duration': {
      'value': {'value': 60},
      'description': 'مدة الجلسة الافتراضية بالدقائق',
    },
    'subscription_plans_enabled': {
      'value': {'value': true},
      'description': 'تفعيل خطط الاشتراك',
    },
    'chat_enabled': {
      'value': {'value': true},
      'description': 'تفعيل نظام الدردشة',
    },
    'notifications_enabled': {
      'value': {'value': true},
      'description': 'تفعيل الإشعارات',
    },
    'progress_tracking_enabled': {
      'value': {'value': true},
      'description': 'تفعيل تتبع التقدم',
    },
    'reviews_enabled': {
      'value': {'value': true},
      'description': 'تفعيل نظام التقييمات',
    },
    'max_file_size_mb': {
      'value': {'value': 10},
      'description': 'الحد الأقصى لحجم الملف بالميجابايت',
    },
    'supported_image_formats': {
      'value': {'value': ['jpg', 'jpeg', 'png', 'gif', 'webp']},
      'description': 'صيغ الصور المدعومة',
    },
    'supported_video_formats': {
      'value': {'value': ['mp4', 'avi', 'mov']},
      'description': 'صيغ الفيديو المدعومة',
    },
    'session_reminder_hours': {
      'value': {'value': 1},
      'description': 'عدد الساعات قبل الجلسة لإرسال التذكير',
    },
    'auto_backup_enabled': {
      'value': {'value': false},
      'description': 'تفعيل النسخ الاحتياطي التلقائي',
    },
    'maintenance_mode': {
      'value': {'value': false},
      'description': 'وضع الصيانة',
    },
    'app_version': {
      'value': {'value': '1.0.0'},
      'description': 'إصدار التطبيق',
    },
    'terms_version': {
      'value': {'value': '1.0'},
      'description': 'إصدار الشروط والأحكام',
    },
    'privacy_version': {
      'value': {'value': '1.0'},
      'description': 'إصدار سياسة الخصوصية',
    },
  };

  // تحميل الإعدادات الافتراضية
  void loadDefaultSettings() {
    defaultSettings.forEach((key, data) {
      final setting = SystemSetting(
        id: key,
        key: key,
        value: data['value'] as Map<String, dynamic>,
        description: data['description'] as String?,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      addSetting(setting);
    });
  }

  // الحصول على جميع الإعدادات
  List<SystemSetting> get allSettings => _settings.values.toList();

  // البحث في الإعدادات
  List<SystemSetting> searchSettings(String query) {
    return _settings.values
        .where((setting) =>
            setting.key.toLowerCase().contains(query.toLowerCase()) ||
            (setting.description?.toLowerCase().contains(query.toLowerCase()) ?? false))
        .toList();
  }

  // تصدير الإعدادات
  Map<String, dynamic> exportSettings() {
    return _settings.map((key, setting) => MapEntry(key, setting.toJson()));
  }

  // استيراد الإعدادات
  void importSettings(Map<String, dynamic> settingsData) {
    settingsData.forEach((key, data) {
      if (data is Map<String, dynamic>) {
        final setting = SystemSetting.fromJson(data);
        addSetting(setting);
      }
    });
  }
}
