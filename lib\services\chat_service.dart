import 'package:flutter/foundation.dart';
import '../main.dart';

class ChatService {
  static Future<List<Map<String, dynamic>>> getMessages({
    required String userId,
    required String otherUserId,
    int limit = 50,
  }) async {
    try {
      final response = await supabase
          .from('chat_messages')
          .select('*, sender:users!sender_id(full_name, avatar_url)')
          .or('sender_id.eq.$userId,receiver_id.eq.$userId')
          .or('sender_id.eq.$otherUserId,receiver_id.eq.$otherUserId')
          .order('created_at', ascending: false)
          .limit(limit);

      return List<Map<String, dynamic>>.from(response);
    } catch (error) {
      if (kDebugMode) {
        print('Error fetching messages: $error');
      }
      return [];
    }
  }

  static Future<bool> sendMessage({
    required String senderId,
    required String receiverId,
    required String message,
    String messageType = 'text',
    String? fileUrl,
  }) async {
    try {
      await supabase.from('chat_messages').insert({
        'sender_id': senderId,
        'receiver_id': receiverId,
        'message': message,
        'message_type': messageType,
        'file_url': fileUrl,
        'created_at': DateTime.now().toIso8601String(),
      });

      return true;
    } catch (error) {
      if (kDebugMode) {
        print('Error sending message: $error');
      }
      return false;
    }
  }

  static Future<void> markMessagesAsRead({
    required String userId,
    required String senderId,
  }) async {
    try {
      await supabase
          .from('chat_messages')
          .update({'is_read': true})
          .eq('receiver_id', userId)
          .eq('sender_id', senderId)
          .eq('is_read', false);
    } catch (error) {
      if (kDebugMode) {
        print('Error marking messages as read: $error');
      }
    }
  }

  static Future<List<Map<String, dynamic>>> getConversations(String userId) async {
    try {
      // Get latest message for each conversation
      final response = await supabase.rpc('get_user_conversations', params: {
        'user_uuid': userId,
      });

      return List<Map<String, dynamic>>.from(response);
    } catch (error) {
      if (kDebugMode) {
        print('Error fetching conversations: $error');
      }
      return [];
    }
  }
}
