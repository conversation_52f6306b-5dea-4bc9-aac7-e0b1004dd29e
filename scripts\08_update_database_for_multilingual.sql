-- Update database to support multilingual content

-- Add language columns to existing tables
ALTER TABLE public.trainers 
ADD COLUMN bio_en TEXT,
ADD COLUMN specialization_en TEXT[] DEFAULT '{}';

ALTER TABLE public.nutrition_plans 
ADD COLUMN title_en TEXT,
ADD COLUMN description_en TEXT,
ADD COLUMN instructions_en TEXT;

ALTER TABLE public.workout_plans 
ADD COLUMN title_en TEXT,
ADD COLUMN description_en TEXT,
ADD COLUMN instructions_en TEXT;

ALTER TABLE public.sessions 
ADD COLUMN title_en TEXT,
ADD COLUMN description_en TEXT;

ALTER TABLE public.notifications 
ADD COLUMN title_en TEXT,
ADD COLUMN message_en TEXT;

-- Update system settings with multilingual data
UPDATE public.system_settings 
SET value = jsonb_build_object(
    'ar', '["خسارة الوزن", "بناء العضل", "تمارين القلب", "التغذية الرياضية", "إعادة التأهيل", "اللياقة العامة", "تدريب كبار السن", "تدريب الأطفال"]',
    'en', '["Weight Loss", "Muscle Building", "Cardio Training", "Sports Nutrition", "Rehabilitation", "General Fitness", "Senior Training", "Youth Training"]'
)
WHERE key = 'trainer_specializations';

UPDATE public.system_settings 
SET value = jsonb_build_object(
    'ar', '["السكري", "ضغط الدم", "أمراض القلب", "التهاب المفاصل", "هشاشة العظام", "الربو", "مشاكل الظهر", "إصابات رياضية"]',
    'en', '["Diabetes", "Hypertension", "Heart Disease", "Arthritis", "Osteoporosis", "Asthma", "Back Problems", "Sports Injuries"]'
)
WHERE key = 'health_conditions';

UPDATE public.system_settings 
SET value = jsonb_build_object(
    'ar', '["نباتي", "عالي البروتين", "قليل الكربوهيدرات", "خالي من الجلوتين", "كيتو", "متوسطي", "خالي من اللاكتوز", "حلال"]',
    'en', '["Vegetarian", "High Protein", "Low Carb", "Gluten Free", "Keto", "Mediterranean", "Lactose Free", "Halal"]'
)
WHERE key = 'dietary_preferences';

-- Function to get localized content
CREATE OR REPLACE FUNCTION get_localized_text(
    text_ar TEXT,
    text_en TEXT,
    language_code TEXT DEFAULT 'ar'
)
RETURNS TEXT AS $$
BEGIN
    IF language_code = 'en' AND text_en IS NOT NULL THEN
        RETURN text_en;
    ELSE
        RETURN text_ar;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to get localized system settings
CREATE OR REPLACE FUNCTION get_localized_system_setting(
    setting_key TEXT,
    language_code TEXT DEFAULT 'ar'
)
RETURNS JSONB AS $$
DECLARE
    setting_value JSONB;
BEGIN
    SELECT value INTO setting_value
    FROM public.system_settings
    WHERE key = setting_key;
    
    IF setting_value ? language_code THEN
        RETURN setting_value->language_code;
    ELSE
        RETURN setting_value->'ar';
    END IF;
END;
$$ LANGUAGE plpgsql;
