import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocalizationService {
  static const String _languageKey = 'selected_language';
  static const String arabicCode = 'ar';
  static const String englishCode = 'en';
  
  static SharedPreferences? _prefs;
  
  static Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
  }
  
  static String get currentLanguage {
    return _prefs?.getString(_languageKey) ?? arabicCode;
  }
  
  static bool get isArabic => currentLanguage == arabicCode;
  static bool get isEnglish => currentLanguage == englishCode;
  
  static Future<void> setLanguage(String languageCode) async {
    await _prefs?.setString(_languageKey, languageCode);
  }
  
  static Locale get currentLocale {
    return Locale(currentLanguage);
  }
  
  static List<Locale> get supportedLocales {
    return const [
      Locale(arabicCode),
      Locale(englishCode),
    ];
  }
  
  static String getLanguageName(String code) {
    switch (code) {
      case arabicCode:
        return 'العربية';
      case englishCode:
        return 'English';
      default:
        return 'العربية';
    }
  }
}
