import 'app_localizations.dart';

class AppLocalizationsAr extends AppLocalizations {
  @override
  String get appName => 'BALANCE';

  @override
  String get appSlogan => 'توازن حياتك، توازن صحتك';

  // Authentication
  @override
  String get welcome => 'مرحباً بك في BALANCE';

  @override
  String get login => 'تسجيل الدخول';

  @override
  String get register => 'إنشاء حساب';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get password => 'كلمة المرور';

  @override
  String get confirmPassword => 'تأكيد كلمة المرور';

  @override
  String get fullName => 'الاسم الكامل';

  @override
  String get forgotPassword => 'نسيت كلمة المرور؟';

  @override
  String get dontHaveAccount => 'ليس لديك حساب؟ ';

  @override
  String get alreadyHaveAccount => 'لديك حساب بالفعل؟ ';

  @override
  String get createAccount => 'إنشاء حساب جديد';

  @override
  String get signInWithGoogle => 'تسجيل الدخول بـ Google';

  @override
  String get or => 'أو';

  @override
  String get signOut => 'تسجيل الخروج';

  // Navigation
  @override
  String get home => 'الرئيسية';

  @override
  String get plans => 'الخطط';

  @override
  String get sessions => 'الجلسات';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get trainers => 'المدربين';

  @override
  String get notifications => 'الإشعارات';

  @override
  String get chat => 'المحادثات';

  @override
  String get settings => 'الإعدادات';

  // Profile Setup
  @override
  String get profileSetup => 'إعداد الملف الشخصي';

  @override
  String get tellUsAboutYou => 'أخبرنا عن نفسك';

  @override
  String get basicInfo => 'المعلومات الأساسية';

  @override
  String get age => 'العمر';

  @override
  String get gender => 'الجنس';

  @override
  String get male => 'ذكر';

  @override
  String get female => 'أنثى';

  @override
  String get weight => 'الوزن';

  @override
  String get height => 'الطول';

  @override
  String get fitnessGoal => 'الهدف الصحي';

  @override
  String get healthConditions => 'الحالة الصحية';

  @override
  String get dietaryPreferences => 'التفضيلات الغذائية';

  @override
  String get saveProfile => 'حفظ الملف الشخصي';

  // Fitness Goals
  @override
  String get loseWeight => 'خسارة الوزن';

  @override
  String get gainWeight => 'زيادة الوزن';

  @override
  String get maintainWeight => 'الحفاظ على الوزن';

  @override
  String get buildMuscle => 'بناء العضلات';

  @override
  String get improveEndurance => 'تحسين التحمل';

  @override
  String get generalFitness => 'لياقة عامة';

  // Home Screen
  @override
  String get quickStats => 'إحصائياتك السريعة';

  @override
  String get currentTrainer => 'مدربك الحالي';

  @override
  String get noTrainerYet => 'لم تختر مدرباً بعد';

  @override
  String get chooseTrainer => 'اختيار المدرب';

  @override
  String get nextSession => 'الجلسة القادمة';

  @override
  String get quickActions => 'الإجراءات السريعة';

  @override
  String get nutritionPlans => 'الخطط الغذائية';

  @override
  String get workoutPlans => 'الخطط الرياضية';

  // Trainers
  @override
  String get selectTrainer => 'اختيار المدرب';

  @override
  String get filterBySpecialization => 'فلترة حسب التخصص';

  @override
  String get allSpecializations => 'جميع التخصصات';

  @override
  String get rating => 'التقييم';

  @override
  String get experience => 'الخبرة';

  @override
  String get yearsExperience => 'سنوات خبرة';

  @override
  String get pricePerSession => 'ريال/جلسة';

  @override
  String get details => 'التفاصيل';

  @override
  String get select => 'اختيار';

  @override
  String get aboutTrainer => 'نبذة عن المدرب:';

  @override
  String get close => 'إغلاق';

  @override
  String get selectThisTrainer => 'اختيار المدرب';

  // Plans
  @override
  String get myPlans => 'خططي';

  @override
  String get nutritionPlan => 'خطة غذائية';

  @override
  String get workoutPlan => 'خطة رياضية';

  @override
  String get noPlanYet => 'لا توجد خطط حالياً';

  @override
  String get trainerWillSend => 'سيقوم مدربك بإرسال خطة قريباً';

  @override
  String get completed => 'مكتملة';

  @override
  String get markAsCompleted => 'تم تنفيذ الخطة';

  @override
  String get meals => 'الوجبات:';

  @override
  String get exercises => 'التمارين:';

  @override
  String get sets => 'مجموعات';

  @override
  String get reps => 'تكرار';

  // Sessions
  @override
  String get mySessions => 'جلساتي';

  @override
  String get upcoming => 'القادمة';

  @override
  String get past => 'السابقة';

  @override
  String get noUpcomingSessions => 'لا توجد جلسات قادمة';

  @override
  String get noPastSessions => 'لا توجد جلسات سابقة';

  @override
  String get bookNewSession => 'احجز جلسة جديدة مع مدربك';

  @override
  String get scheduled => 'مجدولة';

  @override
  String get cancelled => 'ملغية';

  @override
  String get rescheduleRequested => 'طلب تغيير موعد';

  @override
  String get reschedule => 'تغيير الموعد';

  @override
  String get withTrainer => 'مع المدرب:';

  // Notifications
  @override
  String get myNotifications => 'إشعاراتي';

  @override
  String get markAllAsRead => 'تحديد الكل كمقروء';

  @override
  String get noNotifications => 'لا توجد إشعارات';

  @override
  String get newNutritionPlan => 'خطة غذائية جديدة';

  @override
  String get newWorkoutPlan => 'خطة رياضية جديدة';

  @override
  String get sessionReminder => 'تذكير بالجلسة';

  @override
  String get sessionBooked => 'جلسة مجدولة';

  // Chat
  @override
  String get messages => 'الرسائل';

  @override
  String get typeMessage => 'اكتب رسالة...';

  @override
  String get send => 'إرسال';

  @override
  String get online => 'متصل';

  @override
  String get offline => 'غير متصل';

  // Common
  @override
  String get save => 'حفظ';

  @override
  String get cancel => 'إلغاء';

  @override
  String get delete => 'حذف';

  @override
  String get edit => 'تعديل';

  @override
  String get update => 'تحديث';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get error => 'خطأ';

  @override
  String get success => 'نجح';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get ok => 'حسناً';

  @override
  String get yes => 'نعم';

  @override
  String get no => 'لا';

  @override
  String get required => 'مطلوب';

  @override
  String get optional => 'اختياري';

  @override
  String get kg => 'كجم';

  @override
  String get cm => 'سم';

  @override
  String get years => 'سنة';

  @override
  String get minutes => 'دقيقة';

  @override
  String get hours => 'ساعة';

  @override
  String get days => 'يوم';

  @override
  String get weeks => 'أسبوع';

  @override
  String get months => 'شهر';

  @override
  String get sar => 'ريال';

  // Validation Messages
  @override
  String get emailRequired => 'يرجى إدخال البريد الإلكتروني';

  @override
  String get emailInvalid => 'يرجى إدخال بريد إلكتروني صحيح';

  @override
  String get passwordRequired => 'يرجى إدخال كلمة المرور';

  @override
  String get passwordTooShort => 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';

  @override
  String get passwordsNotMatch => 'كلمة المرور غير متطابقة';

  @override
  String get nameRequired => 'يرجى إدخال الاسم الكامل';

  @override
  String get ageRequired => 'يرجى إدخال العمر';

  @override
  String get ageInvalid => 'عمر غير صحيح';

  @override
  String get weightRequired => 'يرجى إدخال الوزن';

  @override
  String get weightInvalid => 'وزن غير صحيح';

  @override
  String get heightRequired => 'يرجى إدخال الطول';

  @override
  String get heightInvalid => 'طول غير صحيح';

  // Success Messages
  @override
  String get profileUpdated => 'تم تحديث الملف الشخصي بنجاح';

  @override
  String get planCompleted => 'تم تسجيل إكمال الخطة بنجاح!';

  @override
  String get trainerSelected => 'تم إرسال طلب الربط بالمدرب بنجاح';

  @override
  String get sessionRescheduled => 'تم إرسال طلب تغيير الموعد';

  @override
  String get notificationsSent => 'تم إرسال الإشعارات';

  // Error Messages
  @override
  String get unexpectedError => 'حدث خطأ غير متوقع';

  @override
  String get networkError => 'خطأ في الاتصال بالشبكة';

  @override
  String get authError => 'خطأ في المصادقة';

  @override
  String get permissionDenied => 'تم رفض الإذن';

  // Settings
  @override
  String get language => 'اللغة';

  @override
  String get arabic => 'العربية';

  @override
  String get english => 'English';

  @override
  String get changeLanguage => 'تغيير اللغة';

  @override
  String get privacySecurity => 'الخصوصية والأمان';

  @override
  String get helpSupport => 'المساعدة والدعم';

  @override
  String get aboutApp => 'حول التطبيق';

  @override
  String get version => 'الإصدار';

  // BMI Categories
  @override
  String get underweight => 'نقص في الوزن';

  @override
  String get normalWeight => 'وزن طبيعي';

  @override
  String get overweight => 'زيادة في الوزن';

  @override
  String get obese => 'سمنة';

  // Activity Levels
  @override
  String get sedentary => 'قليل النشاط (لا توجد تمارين)';

  @override
  String get lightlyActive => 'نشاط خفيف (تمارين خفيفة 1-3 أيام/أسبوع)';

  @override
  String get moderatelyActive => 'نشاط متوسط (تمارين متوسطة 3-5 أيام/أسبوع)';

  @override
  String get veryActive => 'نشاط عالي (تمارين قوية 6-7 أيام/أسبوع)';

  @override
  String get extremelyActive =>
      'نشاط عالي جداً (تمارين قوية يومياً + عمل بدني)';

  // Time Ago
  @override
  String get now => 'الآن';

  @override
  String get minuteAgo => 'منذ دقيقة';

  @override
  String get minutesAgo => 'منذ دقائق';

  @override
  String get hourAgo => 'منذ ساعة';

  @override
  String get hoursAgo => 'منذ ساعات';

  @override
  String get dayAgo => 'منذ يوم';

  @override
  String get daysAgo => 'منذ أيام';

  @override
  String get weekAgo => 'منذ أسبوع';

  @override
  String get weeksAgo => 'منذ أسابيع';

  @override
  String get monthAgo => 'منذ شهر';

  @override
  String get monthsAgo => 'منذ أشهر';

  @override
  String get yearAgo => 'منذ سنة';

  @override
  String get yearsAgo => 'منذ سنوات';
}
