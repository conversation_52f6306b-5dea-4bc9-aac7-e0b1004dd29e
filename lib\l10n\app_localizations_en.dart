import 'app_localizations.dart';

class AppLocalizationsEn extends AppLocalizations {
  @override
  String get appName => 'BALANCE';

  @override
  String get appSlogan => 'Balance your life, balance your health';

  // Authentication
  @override
  String get welcome => 'Welcome to BALANCE';

  @override
  String get login => 'Sign In';

  @override
  String get register => 'Sign Up';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get fullName => 'Full Name';

  @override
  String get forgotPassword => 'Forgot Password?';

  @override
  String get dontHaveAccount => "Don't have an account? ";

  @override
  String get alreadyHaveAccount => 'Already have an account? ';

  @override
  String get createAccount => 'Create New Account';

  @override
  String get signInWithGoogle => 'Sign in with Google';

  @override
  String get or => 'OR';

  @override
  String get signOut => 'Sign Out';

  // Navigation
  @override
  String get home => 'Home';

  @override
  String get plans => 'Plans';

  @override
  String get sessions => 'Sessions';

  @override
  String get profile => 'Profile';

  @override
  String get trainers => 'Trainers';

  @override
  String get notifications => 'Notifications';

  @override
  String get chat => 'Chat';

  @override
  String get settings => 'Settings';

  // Profile Setup
  @override
  String get profileSetup => 'Profile Setup';

  @override
  String get tellUsAboutYou => 'Tell us about yourself';

  @override
  String get basicInfo => 'Basic Information';

  @override
  String get age => 'Age';

  @override
  String get gender => 'Gender';

  @override
  String get male => 'Male';

  @override
  String get female => 'Female';

  @override
  String get weight => 'Weight';

  @override
  String get height => 'Height';

  @override
  String get fitnessGoal => 'Fitness Goal';

  @override
  String get healthConditions => 'Health Conditions';

  @override
  String get dietaryPreferences => 'Dietary Preferences';

  @override
  String get saveProfile => 'Save Profile';

  // Fitness Goals
  @override
  String get loseWeight => 'Lose Weight';

  @override
  String get gainWeight => 'Gain Weight';

  @override
  String get maintainWeight => 'Maintain Weight';

  @override
  String get buildMuscle => 'Build Muscle';

  @override
  String get improveEndurance => 'Improve Endurance';

  @override
  String get generalFitness => 'General Fitness';

  // Home Screen
  @override
  String get quickStats => 'Your Quick Stats';

  @override
  String get currentTrainer => 'Current Trainer';

  @override
  String get noTrainerYet => 'No trainer selected yet';

  @override
  String get chooseTrainer => 'Choose Trainer';

  @override
  String get nextSession => 'Next Session';

  @override
  String get quickActions => 'Quick Actions';

  @override
  String get nutritionPlans => 'Nutrition Plans';

  @override
  String get workoutPlans => 'Workout Plans';

  // Trainers
  @override
  String get selectTrainer => 'Select Trainer';

  @override
  String get filterBySpecialization => 'Filter by Specialization';

  @override
  String get allSpecializations => 'All Specializations';

  @override
  String get rating => 'Rating';

  @override
  String get experience => 'Experience';

  @override
  String get yearsExperience => 'years experience';

  @override
  String get pricePerSession => 'SAR/session';

  @override
  String get details => 'Details';

  @override
  String get select => 'Select';

  @override
  String get aboutTrainer => 'About Trainer:';

  @override
  String get close => 'Close';

  @override
  String get selectThisTrainer => 'Select Trainer';

  // Plans
  @override
  String get myPlans => 'My Plans';

  @override
  String get nutritionPlan => 'Nutrition Plan';

  @override
  String get workoutPlan => 'Workout Plan';

  @override
  String get noPlanYet => 'No plans available yet';

  @override
  String get trainerWillSend => 'Your trainer will send a plan soon';

  @override
  String get completed => 'Completed';

  @override
  String get markAsCompleted => 'Mark as Completed';

  @override
  String get meals => 'Meals:';

  @override
  String get exercises => 'Exercises:';

  @override
  String get sets => 'sets';

  @override
  String get reps => 'reps';

  // Sessions
  @override
  String get mySessions => 'My Sessions';

  @override
  String get upcoming => 'Upcoming';

  @override
  String get past => 'Past';

  @override
  String get noUpcomingSessions => 'No upcoming sessions';

  @override
  String get noPastSessions => 'No past sessions';

  @override
  String get bookNewSession => 'Book a new session with your trainer';

  @override
  String get scheduled => 'Scheduled';

  @override
  String get cancelled => 'Cancelled';

  @override
  String get rescheduleRequested => 'Reschedule Requested';

  @override
  String get reschedule => 'Reschedule';

  @override
  String get withTrainer => 'with Trainer:';

  // Notifications
  @override
  String get myNotifications => 'My Notifications';

  @override
  String get markAllAsRead => 'Mark All as Read';

  @override
  String get noNotifications => 'No notifications';

  @override
  String get newNutritionPlan => 'New Nutrition Plan';

  @override
  String get newWorkoutPlan => 'New Workout Plan';

  @override
  String get sessionReminder => 'Session Reminder';

  @override
  String get sessionBooked => 'Session Booked';

  // Chat
  @override
  String get messages => 'Messages';

  @override
  String get typeMessage => 'Type a message...';

  @override
  String get send => 'Send';

  @override
  String get online => 'Online';

  @override
  String get offline => 'Offline';

  // Common
  @override
  String get save => 'Save';

  @override
  String get cancel => 'Cancel';

  @override
  String get delete => 'Delete';

  @override
  String get edit => 'Edit';

  @override
  String get update => 'Update';

  @override
  String get loading => 'Loading...';

  @override
  String get error => 'Error';

  @override
  String get success => 'Success';

  @override
  String get retry => 'Retry';

  @override
  String get ok => 'OK';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get required => 'Required';

  @override
  String get optional => 'Optional';

  @override
  String get kg => 'kg';

  @override
  String get cm => 'cm';

  @override
  String get years => 'years';

  @override
  String get minutes => 'minutes';

  @override
  String get hours => 'hours';

  @override
  String get days => 'days';

  @override
  String get weeks => 'weeks';

  @override
  String get months => 'months';

  @override
  String get sar => 'SAR';

  // Validation Messages
  @override
  String get emailRequired => 'Please enter your email';

  @override
  String get emailInvalid => 'Please enter a valid email';

  @override
  String get passwordRequired => 'Please enter your password';

  @override
  String get passwordTooShort => 'Password must be at least 6 characters';

  @override
  String get passwordsNotMatch => 'Passwords do not match';

  @override
  String get nameRequired => 'Please enter your full name';

  @override
  String get ageRequired => 'Please enter your age';

  @override
  String get ageInvalid => 'Invalid age';

  @override
  String get weightRequired => 'Please enter your weight';

  @override
  String get weightInvalid => 'Invalid weight';

  @override
  String get heightRequired => 'Please enter your height';

  @override
  String get heightInvalid => 'Invalid height';

  // Success Messages
  @override
  String get profileUpdated => 'Profile updated successfully';

  @override
  String get planCompleted => 'Plan completion recorded successfully!';

  @override
  String get trainerSelected => 'Trainer request sent successfully';

  @override
  String get sessionRescheduled => 'Reschedule request sent';

  @override
  String get notificationsSent => 'Notifications sent';

  // Error Messages
  @override
  String get unexpectedError => 'An unexpected error occurred';

  @override
  String get networkError => 'Network connection error';

  @override
  String get authError => 'Authentication error';

  @override
  String get permissionDenied => 'Permission denied';

  // Settings
  @override
  String get language => 'Language';

  @override
  String get arabic => 'العربية';

  @override
  String get english => 'English';

  @override
  String get changeLanguage => 'Change Language';

  @override
  String get privacySecurity => 'Privacy & Security';

  @override
  String get helpSupport => 'Help & Support';

  @override
  String get aboutApp => 'About App';

  @override
  String get version => 'Version';

  // BMI Categories
  @override
  String get underweight => 'Underweight';

  @override
  String get normalWeight => 'Normal Weight';

  @override
  String get overweight => 'Overweight';

  @override
  String get obese => 'Obese';

  // Activity Levels
  @override
  String get sedentary => 'Sedentary (no exercise)';

  @override
  String get lightlyActive => 'Lightly Active (light exercise 1-3 days/week)';

  @override
  String get moderatelyActive =>
      'Moderately Active (moderate exercise 3-5 days/week)';

  @override
  String get veryActive => 'Very Active (hard exercise 6-7 days/week)';

  @override
  String get extremelyActive =>
      'Extremely Active (very hard exercise daily + physical job)';

  // Time Ago
  @override
  String get now => 'Now';

  @override
  String get minuteAgo => '1 minute ago';

  @override
  String get minutesAgo => 'minutes ago';

  @override
  String get hourAgo => '1 hour ago';

  @override
  String get hoursAgo => 'hours ago';

  @override
  String get dayAgo => '1 day ago';

  @override
  String get daysAgo => 'days ago';

  @override
  String get weekAgo => '1 week ago';

  @override
  String get weeksAgo => 'weeks ago';

  @override
  String get monthAgo => '1 month ago';

  @override
  String get monthsAgo => 'months ago';

  @override
  String get yearAgo => '1 year ago';

  @override
  String get yearsAgo => 'years ago';
}
