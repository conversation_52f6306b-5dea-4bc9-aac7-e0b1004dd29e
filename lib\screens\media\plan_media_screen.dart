import 'package:flutter/material.dart';
import '../../models/media_model.dart';
import '../../widgets/custom_app_bar.dart';

class PlanMediaScreen extends StatefulWidget {
  final String planId;
  final String planType; // 'workout' or 'nutrition'
  final String planName;

  const PlanMediaScreen({
    super.key,
    required this.planId,
    required this.planType,
    required this.planName,
  });

  @override
  State<PlanMediaScreen> createState() => _PlanMediaScreenState();
}

class _PlanMediaScreenState extends State<PlanMediaScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  List<WorkoutMedia> _workoutMedia = [];
  List<NutritionMedia> _nutritionMedia = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadMedia();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadMedia() async {
    setState(() => _isLoading = true);

    try {
      if (widget.planType == 'workout') {
        // TODO: تحميل وسائط التمارين من Supabase
        // final response = await Supabase.instance.client
        //     .from('workout_media')
        //     .select()
        //     .eq('plan_id', widget.planId);

        _workoutMedia = _generateSampleWorkoutMedia();
      } else {
        // TODO: تحميل وسائط التغذية من Supabase
        // final response = await Supabase.instance.client
        //     .from('nutrition_media')
        //     .select()
        //     .eq('plan_id', widget.planId);

        _nutritionMedia = _generateSampleNutritionMedia();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل الوسائط: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  List<WorkoutMedia> _generateSampleWorkoutMedia() {
    return [
      WorkoutMedia(
        id: 'workout_media_1',
        workoutPlanId: widget.planId,
        mediaType: MediaType.video,
        mediaUrl: 'https://example.com/pushup_demo.mp4',
        title: 'تمرين الضغط',
        description: 'شرح تفصيلي لتمرين الضغط الصحيح',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      WorkoutMedia(
        id: 'workout_media_2',
        workoutPlanId: widget.planId,
        mediaType: MediaType.image,
        mediaUrl: 'https://example.com/squat_guide.jpg',
        title: 'تمرين القرفصاء',
        description: 'الوضعية الصحيحة لتمرين القرفصاء',
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
      ),
      WorkoutMedia(
        id: 'workout_media_3',
        workoutPlanId: widget.planId,
        mediaType: MediaType.video,
        mediaUrl: 'https://example.com/pullup_tutorial.mp4',
        title: 'تمرين العقلة',
        description: 'كيفية أداء تمرين العقلة للمبتدئين',
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
      ),
    ];
  }

  List<NutritionMedia> _generateSampleNutritionMedia() {
    return [
      NutritionMedia(
        id: 'nutrition_media_1',
        nutritionPlanId: widget.planId,
        mediaType: MediaType.image,
        mediaUrl: 'https://example.com/healthy_breakfast.jpg',
        title: 'إفطار صحي',
        description: 'وجبة إفطار متوازنة غنية بالبروتين',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      NutritionMedia(
        id: 'nutrition_media_2',
        nutritionPlanId: widget.planId,
        mediaType: MediaType.video,
        mediaUrl: 'https://example.com/protein_smoothie.mp4',
        title: 'سموثي البروتين',
        description: 'طريقة تحضير سموثي البروتين اللذيذ',
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
      ),
      NutritionMedia(
        id: 'nutrition_media_3',
        nutritionPlanId: widget.planId,
        mediaType: MediaType.image,
        mediaUrl: 'https://example.com/quinoa_salad.jpg',
        title: 'سلطة الكينوا',
        description: 'سلطة كينوا صحية ومشبعة',
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: CustomAppBar(
        title: 'وسائط ${widget.planName}',
        showBackButton: true,
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              icon: const Icon(Icons.play_circle_outline),
              text: widget.planType == 'workout'
                  ? 'فيديوهات التمارين'
                  : 'وصفات الطعام',
            ),
            const Tab(
              icon: Icon(Icons.image),
              text: 'الصور التوضيحية',
            ),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildVideosTab(),
                _buildImagesTab(),
              ],
            ),
    );
  }

  Widget _buildVideosTab() {
    final videos = widget.planType == 'workout'
        ? _workoutMedia.where((m) => m.mediaType == MediaType.video).toList()
        : _nutritionMedia.where((m) => m.mediaType == MediaType.video).toList();

    if (videos.isEmpty) {
      return _buildEmptyState('لا توجد فيديوهات متاحة', Icons.videocam_off);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: videos.length,
      itemBuilder: (context, index) {
        if (widget.planType == 'workout') {
          return _buildWorkoutVideoCard(videos[index] as WorkoutMedia);
        } else {
          return _buildNutritionVideoCard(videos[index] as NutritionMedia);
        }
      },
    );
  }

  Widget _buildImagesTab() {
    final images = widget.planType == 'workout'
        ? _workoutMedia.where((m) => m.mediaType == MediaType.image).toList()
        : _nutritionMedia.where((m) => m.mediaType == MediaType.image).toList();

    if (images.isEmpty) {
      return _buildEmptyState('لا توجد صور متاحة', Icons.image_not_supported);
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        mainAxisSpacing: 16,
        crossAxisSpacing: 16,
        childAspectRatio: 0.8,
      ),
      itemCount: images.length,
      itemBuilder: (context, index) {
        if (widget.planType == 'workout') {
          return _buildWorkoutImageCard(images[index] as WorkoutMedia);
        } else {
          return _buildNutritionImageCard(images[index] as NutritionMedia);
        }
      },
    );
  }

  Widget _buildWorkoutVideoCard(WorkoutMedia media) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        elevation: 2,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => _playVideo(media.mediaUrl),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.play_circle_filled,
                    color: Colors.blue,
                    size: 40,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        media.title ?? '',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        media.description ?? '',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNutritionVideoCard(NutritionMedia media) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        elevation: 2,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => _playVideo(media.mediaUrl),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.play_circle_filled,
                    color: Colors.orange,
                    size: 40,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        media.title ?? '',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        media.description ?? '',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWorkoutImageCard(WorkoutMedia media) {
    return Material(
      color: Colors.white,
      borderRadius: BorderRadius.circular(12),
      elevation: 2,
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _viewImage(media.mediaUrl),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius:
                      const BorderRadius.vertical(top: Radius.circular(12)),
                ),
                child: const Center(
                  child: Icon(
                    Icons.fitness_center,
                    color: Colors.blue,
                    size: 40,
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    media.title ?? '',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    media.description ?? '',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNutritionImageCard(NutritionMedia media) {
    return Material(
      color: Colors.white,
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _viewImage(media.mediaUrl),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius:
                      const BorderRadius.vertical(top: Radius.circular(12)),
                ),
                child: const Center(
                  child: Icon(
                    Icons.restaurant,
                    color: Colors.orange,
                    size: 40,
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    media.title ?? '',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    media.description ?? '',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(String message, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Colors.grey.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _playVideo(String url) {
    // TODO: تشغيل الفيديو باستخدام مشغل فيديو
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تشغيل الفيديو: $url')),
    );
  }

  void _viewImage(String url) {
    // TODO: عرض الصورة في شاشة منفصلة
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('عرض الصورة: $url')),
    );
  }
}
