import 'package:flutter/material.dart';
import '../../main.dart';
import '../../theme/app_theme.dart';
import '../home/<USER>';

class ProfileSetupScreen extends StatefulWidget {
  final Function(String) onLanguageChanged;
  const ProfileSetupScreen({super.key, required this.onLanguageChanged});

  @override
  State<ProfileSetupScreen> createState() => _ProfileSetupScreenState();
}

class _ProfileSetupScreenState extends State<ProfileSetupScreen> {
  final _formKey = GlobalKey<FormState>();
  final _ageController = TextEditingController();
  final _weightController = TextEditingController();
  final _heightController = TextEditingController();

  String _selectedGender = 'male';
  String _selectedGoal = 'lose_weight';
  List<String> _selectedConditions = [];
  List<String> _selectedPreferences = [];
  bool _isLoading = false;

  final List<Map<String, String>> _goals = [
    {'value': 'lose_weight', 'label': 'خسارة الوزن'},
    {'value': 'gain_weight', 'label': 'زيادة الوزن'},
    {'value': 'maintain_weight', 'label': 'الحفاظ على الوزن'},
    {'value': 'build_muscle', 'label': 'بناء العضلات'},
  ];

  final List<Map<String, String>> _conditions = [
    {'value': 'diabetes', 'label': 'السكري'},
    {'value': 'hypertension', 'label': 'ضغط الدم'},
    {'value': 'heart_disease', 'label': 'أمراض القلب'},
    {'value': 'arthritis', 'label': 'التهاب المفاصل'},
  ];

  final List<Map<String, String>> _preferences = [
    {'value': 'vegetarian', 'label': 'نباتي'},
    {'value': 'high_protein', 'label': 'عالي البروتين'},
    {'value': 'low_carb', 'label': 'قليل الكربوهيدرات'},
    {'value': 'gluten_free', 'label': 'خالي من الجلوتين'},
  ];

  @override
  void dispose() {
    _ageController.dispose();
    _weightController.dispose();
    _heightController.dispose();
    super.dispose();
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final user = supabase.auth.currentUser;
      print('USER: ' + (user?.id ?? 'null'));
      print('AGE: ' + _ageController.text);
      print('WEIGHT: ' + _weightController.text);
      print('HEIGHT: ' + _heightController.text);
      print('GENDER: ' + _selectedGender);
      print('GOAL: ' + _selectedGoal);
      print('CONDITIONS: ' + _selectedConditions.toString());
      print('PREFERENCES: ' + _selectedPreferences.toString());
      if (user != null) {
        final response = await supabase.from('trainees_profiles').insert({
          'user_id': user.id,
          'age': int.parse(_ageController.text),
          'weight': double.parse(_weightController.text),
          'height': double.parse(_heightController.text),
          'gender': _selectedGender,
          'fitness_goal': _selectedGoal,
          'health_conditions': _selectedConditions,
          'dietary_preferences': _selectedPreferences,
          'created_at': DateTime.now().toIso8601String(),
        });
        print('INSERT RESPONSE: ' + response.toString());
        if (mounted) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
                builder: (context) =>
                    HomeScreen(onLanguageChanged: widget.onLanguageChanged)),
          );
        }
      } else {
        print('NO USER FOUND');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content:
                  Text('لم يتم العثور على المستخدم. يرجى إعادة تسجيل الدخول.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (error) {
      print('ERROR: ' + error.toString());
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $error'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعداد الملف الشخصي'),
        automaticallyImplyLeading: false,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.darkBackground,
              Color(0xFF1A1A1A),
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const Text(
                    'أخبرنا عن نفسك',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryGold,
                    ),
                  ),
                  const SizedBox(height: 10),
                  const Text(
                    'سنستخدم هذه المعلومات لإنشاء خطة مخصصة لك',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      color: AppTheme.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 30),

                  // Basic Info Card
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'المعلومات الأساسية',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: AppTheme.primaryGold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: TextFormField(
                                  controller: _ageController,
                                  keyboardType: TextInputType.number,
                                  decoration: const InputDecoration(
                                    labelText: 'العمر',
                                    suffixText: 'سنة',
                                  ),
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'مطلوب';
                                    }
                                    final age = int.tryParse(value);
                                    if (age == null || age < 16 || age > 80) {
                                      return 'عمر غير صحيح';
                                    }
                                    return null;
                                  },
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: DropdownButtonFormField<String>(
                                  value: _selectedGender,
                                  decoration: const InputDecoration(
                                    labelText: 'الجنس',
                                  ),
                                  dropdownColor: AppTheme.cardBackground,
                                  items: const [
                                    DropdownMenuItem(
                                      value: 'male',
                                      child: Text('ذكر'),
                                    ),
                                    DropdownMenuItem(
                                      value: 'female',
                                      child: Text('أنثى'),
                                    ),
                                  ],
                                  onChanged: (value) {
                                    setState(() {
                                      _selectedGender = value!;
                                    });
                                  },
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: TextFormField(
                                  controller: _weightController,
                                  keyboardType: TextInputType.number,
                                  decoration: const InputDecoration(
                                    labelText: 'الوزن',
                                    suffixText: 'كجم',
                                  ),
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'مطلوب';
                                    }
                                    final weight = double.tryParse(value);
                                    if (weight == null ||
                                        weight < 30 ||
                                        weight > 300) {
                                      return 'وزن غير صحيح';
                                    }
                                    return null;
                                  },
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: TextFormField(
                                  controller: _heightController,
                                  keyboardType: TextInputType.number,
                                  decoration: const InputDecoration(
                                    labelText: 'الطول',
                                    suffixText: 'سم',
                                  ),
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'مطلوب';
                                    }
                                    final height = double.tryParse(value);
                                    if (height == null ||
                                        height < 120 ||
                                        height > 250) {
                                      return 'طول غير صحيح';
                                    }
                                    return null;
                                  },
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 20),

                  // Fitness Goal Card
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'هدفك الصحي',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: AppTheme.primaryGold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          ..._goals.map((goal) => RadioListTile<String>(
                                title: Text(goal['label']!),
                                value: goal['value']!,
                                groupValue: _selectedGoal,
                                activeColor: AppTheme.primaryGold,
                                onChanged: (value) {
                                  setState(() {
                                    _selectedGoal = value!;
                                  });
                                },
                              )),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 20),

                  // Health Conditions Card
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'الحالة الصحية (اختياري)',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: AppTheme.primaryGold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          ..._conditions.map((condition) => CheckboxListTile(
                                title: Text(condition['label']!),
                                value: _selectedConditions
                                    .contains(condition['value']),
                                activeColor: AppTheme.primaryGold,
                                onChanged: (bool? value) {
                                  setState(() {
                                    if (value == true) {
                                      _selectedConditions
                                          .add(condition['value']!);
                                    } else {
                                      _selectedConditions
                                          .remove(condition['value']);
                                    }
                                  });
                                },
                              )),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 20),

                  // Dietary Preferences Card
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'التفضيلات الغذائية (اختياري)',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: AppTheme.primaryGold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          ..._preferences.map((preference) => CheckboxListTile(
                                title: Text(preference['label']!),
                                value: _selectedPreferences
                                    .contains(preference['value']),
                                activeColor: AppTheme.primaryGold,
                                onChanged: (bool? value) {
                                  setState(() {
                                    if (value == true) {
                                      _selectedPreferences
                                          .add(preference['value']!);
                                    } else {
                                      _selectedPreferences
                                          .remove(preference['value']);
                                    }
                                  });
                                },
                              )),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 30),

                  // Save Button
                  ElevatedButton(
                    onPressed: _isLoading ? null : _saveProfile,
                    child: _isLoading
                        ? const CircularProgressIndicator(color: Colors.black)
                        : const Text(
                            'حفظ الملف الشخصي',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
