class WorkoutPlan {
  final String id;
  final String traineeId;
  final String trainerId;
  final String title;
  final String? description;
  final DateTime startDate;
  final DateTime? endDate;
  final int difficultyLevel;
  final List<dynamic> exercises;
  final List<String> restDays;
  final String? instructions;
  final bool isActive;
  final bool completed;
  final DateTime? completedAt;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<WorkoutMedia> media; // إضافة الميديا

  WorkoutPlan({
    required this.id,
    required this.traineeId,
    required this.trainerId,
    required this.title,
    this.description,
    required this.startDate,
    this.endDate,
    this.difficultyLevel = 1,
    this.exercises = const [],
    this.restDays = const [],
    this.instructions,
    this.isActive = true,
    this.completed = false,
    this.completedAt,
    required this.createdAt,
    required this.updatedAt,
    this.media = const [],
  });

  factory WorkoutPlan.fromJson(Map<String, dynamic> json) {
    List<WorkoutMedia> mediaList = [];
    if (json['workout_media'] != null) {
      mediaList = List<WorkoutMedia>.from(
          json['workout_media'].map((media) => WorkoutMedia.fromJson(media)));
    }

    return WorkoutPlan(
      id: json['id'],
      traineeId: json['trainee_id'],
      trainerId: json['trainer_id'],
      title: json['title'],
      description: json['description'],
      startDate: DateTime.parse(json['start_date']),
      endDate:
          json['end_date'] != null ? DateTime.parse(json['end_date']) : null,
      difficultyLevel: json['difficulty_level'] ?? 1,
      exercises: json['exercises'] ?? [],
      restDays: _parseStringList(json['rest_days']),
      instructions: json['instructions'],
      isActive: json['is_active'] ?? true,
      completed: json['completed'] ?? false,
      completedAt: json['completed_at'] != null
          ? DateTime.parse(json['completed_at'])
          : null,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      media: mediaList,
    );
  }

  static List<String> _parseStringList(dynamic data) {
    if (data == null) return [];

    if (data is List) {
      return data.map((e) => e.toString()).toList();
    }

    if (data is String) {
      if (data.isEmpty) return [];
      try {
        return data
            .split(',')
            .map((e) => e.trim())
            .where((e) => e.isNotEmpty)
            .toList();
      } catch (e) {
        return [data];
      }
    }

    return [];
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'trainee_id': traineeId,
      'trainer_id': trainerId,
      'title': title,
      'description': description,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate?.toIso8601String(),
      'difficulty_level': difficultyLevel,
      'exercises': exercises,
      'rest_days': restDays,
      'instructions': instructions,
      'is_active': isActive,
      'completed': completed,
      'completed_at': completedAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  String get difficultyText {
    switch (difficultyLevel) {
      case 1:
        return 'سهل';
      case 2:
        return 'سهل متوسط';
      case 3:
        return 'متوسط';
      case 4:
        return 'متوسط صعب';
      case 5:
        return 'صعب';
      default:
        return 'متوسط';
    }
  }
}

class WorkoutMedia {
  final String id;
  final String workoutPlanId;
  final String mediaType; // 'image' أو 'video'
  final String mediaUrl;
  final String? title;
  final String? description;
  final DateTime createdAt;

  WorkoutMedia({
    required this.id,
    required this.workoutPlanId,
    required this.mediaType,
    required this.mediaUrl,
    this.title,
    this.description,
    required this.createdAt,
  });

  factory WorkoutMedia.fromJson(Map<String, dynamic> json) {
    return WorkoutMedia(
      id: json['id'],
      workoutPlanId: json['workout_plan_id'],
      mediaType: json['media_type'],
      mediaUrl: json['media_url'],
      title: json['title'],
      description: json['description'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'workout_plan_id': workoutPlanId,
      'media_type': mediaType,
      'media_url': mediaUrl,
      'title': title,
      'description': description,
      'created_at': createdAt.toIso8601String(),
    };
  }

  bool get isImage => mediaType == 'image';
  bool get isVideo => mediaType == 'video';
}
