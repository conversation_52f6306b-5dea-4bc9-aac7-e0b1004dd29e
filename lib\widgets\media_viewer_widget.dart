import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import '../theme/app_theme.dart';
import 'premium_widgets.dart';

class MediaViewerWidget extends StatefulWidget {
  final List<MediaItem> mediaItems;
  final double height;
  final BorderRadius? borderRadius;

  const MediaViewerWidget({
    super.key,
    required this.mediaItems,
    this.height = 300,
    this.borderRadius,
  });

  @override
  State<MediaViewerWidget> createState() => _MediaViewerWidgetState();
}

class _MediaViewerWidgetState extends State<MediaViewerWidget> {
  late PageController _pageController;
  Map<String, VideoPlayerController> _videoControllers = {};
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _videoControllers.values.forEach((controller) {
      controller.dispose();
    });
    super.dispose();
  }

  Future<void> _initializeVideoController(String url) async {
    if (_videoControllers.containsKey(url)) return;

    final controller = VideoPlayerController.networkUrl(Uri.parse(url));
    await controller.initialize();
    _videoControllers[url] = controller;
    if (mounted) setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    if (widget.mediaItems.isEmpty) {
      return Container(
        height: widget.height,
        decoration: BoxDecoration(
          color: AppTheme.surfaceLight,
          borderRadius: widget.borderRadius ?? BorderRadius.circular(20),
        ),
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.image_not_supported,
                size: 50,
                color: AppTheme.textMuted,
              ),
              SizedBox(height: 12),
              Text(
                'لا توجد ميديا متاحة',
                style: TextStyle(
                  color: AppTheme.textMuted,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      height: widget.height,
      child: Stack(
        children: [
          PageView.builder(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            itemCount: widget.mediaItems.length,
            itemBuilder: (context, index) {
              final mediaItem = widget.mediaItems[index];
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 8),
                child: ClipRRect(
                  borderRadius:
                      widget.borderRadius ?? BorderRadius.circular(20),
                  child: _buildMediaItem(mediaItem),
                ),
              );
            },
          ),

          // المؤشرات
          if (widget.mediaItems.length > 1) _buildIndicators(),

          // أزرار التنقل
          if (widget.mediaItems.length > 1) _buildNavigationButtons(),

          // معلومات الميديا
          _buildMediaInfo(widget.mediaItems[_currentIndex]),
        ],
      ),
    );
  }

  Widget _buildMediaItem(MediaItem mediaItem) {
    switch (mediaItem.type) {
      case MediaType.image:
        return _buildImageViewer(mediaItem);
      case MediaType.video:
        return _buildVideoPlayer(mediaItem);
      default:
        return Container(
          color: AppTheme.surfaceLight,
          child: const Center(
            child: Icon(
              Icons.broken_image,
              size: 50,
              color: AppTheme.textMuted,
            ),
          ),
        );
    }
  }

  Widget _buildImageViewer(MediaItem mediaItem) {
    return Stack(
      fit: StackFit.expand,
      children: [
        Image.network(
          mediaItem.url,
          fit: BoxFit.cover,
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return Container(
              color: AppTheme.surfaceLight,
              child: Center(
                child: CircularProgressIndicator(
                  value: loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                          loadingProgress.expectedTotalBytes!
                      : null,
                  valueColor:
                      const AlwaysStoppedAnimation<Color>(AppTheme.primaryGold),
                ),
              ),
            );
          },
          errorBuilder: (context, error, stackTrace) {
            return Container(
              color: AppTheme.surfaceLight,
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.image_not_supported,
                      size: 50,
                      color: AppTheme.textMuted,
                    ),
                    SizedBox(height: 8),
                    Text(
                      'فشل في تحميل الصورة',
                      style: TextStyle(color: AppTheme.textMuted),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
        // زر التكبير/العرض بحجم كامل
        Positioned(
          top: 16,
          right: 16,
          child: PremiumIconButton(
            icon: Icons.fullscreen,
            onPressed: () => _showFullScreenImage(mediaItem),
            hasGradient: false,
            backgroundColor: Colors.black.withValues(alpha: 0.5),
            iconColor: Colors.white,
            size: 40,
          ),
        ),
      ],
    );
  }

  Widget _buildVideoPlayer(MediaItem mediaItem) {
    if (!_videoControllers.containsKey(mediaItem.url)) {
      _initializeVideoController(mediaItem.url);
      return Container(
        color: Colors.black,
        child: const Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryGold),
          ),
        ),
      );
    }

    final controller = _videoControllers[mediaItem.url]!;
    return Stack(
      alignment: Alignment.center,
      children: [
        AspectRatio(
          aspectRatio: controller.value.aspectRatio,
          child: VideoPlayer(controller),
        ),

        // أزرار التحكم
        _buildVideoControls(controller),

        // زر ملء الشاشة
        Positioned(
          top: 16,
          right: 16,
          child: PremiumIconButton(
            icon: Icons.fullscreen,
            onPressed: () => _showFullScreenVideo(mediaItem, controller),
            hasGradient: false,
            backgroundColor: Colors.black.withValues(alpha: 0.5),
            iconColor: Colors.white,
            size: 40,
          ),
        ),
      ],
    );
  }

  Widget _buildVideoControls(VideoPlayerController controller) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.transparent,
            Colors.black.withValues(alpha: 0.7),
          ],
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // زر التشغيل/الإيقاف
          GestureDetector(
            onTap: () {
              setState(() {
                controller.value.isPlaying
                    ? controller.pause()
                    : controller.play();
              });
            },
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                shape: BoxShape.circle,
              ),
              child: Icon(
                controller.value.isPlaying ? Icons.pause : Icons.play_arrow,
                color: Colors.white,
                size: 40,
              ),
            ),
          ),

          const SizedBox(height: 20),

          // شريط التقدم
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: VideoProgressIndicator(
              controller,
              allowScrubbing: true,
              colors: const VideoProgressColors(
                playedColor: AppTheme.primaryGold,
                bufferedColor: Colors.grey,
                backgroundColor: Colors.white30,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIndicators() {
    return Positioned(
      bottom: 20,
      left: 0,
      right: 0,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(
          widget.mediaItems.length,
          (index) => Container(
            margin: const EdgeInsets.symmetric(horizontal: 4),
            width: _currentIndex == index ? 12 : 8,
            height: 8,
            decoration: BoxDecoration(
              color: _currentIndex == index
                  ? AppTheme.primaryGold
                  : Colors.white.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Positioned(
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // السابق
          Padding(
            padding: const EdgeInsets.only(left: 16),
            child: PremiumIconButton(
              icon: Icons.arrow_back_ios,
              onPressed: _currentIndex > 0
                  ? () {
                      _pageController.previousPage(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    }
                  : null,
              hasGradient: false,
              backgroundColor: Colors.black.withValues(alpha: 0.5),
              iconColor: Colors.white,
            ),
          ),

          // التالي
          Padding(
            padding: const EdgeInsets.only(right: 16),
            child: PremiumIconButton(
              icon: Icons.arrow_forward_ios,
              onPressed: _currentIndex < widget.mediaItems.length - 1
                  ? () {
                      _pageController.nextPage(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    }
                  : null,
              hasGradient: false,
              backgroundColor: Colors.black.withValues(alpha: 0.5),
              iconColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMediaInfo(MediaItem mediaItem) {
    if (mediaItem.title == null && mediaItem.description == null) {
      return const SizedBox.shrink();
    }

    return Positioned(
      bottom: 60,
      left: 16,
      right: 16,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.8),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            if (mediaItem.title != null)
              Text(
                mediaItem.title!,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            if (mediaItem.description != null) ...[
              const SizedBox(height: 4),
              Text(
                mediaItem.description!,
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showFullScreenImage(MediaItem mediaItem) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FullScreenImageViewer(mediaItem: mediaItem),
      ),
    );
  }

  void _showFullScreenVideo(
      MediaItem mediaItem, VideoPlayerController controller) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FullScreenVideoViewer(
          mediaItem: mediaItem,
          controller: controller,
        ),
      ),
    );
  }
}

// نماذج للميديا
enum MediaType { image, video }

class MediaItem {
  final String url;
  final MediaType type;
  final String? title;
  final String? description;
  final DateTime? createdAt;

  MediaItem({
    required this.url,
    required this.type,
    this.title,
    this.description,
    this.createdAt,
  });
}

// شاشة عرض الصورة بحجم كامل
class FullScreenImageViewer extends StatelessWidget {
  final MediaItem mediaItem;

  const FullScreenImageViewer({super.key, required this.mediaItem});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: mediaItem.title != null
            ? Text(
                mediaItem.title!,
                style: const TextStyle(color: Colors.white),
              )
            : null,
      ),
      body: Center(
        child: InteractiveViewer(
          child: Image.network(
            mediaItem.url,
            fit: BoxFit.contain,
          ),
        ),
      ),
    );
  }
}

// شاشة عرض الفيديو بحجم كامل
class FullScreenVideoViewer extends StatelessWidget {
  final MediaItem mediaItem;
  final VideoPlayerController controller;

  const FullScreenVideoViewer({
    super.key,
    required this.mediaItem,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Center(
        child: AspectRatio(
          aspectRatio: controller.value.aspectRatio,
          child: VideoPlayer(controller),
        ),
      ),
    );
  }
}
