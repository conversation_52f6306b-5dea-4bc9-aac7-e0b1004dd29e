import 'package:flutter/material.dart';
import 'dart:ui';
import '../theme/app_theme.dart';
import 'animated_widgets.dart';

class EnhancedPremiumCard extends StatefulWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? width;
  final double? height;
  final bool hasGradientBorder;
  final bool hasGlow;
  final bool hasFloatingEffect;
  final bool hasRippleEffect;
  final Color? customColor;
  final List<Color>? customGradient;
  final VoidCallback? onTap;
  final double borderRadius;
  final double elevation;

  const EnhancedPremiumCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.hasGradientBorder = false,
    this.hasGlow = false,
    this.hasFloatingEffect = false,
    this.hasRippleEffect = true,
    this.customColor,
    this.customGradient,
    this.onTap,
    this.borderRadius = 20,
    this.elevation = 8,
  });

  @override
  State<EnhancedPremiumCard> createState() => _EnhancedPremiumCardState();
}

class _EnhancedPremiumCardState extends State<EnhancedPremiumCard>
    with TickerProviderStateMixin {
  late AnimationController _hoverController;
  late AnimationController _rippleController;
  late AnimationController _glowController;
  late Animation<double> _elevationAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();

    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _rippleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _glowController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _elevationAnimation = Tween<double>(
      begin: widget.elevation,
      end: widget.elevation + 6,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: Curves.easeInOut,
    ));

    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _glowController,
      curve: Curves.easeInOut,
    ));

    if (widget.hasGlow) {
      _glowController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _hoverController.dispose();
    _rippleController.dispose();
    _glowController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (widget.hasRippleEffect) {
      _rippleController.forward();
    }
    _hoverController.forward();
  }

  void _onTapUp(TapUpDetails details) {
    _hoverController.reverse();
    if (widget.hasRippleEffect) {
      _rippleController.reverse();
    }
  }

  void _onTapCancel() {
    _hoverController.reverse();
    if (widget.hasRippleEffect) {
      _rippleController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget card = Container(
      width: widget.width,
      height: widget.height,
      margin: widget.margin,
      child: Stack(
        children: [
          // Main card
          AnimatedBuilder(
            animation: Listenable.merge([_hoverController, _glowController]),
            builder: (context, child) {
              return Transform.scale(
                scale: _scaleAnimation.value,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: widget.customGradient != null
                        ? LinearGradient(
                            colors: widget.customGradient!,
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          )
                        : LinearGradient(
                            colors: [
                              AppTheme.cardBackground,
                              AppTheme.surfaceLight,
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                    borderRadius: BorderRadius.circular(widget.borderRadius),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: _elevationAnimation.value,
                        offset: Offset(0, _elevationAnimation.value / 2),
                        spreadRadius: 1,
                      ),
                      if (widget.hasGlow)
                        BoxShadow(
                          color: AppTheme.primaryGold.withValues(
                            alpha: 0.2 * _glowAnimation.value,
                          ),
                          blurRadius: 20 + (10 * _glowAnimation.value),
                          spreadRadius: 2 + (3 * _glowAnimation.value),
                        ),
                    ],
                    border: widget.hasGradientBorder
                        ? null
                        : Border.all(
                            color: AppTheme.primaryGold.withValues(alpha: 0.1),
                            width: 1,
                          ),
                  ),
                  child: widget.hasGradientBorder
                      ? Container(
                          decoration: BoxDecoration(
                            borderRadius:
                                BorderRadius.circular(widget.borderRadius),
                            gradient: LinearGradient(
                              colors: [
                                AppTheme.primaryGold.withValues(alpha: 0.3),
                                AppTheme.accentGold.withValues(alpha: 0.1),
                              ],
                            ),
                          ),
                          padding: const EdgeInsets.all(2),
                          child: Container(
                            decoration: BoxDecoration(
                              color: AppTheme.cardBackground,
                              borderRadius: BorderRadius.circular(
                                  widget.borderRadius - 2),
                            ),
                            padding: widget.padding ?? const EdgeInsets.all(20),
                            child: widget.child,
                          ),
                        )
                      : Container(
                          padding: widget.padding ?? const EdgeInsets.all(20),
                          child: widget.child,
                        ),
                ),
              );
            },
          ),
          // Ripple effect
          if (widget.hasRippleEffect && widget.onTap != null)
            Positioned.fill(
              child: AnimatedBuilder(
                animation: _rippleController,
                builder: (context, child) {
                  return CustomPaint(
                    painter: RipplePainter(
                      animationValue: _rippleController.value,
                      color: AppTheme.primaryGold.withValues(alpha: 0.1),
                    ),
                  );
                },
              ),
            ),
        ],
      ),
    );

    if (widget.hasFloatingEffect) {
      card = FloatingWidget(child: card);
    }

    if (widget.onTap != null) {
      return GestureDetector(
        onTap: widget.onTap,
        onTapDown: _onTapDown,
        onTapUp: _onTapUp,
        onTapCancel: _onTapCancel,
        child: card,
      );
    }

    return card;
  }
}

class RipplePainter extends CustomPainter {
  final double animationValue;
  final Color color;

  RipplePainter({
    required this.animationValue,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (animationValue == 0) return;

    final paint = Paint()
      ..color = color.withValues(alpha: color.alpha * (1 - animationValue))
      ..style = PaintingStyle.fill;

    final radius =
        (size.width.compareTo(size.height) > 0 ? size.width : size.height) *
            animationValue;

    canvas.drawCircle(
      Offset(size.width / 2, size.height / 2),
      radius,
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// Glass Card with frosted effect
class GlassCard extends StatelessWidget {
  final Widget child;
  final double blur;
  final double opacity;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double borderRadius;
  final VoidCallback? onTap;

  const GlassCard({
    super.key,
    required this.child,
    this.blur = 10,
    this.opacity = 0.1,
    this.padding,
    this.margin,
    this.borderRadius = 20,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: blur, sigmaY: blur),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: opacity),
              borderRadius: BorderRadius.circular(borderRadius),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            padding: padding ?? const EdgeInsets.all(20),
            child: child,
          ),
        ),
      ),
    );
  }
}

// Neumorphism Card
class NeumorphismCard extends StatefulWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double borderRadius;
  final bool isPressed;
  final VoidCallback? onTap;

  const NeumorphismCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.borderRadius = 20,
    this.isPressed = false,
    this.onTap,
  });

  @override
  State<NeumorphismCard> createState() => _NeumorphismCardState();
}

class _NeumorphismCardState extends State<NeumorphismCard> {
  bool _isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => _isPressed = true),
      onTapUp: (_) => setState(() => _isPressed = false),
      onTapCancel: () => setState(() => _isPressed = false),
      onTap: widget.onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 150),
        margin: widget.margin,
        padding: widget.padding ?? const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: AppTheme.cardBackground,
          borderRadius: BorderRadius.circular(widget.borderRadius),
          boxShadow: _isPressed
              ? [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 5,
                    offset: const Offset(2, 2),
                  ),
                  BoxShadow(
                    color: Colors.white.withValues(alpha: 0.05),
                    blurRadius: 5,
                    offset: const Offset(-2, -2),
                  ),
                ]
              : [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(5, 5),
                  ),
                  BoxShadow(
                    color: Colors.white.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(-5, -5),
                  ),
                ],
        ),
        child: widget.child,
      ),
    );
  }
}
