-- Database functions for business logic

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to calculate trainer rating
CREATE OR REPLACE FUNCTION calculate_trainer_rating(trainer_uuid UUID)
RETURNS VOID AS $$
DECLARE
    avg_rating DECIMAL(3,2);
    review_count INTEGER;
BEGIN
    SELECT AVG(rating), COUNT(*)
    INTO avg_rating, review_count
    FROM public.reviews
    WHERE trainer_id = trainer_uuid;
    
    UPDATE public.trainers
    SET rating = COALESCE(avg_rating, 0.00),
        total_reviews = review_count
    WHERE id = trainer_uuid;
END;
$$ LANGUAGE plpgsql;

-- Function to create notification
CREATE OR REPLACE FUNCTION create_notification(
    user_uuid UUID,
    notification_title TEXT,
    notification_message TEXT,
    notification_type notification_type DEFAULT 'general',
    notification_data JSONB DEFAULT '{}'
)
<PERSON><PERSON>URNS UUID AS $$
DECLARE
    notification_id UUID;
BEGIN
    INSERT INTO public.notifications (user_id, title, message, type, data)
    VALUES (user_uuid, notification_title, notification_message, notification_type, notification_data)
    RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$ LANGUAGE plpgsql;

-- Function to check subscription status
CREATE OR REPLACE FUNCTION check_subscription_status(trainee_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    has_active_subscription BOOLEAN;
BEGIN
    SELECT EXISTS(
        SELECT 1 FROM public.subscriptions
        WHERE trainee_id = trainee_uuid
        AND status = 'active'
        AND end_date >= CURRENT_DATE
    ) INTO has_active_subscription;
    
    RETURN has_active_subscription;
END;
$$ LANGUAGE plpgsql;

-- Function to get trainer availability
CREATE OR REPLACE FUNCTION get_trainer_availability(
    trainer_uuid UUID,
    check_date DATE
)
RETURNS JSONB AS $$
DECLARE
    availability JSONB;
    day_name TEXT;
BEGIN
    day_name := LOWER(TO_CHAR(check_date, 'Day'));
    day_name := TRIM(day_name);
    
    SELECT t.availability->day_name
    INTO availability
    FROM public.trainers t
    WHERE t.id = trainer_uuid;
    
    RETURN COALESCE(availability, '[]'::jsonb);
END;
$$ LANGUAGE plpgsql;

-- Function to calculate BMI
CREATE OR REPLACE FUNCTION calculate_bmi(weight_kg DECIMAL, height_cm DECIMAL)
RETURNS DECIMAL(4,2) AS $$
BEGIN
    RETURN ROUND((weight_kg / POWER(height_cm / 100, 2))::DECIMAL, 2);
END;
$$ LANGUAGE plpgsql;
