import 'package:flutter/material.dart';
import '../../main.dart';
import '../../theme/app_theme.dart';
import '../../widgets/premium_button.dart';
import '../../widgets/premium_card.dart';
import '../../widgets/animated_widgets.dart';
import '../../widgets/subscription_card_widget.dart';
import '../../models/subscription_plan_model.dart';
import 'subscription_plans_screen.dart';
import 'subscription_details_screen.dart';

class SubscriptionsScreen extends StatefulWidget {
  const SubscriptionsScreen({super.key});

  @override
  State<SubscriptionsScreen> createState() => _SubscriptionsScreenState();
}

class _SubscriptionsScreenState extends State<SubscriptionsScreen>
    with SingleTickerProviderStateMixin {
  late Future<Map<String, dynamic>> _dataFuture;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadData() {
    final userId = supabase.auth.currentUser!.id;
    _dataFuture = _fetchData(userId);
  }

  Future<Map<String, dynamic>> _fetchData(String userId) async {
    // جلب الاشتراكات الحالية
    final subscriptionsResponse =
        await supabase.from('subscriptions').select('''
          *,
          trainers(*, users(*)),
          subscription_plans(*)
        ''').eq('trainee_id', userId).order('created_at', ascending: false);

    // جلب خطط الاشتراك المتاحة
    final availablePlansResponse =
        await supabase.from('subscription_plans').select('''
          *,
          trainers(*, users(*))
        ''').eq('is_active', true).order('price', ascending: true);

    return {
      'subscriptions': subscriptionsResponse,
      'availablePlans': availablePlansResponse,
    };
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              _buildTabBar(),
              Expanded(
                child: FutureBuilder<Map<String, dynamic>>(
                  future: _dataFuture,
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return _buildLoadingState();
                    }
                    if (snapshot.hasError) {
                      return _buildErrorState();
                    }

                    final data = snapshot.data!;
                    final subscriptions = data['subscriptions'] as List;
                    final availablePlans = data['availablePlans'] as List;

                    return TabBarView(
                      controller: _tabController,
                      children: [
                        _buildSubscriptionsList(subscriptions),
                        _buildAvailablePlansList(availablePlans),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return SlideInAnimation(
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            PremiumIconButton(
              icon: Icons.arrow_back_ios,
              onPressed: () => Navigator.pop(context),
              hasGradient: false,
              backgroundColor: AppTheme.surfaceLight,
              iconColor: AppTheme.primaryGold,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'الاشتراكات',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimary,
                        ),
                  ),
                  Text(
                    'إدارة اشتراكاتك والخطط المتاحة',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textSecondary,
                        ),
                  ),
                ],
              ),
            ),
            PremiumIconButton(
              icon: Icons.add_shopping_cart,
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SubscriptionPlansScreen(),
                  ),
                );
              },
              hasGradient: true,
              iconColor: Colors.black,
              size: 50,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return SlideInAnimation(
      delay: 200,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20),
        decoration: BoxDecoration(
          color: AppTheme.surfaceLight,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: AppTheme.primaryGold.withValues(alpha: 0.1),
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: TabBar(
          controller: _tabController,
          labelColor: Colors.black,
          unselectedLabelColor: AppTheme.textSecondary,
          indicator: BoxDecoration(
            gradient: AppTheme.primaryGradient,
            borderRadius: BorderRadius.circular(12),
          ),
          indicatorPadding: const EdgeInsets.all(4),
          labelStyle: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
          unselectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
          tabs: const [
            Tab(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.check_circle, size: 18),
                  SizedBox(width: 8),
                  Text('اشتراكاتي'),
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.card_membership, size: 18),
                  SizedBox(width: 8),
                  Text('الخطط المتاحة'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: 3,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: ShimmerLoading(
            child: Container(
              height: 120,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildErrorState() {
    return ScaleInAnimation(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                color: AppTheme.coralRed.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.error_outline,
                size: 50,
                color: AppTheme.coralRed,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'حدث خطأ في تحميل البيانات',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'يرجى المحاولة مرة أخرى',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondary,
                  ),
            ),
            const SizedBox(height: 24),
            PremiumButton(
              text: 'إعادة المحاولة',
              onPressed: () => _loadData(),
              icon: Icons.refresh,
              width: 160,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubscriptionsList(List subscriptions) {
    if (subscriptions.isEmpty) {
      return _buildEmptySubscriptions();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: subscriptions.length,
      itemBuilder: (context, index) {
        final subscription = subscriptions[index];
        return SlideInAnimation(
          delay: index * 100,
          child: _buildEnhancedSubscriptionCard(subscription),
        );
      },
    );
  }

  Widget _buildEmptySubscriptions() {
    return ScaleInAnimation(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                color: AppTheme.textMuted.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.subscriptions_outlined,
                size: 50,
                color: AppTheme.textMuted,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'لا يوجد اشتراكات حالياً',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'اشترك في إحدى الخطط المتاحة',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondary,
                  ),
            ),
            const SizedBox(height: 20),
            PremiumButton(
              text: 'تصفح الخطط المتاحة',
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SubscriptionPlansScreen(),
                  ),
                );
              },
              icon: Icons.explore,
              width: 200,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAvailablePlansList(List plans) {
    if (plans.isEmpty) {
      return _buildEmptyPlans();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: plans.length,
      itemBuilder: (context, index) {
        final plan = plans[index];
        return SlideInAnimation(
          delay: index * 100,
          child: _buildPlanCard(plan),
        );
      },
    );
  }

  Widget _buildEmptyPlans() {
    return ScaleInAnimation(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                color: AppTheme.textMuted.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.card_membership,
                size: 50,
                color: AppTheme.textMuted,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'لا توجد خطط متاحة حالياً',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'سيتم إضافة خطط جديدة قريباً',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondary,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlanCard(Map<String, dynamic> plan) {
    final trainer = plan['trainers'];
    final user = trainer['users'];
    final planName = plan['name'];
    final features = _parseStringList(plan['features']);

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: PremiumCard(
        hasGradient: true,
        gradientColors: [
          SubscriptionPlan.getPlanColor(planName).withValues(alpha: 0.1),
          AppTheme.primaryGold.withValues(alpha: 0.05),
        ],
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: SubscriptionPlan.getPlanColor(planName),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: SubscriptionPlan.getPlanColor(planName)
                            .withValues(alpha: 0.3),
                        blurRadius: 12,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: Icon(
                    SubscriptionPlan.getPlanIcon(planName),
                    color: Colors.white,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'خطة $planName',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                      Text(
                        'مع ${user['full_name']}',
                        style: const TextStyle(
                          color: AppTheme.textSecondary,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '${plan['price']} ريال',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryGold,
                      ),
                    ),
                    Text(
                      '${plan['duration_days']} يوم',
                      style: const TextStyle(
                        color: AppTheme.textSecondary,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            if (plan['description'] != null) ...[
              const SizedBox(height: 12),
              Text(
                plan['description'],
                style: const TextStyle(
                  color: AppTheme.textSecondary,
                  fontSize: 14,
                  height: 1.4,
                ),
              ),
            ],
            if (features.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(
                'مميزات الخطة:',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimary,
                ),
              ),
              const SizedBox(height: 8),
              ...features.map((feature) => Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.check_circle,
                          size: 16,
                          color: AppTheme.emeraldGreen,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            feature,
                            style: const TextStyle(
                              color: AppTheme.textSecondary,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                  )),
            ],
            const SizedBox(height: 16),
            PremiumButton(
              text: 'اشترك الآن',
              onPressed: () {
                // تنفيذ عملية الاشتراك
                _subscribeToPlan(plan);
              },
              hasGradient: true,
              height: 45,
              icon: Icons.payment,
              width: double.infinity,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedSubscriptionCard(Map<String, dynamic> subscription) {
    final trainer = subscription['trainers'];
    final user = trainer?['users'];
    final trainerName = user?['full_name'] ?? 'مدرب غير محدد';
    final status = subscription['status'] ?? 'غير محدد';
    final planType = subscription['plan_type'] ?? 'غير محدد';
    final price = subscription['price']?.toDouble() ?? 0.0;
    final startDate = subscription['start_date'];
    final endDate = subscription['end_date'];

    // تحديد لون الحالة
    Color statusColor;
    IconData statusIcon;
    switch (status.toLowerCase()) {
      case 'active':
        statusColor = AppTheme.emeraldGreen;
        statusIcon = Icons.check_circle;
        break;
      case 'pending':
        statusColor = AppTheme.primaryGold;
        statusIcon = Icons.pending;
        break;
      case 'expired':
        statusColor = AppTheme.coralRed;
        statusIcon = Icons.cancel;
        break;
      default:
        statusColor = AppTheme.textSecondary;
        statusIcon = Icons.help;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: PremiumCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with trainer info and status
            Row(
              children: [
                CircleAvatar(
                  radius: 25,
                  backgroundColor: AppTheme.primaryGold.withValues(alpha: 0.2),
                  backgroundImage: user?['avatar_url'] != null
                      ? NetworkImage(user['avatar_url'])
                      : null,
                  child: user?['avatar_url'] == null
                      ? const Icon(Icons.person, color: AppTheme.primaryGold)
                      : null,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        trainerName,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(statusIcon, size: 16, color: statusColor),
                          const SizedBox(width: 4),
                          Text(
                            _getStatusText(status),
                            style: TextStyle(
                              fontSize: 14,
                              color: statusColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert,
                      color: AppTheme.textSecondary),
                  onSelected: (value) =>
                      _handleSubscriptionAction(value, subscription),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'details',
                      child: Row(
                        children: [
                          Icon(Icons.info, color: AppTheme.primaryGold),
                          SizedBox(width: 8),
                          Text('التفاصيل'),
                        ],
                      ),
                    ),
                    if (status.toLowerCase() == 'active') ...[
                      const PopupMenuItem(
                        value: 'renew',
                        child: Row(
                          children: [
                            Icon(Icons.refresh, color: AppTheme.emeraldGreen),
                            SizedBox(width: 8),
                            Text('تجديد'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'cancel',
                        child: Row(
                          children: [
                            Icon(Icons.cancel, color: AppTheme.coralRed),
                            SizedBox(width: 8),
                            Text('إلغاء'),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Plan details
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppTheme.primaryGold.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'نوع الخطة',
                          style: TextStyle(
                            fontSize: 12,
                            color: AppTheme.textSecondary,
                          ),
                        ),
                        Text(
                          _getPlanTypeText(planType),
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.textPrimary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'السعر',
                          style: TextStyle(
                            fontSize: 12,
                            color: AppTheme.textSecondary,
                          ),
                        ),
                        Text(
                          '${price.toStringAsFixed(0)} ريال',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.primaryGold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            if (startDate != null && endDate != null) ...[
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'تاريخ البداية',
                          style: TextStyle(
                            fontSize: 12,
                            color: AppTheme.textSecondary,
                          ),
                        ),
                        Text(
                          _formatDate(startDate),
                          style: const TextStyle(
                            fontSize: 13,
                            color: AppTheme.textPrimary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'تاريخ الانتهاء',
                          style: TextStyle(
                            fontSize: 12,
                            color: AppTheme.textSecondary,
                          ),
                        ),
                        Text(
                          _formatDate(endDate),
                          style: const TextStyle(
                            fontSize: 13,
                            color: AppTheme.textPrimary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _getStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return 'نشط';
      case 'pending':
        return 'في الانتظار';
      case 'expired':
        return 'منتهي';
      case 'cancelled':
        return 'ملغي';
      default:
        return 'غير محدد';
    }
  }

  String _getPlanTypeText(String planType) {
    switch (planType.toLowerCase()) {
      case 'monthly':
        return 'شهري';
      case 'weekly':
        return 'أسبوعي';
      case 'session':
        return 'جلسة واحدة';
      case 'quarterly':
        return 'ربع سنوي';
      default:
        return planType;
    }
  }

  String _formatDate(String dateStr) {
    try {
      final date = DateTime.parse(dateStr);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateStr;
    }
  }

  List<String> _parseStringList(dynamic data) {
    if (data == null) return [];

    if (data is List) {
      return data.map((e) => e.toString()).toList();
    }

    if (data is String) {
      if (data.isEmpty) return [];
      try {
        return data
            .split(',')
            .map((e) => e.trim())
            .where((e) => e.isNotEmpty)
            .toList();
      } catch (e) {
        return [data];
      }
    }

    return [];
  }

  void _handleSubscriptionAction(
      String action, Map<String, dynamic> subscription) {
    switch (action) {
      case 'details':
        _showSubscriptionDetails(subscription);
        break;
      case 'renew':
        _renewSubscription(subscription);
        break;
      case 'cancel':
        _cancelSubscription(subscription);
        break;
    }
  }

  void _showSubscriptionDetails(Map<String, dynamic> subscription) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SubscriptionDetailsScreen(
          subscription: subscription,
        ),
      ),
    );

    // إذا تم تحديث الاشتراك، قم بإعادة تحميل البيانات
    if (result == true) {
      _refreshData();
    }
  }

  void _renewSubscription(Map<String, dynamic> subscription) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تجديد الاشتراك'),
        content: const Text('هل تريد تجديد هذا الاشتراك؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _performRenewal(subscription);
            },
            child: const Text('تجديد'),
          ),
        ],
      ),
    );
  }

  void _cancelSubscription(Map<String, dynamic> subscription) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إلغاء الاشتراك'),
        content: const Text(
            'هل أنت متأكد من إلغاء هذا الاشتراك؟ لا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('تراجع'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(backgroundColor: AppTheme.coralRed),
            onPressed: () {
              Navigator.pop(context);
              _performCancellation(subscription);
            },
            child: const Text('إلغاء الاشتراك'),
          ),
        ],
      ),
    );
  }

  Future<void> _performRenewal(Map<String, dynamic> subscription) async {
    try {
      // تحديث تاريخ الانتهاء
      final newEndDate = DateTime.now().add(const Duration(days: 30));

      await supabase.from('subscriptions').update({
        'end_date': newEndDate.toIso8601String().split('T')[0],
        'status': 'active',
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('id', subscription['id']);

      // إعادة تحميل البيانات
      _refreshData();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم تجديد الاشتراك بنجاح'),
          backgroundColor: AppTheme.emeraldGreen,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('حدث خطأ أثناء تجديد الاشتراك'),
          backgroundColor: AppTheme.coralRed,
        ),
      );
    }
  }

  Future<void> _performCancellation(Map<String, dynamic> subscription) async {
    try {
      await supabase.from('subscriptions').update({
        'status': 'cancelled',
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('id', subscription['id']);

      // إعادة تحميل البيانات
      _refreshData();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إلغاء الاشتراك بنجاح'),
          backgroundColor: AppTheme.emeraldGreen,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('حدث خطأ أثناء إلغاء الاشتراك'),
          backgroundColor: AppTheme.coralRed,
        ),
      );
    }
  }

  void _refreshData() {
    final user = supabase.auth.currentUser;
    if (user != null) {
      setState(() {
        _dataFuture = _fetchData(user.id);
      });
    }
  }

  void _subscribeToPlan(Map<String, dynamic> plan) {
    // هنا يمكن تنفيذ عملية الاشتراك
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('جاري تحضير عملية الاشتراك في خطة ${plan['name']}'),
        backgroundColor: AppTheme.emeraldGreen,
      ),
    );
  }
}
