import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';
import '../../widgets/premium_button.dart';
import '../../widgets/premium_card.dart';
import '../../widgets/animated_widgets.dart';
import '../../models/subscription_plan_model.dart';

class SubscriptionPlanDetailsScreen extends StatefulWidget {
  final Map<String, dynamic> plan;

  const SubscriptionPlanDetailsScreen({
    super.key,
    required this.plan,
  });

  @override
  State<SubscriptionPlanDetailsScreen> createState() =>
      _SubscriptionPlanDetailsScreenState();
}

class _SubscriptionPlanDetailsScreenState
    extends State<SubscriptionPlanDetailsScreen> {
  bool _isSubscribing = false;

  @override
  Widget build(BuildContext context) {
    final trainer = widget.plan['trainers'];
    final user = trainer?['users'];
    final planName = _getSafeString(widget.plan, 'name', 'خطة غير محددة');
    final features = _parseStringList(widget.plan['features']);

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: CustomScrollView(
          slivers: [
            _buildSliverAppBar(planName),
            SliverToBoxAdapter(
              child: Column(
                children: [
                  _buildPlanHeader(planName, user),
                  _buildPlanInfo(),
                  _buildTrainerInfo(user, trainer),
                  if (features.isNotEmpty) _buildFeaturesSection(features),
                  if (_getSafeString(widget.plan, 'description', '').isNotEmpty)
                    _buildDescriptionSection(),
                  _buildPricingInfo(),
                  _buildSubscribeButton(),
                  const SizedBox(height: 50),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSliverAppBar(String planName) {
    return SliverAppBar(
      expandedHeight: 200,
      floating: false,
      pinned: true,
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: PremiumIconButton(
        icon: Icons.arrow_back_ios,
        onPressed: () => Navigator.pop(context),
        hasGradient: false,
        backgroundColor: Colors.black.withValues(alpha: 0.3),
        iconColor: Colors.white,
      ),
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                SubscriptionPlan.getPlanColor(planName),
                SubscriptionPlan.getPlanColor(planName).withValues(alpha: 0.7),
              ],
            ),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: 40),
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    SubscriptionPlan.getPlanIcon(planName),
                    color: Colors.white,
                    size: 40,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'خطة $planName من BALANCE',
                  style: const TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPlanHeader(String planName, Map<String, dynamic> user) {
    return SlideInAnimation(
      child: Container(
        margin: const EdgeInsets.all(20),
        child: PremiumCard(
          hasGradient: true,
          gradientColors: [
            SubscriptionPlan.getPlanColor(planName).withValues(alpha: 0.1),
            AppTheme.primaryGold.withValues(alpha: 0.05),
          ],
          child: Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.primaryGold.withValues(alpha: 0.2),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(15),
                  child: user['avatar_url'] != null
                      ? Image.network(
                          user['avatar_url'],
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return _buildDefaultAvatar(user['full_name']);
                          },
                        )
                      : _buildDefaultAvatar(user['full_name']),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'مدربك الشخصي',
                      style: const TextStyle(
                        color: AppTheme.textSecondary,
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      user['full_name'] ?? 'مدرب غير معروف',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'محترف',
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDefaultAvatar(String? name) {
    return Container(
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: BorderRadius.circular(15),
      ),
      child: Center(
        child: Text(
          name != null && name.isNotEmpty ? name[0].toUpperCase() : '؟',
          style: const TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
      ),
    );
  }

  Widget _buildPlanInfo() {
    return SlideInAnimation(
      delay: 200,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        child: Row(
          children: [
            Expanded(
              child: _buildInfoCard(
                'المدة',
                '${_getSafeInt(widget.plan, 'duration_days', 0)} يوم',
                Icons.access_time,
                AppTheme.lightBlue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildInfoCard(
                'نوع الخطة',
                _getSafeString(widget.plan, 'name', 'خطة غير محددة'),
                Icons.card_membership,
                AppTheme.emeraldGreen,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard(
      String title, String value, IconData icon, Color color) {
    return PremiumCard(
      hasGradient: true,
      gradientColors: [
        color.withValues(alpha: 0.1),
        color.withValues(alpha: 0.05),
      ],
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 32,
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrainerInfo(
      Map<String, dynamic> user, Map<String, dynamic> trainer) {
    return SlideInAnimation(
      delay: 300,
      child: Container(
        margin: const EdgeInsets.all(20),
        child: PremiumCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(
                    Icons.person,
                    color: AppTheme.primaryGold,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'معلومات المدرب',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              if (trainer['bio'] != null) ...[
                Text(
                  trainer['bio'],
                  style: const TextStyle(
                    color: AppTheme.textSecondary,
                    fontSize: 14,
                    height: 1.5,
                  ),
                ),
                const SizedBox(height: 12),
              ],
              Row(
                children: [
                  _buildTrainerStat(
                      'سنوات الخبرة', '${trainer['experience_years'] ?? 5}'),
                  const SizedBox(width: 20),
                  _buildTrainerStat(
                      'التخصص', trainer['specialization'] ?? 'عام'),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTrainerStat(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: AppTheme.textMuted,
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            color: AppTheme.textPrimary,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildFeaturesSection(List<String> features) {
    return SlideInAnimation(
      delay: 400,
      child: Container(
        margin: const EdgeInsets.all(20),
        child: PremiumCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(
                    Icons.star,
                    color: AppTheme.primaryGold,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'مميزات الخطة',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              ...features.map((feature) => Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: Row(
                      children: [
                        Container(
                          width: 20,
                          height: 20,
                          decoration: BoxDecoration(
                            color: AppTheme.emeraldGreen,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: const Icon(
                            Icons.check,
                            size: 14,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            feature,
                            style: const TextStyle(
                              color: AppTheme.textPrimary,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                  )),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDescriptionSection() {
    return SlideInAnimation(
      delay: 500,
      child: Container(
        margin: const EdgeInsets.all(20),
        child: PremiumCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(
                    Icons.description,
                    color: AppTheme.primaryGold,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'وصف الخطة',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                _getSafeString(widget.plan, 'description', 'لا يوجد وصف متاح'),
                style: const TextStyle(
                  color: AppTheme.textSecondary,
                  fontSize: 14,
                  height: 1.6,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPricingInfo() {
    return SlideInAnimation(
      delay: 600,
      child: Container(
        margin: const EdgeInsets.all(20),
        child: PremiumCard(
          hasGradient: true,
          gradientColors: [
            SubscriptionPlan.getPlanColor(
                    _getSafeString(widget.plan, 'name', 'basic'))
                .withValues(alpha: 0.1),
            AppTheme.primaryGold.withValues(alpha: 0.05),
          ],
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.attach_money,
                    color: AppTheme.primaryGold,
                    size: 32,
                  ),
                  Text(
                    '${_getSafeDouble(widget.plan, 'price', 0.0)}',
                    style: TextStyle(
                      fontSize: 36,
                      fontWeight: FontWeight.bold,
                      color: SubscriptionPlan.getPlanColor(
                          _getSafeString(widget.plan, 'name', 'basic')),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'ريال سعودي',
                        style: TextStyle(
                          color: AppTheme.textSecondary,
                          fontSize: 14,
                        ),
                      ),
                      Text(
                        'لـ ${_getSafeInt(widget.plan, 'duration_days', 0)} يوم',
                        style: const TextStyle(
                          color: AppTheme.textMuted,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.primaryGold.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: AppTheme.primaryGold,
                      size: 16,
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'يمكنك إلغاء الاشتراك في أي وقت. سيتم تجديد الاشتراك تلقائياً.',
                        style: TextStyle(
                          color: AppTheme.primaryGold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSubscribeButton() {
    return SlideInAnimation(
      delay: 700,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20),
        child: PremiumButton(
          text: _isSubscribing ? 'جاري الاشتراك...' : 'اشترك الآن مع BALANCE',
          onPressed: _isSubscribing ? null : _handleSubscribe,
          icon: _isSubscribing ? null : Icons.payment,
          hasGradient: true,
          gradientColors: [
            SubscriptionPlan.getPlanColor(
                _getSafeString(widget.plan, 'name', 'basic')),
            SubscriptionPlan.getPlanColor(
                    _getSafeString(widget.plan, 'name', 'basic'))
                .withValues(alpha: 0.8),
          ],
          height: 55,
          width: double.infinity,
          isLoading: _isSubscribing,
        ),
      ),
    );
  }

  void _handleSubscribe() async {
    setState(() {
      _isSubscribing = true;
    });

    // محاكاة عملية الاشتراك
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _isSubscribing = false;
    });

    if (!mounted) return;

    // عرض رسالة نجاح
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                  'تم الاشتراك في خطة ${_getSafeString(widget.plan, 'name', 'غير محددة')} بنجاح عبر BALANCE!'),
            ),
          ],
        ),
        backgroundColor: AppTheme.emeraldGreen,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );

    // العودة إلى الشاشة السابقة
    Navigator.pop(context, true);
  }

  List<String> _parseStringList(dynamic data) {
    if (data == null) return [];

    if (data is List) {
      return data.map((e) => e.toString()).toList();
    }

    if (data is String) {
      if (data.isEmpty) return [];
      try {
        return data
            .split(',')
            .map((e) => e.trim())
            .where((e) => e.isNotEmpty)
            .toList();
      } catch (e) {
        return [data];
      }
    }

    return [];
  }

  // دوال مساعدة آمنة لتحويل البيانات
  String _getSafeString(dynamic data, String key, String defaultValue) {
    if (data == null) return defaultValue;
    final value = data[key];
    if (value == null) return defaultValue;
    return value.toString();
  }

  double _getSafeDouble(dynamic data, String key, double defaultValue) {
    if (data == null) return defaultValue;
    final value = data[key];
    if (value == null) return defaultValue;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? defaultValue;
    }
    return defaultValue;
  }

  int _getSafeInt(dynamic data, String key, int defaultValue) {
    if (data == null) return defaultValue;
    final value = data[key];
    if (value == null) return defaultValue;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      return int.tryParse(value) ?? defaultValue;
    }
    return defaultValue;
  }
}
