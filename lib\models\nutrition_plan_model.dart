class NutritionPlan {
  final String id;
  final String traineeId;
  final String trainerId;
  final String title;
  final String? description;
  final DateTime startDate;
  final DateTime? endDate;
  final int? dailyCalories;
  final double? dailyProtein;
  final double? dailyCarbs;
  final double? dailyFats;
  final List<dynamic> meals;
  final List<dynamic> supplements;
  final String? instructions;
  final bool isActive;
  final bool completed;
  final DateTime? completedAt;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<NutritionMedia> media; // إضافة الميديا

  NutritionPlan({
    required this.id,
    required this.traineeId,
    required this.trainerId,
    required this.title,
    this.description,
    required this.startDate,
    this.endDate,
    this.dailyCalories,
    this.dailyProtein,
    this.dailyCarbs,
    this.dailyFats,
    this.meals = const [],
    this.supplements = const [],
    this.instructions,
    this.isActive = true,
    this.completed = false,
    this.completedAt,
    required this.createdAt,
    required this.updatedAt,
    this.media = const [],
  });

  factory NutritionPlan.fromJson(Map<String, dynamic> json) {
    List<NutritionMedia> mediaList = [];
    if (json['nutrition_media'] != null) {
      mediaList = List<NutritionMedia>.from(json['nutrition_media']
          .map((media) => NutritionMedia.fromJson(media)));
    }

    return NutritionPlan(
      id: json['id'],
      traineeId: json['trainee_id'],
      trainerId: json['trainer_id'],
      title: json['title'],
      description: json['description'],
      startDate: DateTime.parse(json['start_date']),
      endDate:
          json['end_date'] != null ? DateTime.parse(json['end_date']) : null,
      dailyCalories: json['daily_calories'],
      dailyProtein: json['daily_protein']?.toDouble(),
      dailyCarbs: json['daily_carbs']?.toDouble(),
      dailyFats: json['daily_fats']?.toDouble(),
      meals: json['meals'] ?? [],
      supplements: json['supplements'] ?? [],
      instructions: json['instructions'],
      isActive: json['is_active'] ?? true,
      completed: json['completed'] ?? false,
      completedAt: json['completed_at'] != null
          ? DateTime.parse(json['completed_at'])
          : null,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      media: mediaList,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'trainee_id': traineeId,
      'trainer_id': trainerId,
      'title': title,
      'description': description,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate?.toIso8601String(),
      'daily_calories': dailyCalories,
      'daily_protein': dailyProtein,
      'daily_carbs': dailyCarbs,
      'daily_fats': dailyFats,
      'meals': meals,
      'supplements': supplements,
      'instructions': instructions,
      'is_active': isActive,
      'completed': completed,
      'completed_at': completedAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class NutritionMedia {
  final String id;
  final String nutritionPlanId;
  final String mediaType; // 'image' أو 'video'
  final String mediaUrl;
  final String? title;
  final String? description;
  final DateTime createdAt;

  NutritionMedia({
    required this.id,
    required this.nutritionPlanId,
    required this.mediaType,
    required this.mediaUrl,
    this.title,
    this.description,
    required this.createdAt,
  });

  factory NutritionMedia.fromJson(Map<String, dynamic> json) {
    return NutritionMedia(
      id: json['id'],
      nutritionPlanId: json['nutrition_plan_id'],
      mediaType: json['media_type'],
      mediaUrl: json['media_url'],
      title: json['title'],
      description: json['description'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nutrition_plan_id': nutritionPlanId,
      'media_type': mediaType,
      'media_url': mediaUrl,
      'title': title,
      'description': description,
      'created_at': createdAt.toIso8601String(),
    };
  }

  bool get isImage => mediaType == 'image';
  bool get isVideo => mediaType == 'video';
}
