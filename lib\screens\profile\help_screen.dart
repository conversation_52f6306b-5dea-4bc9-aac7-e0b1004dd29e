import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';

class HelpScreen extends StatelessWidget {
  const HelpScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المساعدة والدعم'),
        backgroundColor: AppTheme.cardBackground,
        elevation: 0,
        centerTitle: true,
        iconTheme: const IconThemeData(color: AppTheme.primaryGold),
      ),
      body: Container(
        width: double.infinity,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [AppTheme.darkBackground, Color(0xFF1A1A1A)],
          ),
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Column(
                  children: const [
                    Icon(Icons.support_agent,
                        size: 60, color: AppTheme.primaryGold),
                    SizedBox(height: 12),
                    Text('كيف يمكننا مساعدتك؟',
                        style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.primaryGold)),
                    SizedBox(height: 8),
                    Text('نحن هنا لدعمك في أي وقت',
                        style: TextStyle(
                            fontSize: 16, color: AppTheme.textSecondary)),
                  ],
                ),
              ),
              const SizedBox(height: 32),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppTheme.cardBackground,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.08),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: const [
                    Text('الأسئلة الشائعة',
                        style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.primaryGold)),
                    SizedBox(height: 12),
                    ListTile(
                      leading: Icon(Icons.edit, color: AppTheme.primaryGold),
                      title: Text('كيف أعدل بياناتي الشخصية؟',
                          style: TextStyle(color: AppTheme.textPrimary)),
                      subtitle:
                          Text('من خلال زر التعديل في أعلى صفحة الملف الشخصي.'),
                    ),
                    ListTile(
                      leading: Icon(Icons.notifications,
                          color: AppTheme.primaryGold),
                      title: Text('كيف أستقبل الإشعارات؟',
                          style: TextStyle(color: AppTheme.textPrimary)),
                      subtitle:
                          Text('تأكد من تفعيل الإشعارات من إعدادات التطبيق.'),
                    ),
                    ListTile(
                      leading: Icon(Icons.lock, color: AppTheme.primaryGold),
                      title: Text('كيف أغير كلمة المرور؟',
                          style: TextStyle(color: AppTheme.textPrimary)),
                      subtitle: Text('من خلال إعدادات الخصوصية والأمان.'),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 32),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppTheme.cardBackground,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.08),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('تواصل معنا',
                        style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.primaryGold)),
                    const SizedBox(height: 12),
                    Row(
                      children: const [
                        Icon(Icons.email, color: AppTheme.primaryGold),
                        SizedBox(width: 8),
                        Text('<EMAIL>',
                            style: TextStyle(color: AppTheme.textPrimary)),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: const [
                        Icon(Icons.phone, color: AppTheme.primaryGold),
                        SizedBox(width: 8),
                        Text('+966 555 123 456',
                            style: TextStyle(color: AppTheme.textPrimary)),
                      ],
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: null,
                      icon: Icon(Icons.chat, color: Colors.black),
                      label: Text('دردشة مباشرة',
                          style: TextStyle(color: Colors.black)),
                      style: ButtonStyle(
                        backgroundColor:
                            MaterialStatePropertyAll(AppTheme.primaryGold),
                        minimumSize:
                            MaterialStatePropertyAll(Size(double.infinity, 48)),
                        shape: MaterialStatePropertyAll(RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.all(Radius.circular(12)))),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
