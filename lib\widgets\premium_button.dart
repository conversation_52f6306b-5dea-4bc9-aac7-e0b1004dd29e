import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

class PremiumButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isOutlined;
  final Color? backgroundColor;
  final Color? textColor;
  final IconData? icon;
  final double? width;
  final double height;
  final double borderRadius;
  final List<Color>? gradientColors;
  final bool hasGradient;
  final bool isElevated;
  final double fontSize;

  const PremiumButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.isOutlined = false,
    this.backgroundColor,
    this.textColor,
    this.icon,
    this.width,
    this.height = 56,
    this.borderRadius = 16,
    this.gradientColors,
    this.hasGradient = true,
    this.isElevated = true,
    this.fontSize = 16,
  });

  @override
  State<PremiumButton> createState() => _PremiumButtonState();
}

class _PremiumButtonState extends State<PremiumButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _shadowAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppTheme.fastAnimation,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _shadowAnimation = Tween<double>(
      begin: 1.0,
      end: 0.3,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.onPressed != null && !widget.isLoading) {
      _animationController.forward();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    _animationController.reverse();
  }

  void _handleTapCancel() {
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final effectiveGradientColors = widget.gradientColors ??
        [
          AppTheme.primaryGoldLight,
          AppTheme.primaryGold,
          AppTheme.primaryGoldDark,
        ];

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: _handleTapDown,
            onTapUp: _handleTapUp,
            onTapCancel: _handleTapCancel,
            child: AnimatedContainer(
              duration: AppTheme.normalAnimation,
              width: widget.width,
              height: widget.height,
              decoration: BoxDecoration(
                gradient: widget.hasGradient && !widget.isOutlined
                    ? LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: effectiveGradientColors,
                      )
                    : null,
                color: widget.hasGradient || widget.isOutlined
                    ? null
                    : (widget.backgroundColor ?? AppTheme.primaryGold),
                borderRadius: BorderRadius.circular(widget.borderRadius),
                border: widget.isOutlined
                    ? Border.all(
                        color: widget.backgroundColor ?? AppTheme.primaryGold,
                        width: 2,
                      )
                    : null,
                boxShadow: widget.isElevated && !widget.isOutlined
                    ? [
                        BoxShadow(
                          color: (widget.backgroundColor ??
                                  AppTheme.primaryGold)
                              .withValues(alpha: 0.3 * _shadowAnimation.value),
                          blurRadius: 16 * _shadowAnimation.value,
                          offset: Offset(0, 8 * _shadowAnimation.value),
                          spreadRadius: 0,
                        ),
                        BoxShadow(
                          color: Colors.black
                              .withValues(alpha: 0.1 * _shadowAnimation.value),
                          blurRadius: 8 * _shadowAnimation.value,
                          offset: Offset(0, 4 * _shadowAnimation.value),
                          spreadRadius: 0,
                        ),
                      ]
                    : null,
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: widget.isLoading ? null : widget.onPressed,
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (widget.isLoading) ...[
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: widget.isOutlined
                                  ? (widget.textColor ?? AppTheme.primaryGold)
                                  : (widget.textColor ?? Colors.black),
                            ),
                          ),
                          const SizedBox(width: 12),
                        ] else if (widget.icon != null) ...[
                          Icon(
                            widget.icon,
                            color: widget.isOutlined
                                ? (widget.textColor ?? AppTheme.primaryGold)
                                : (widget.textColor ?? Colors.black),
                            size: 20,
                          ),
                          const SizedBox(width: 12),
                        ],
                        Flexible(
                          child: Text(
                            widget.text,
                            style: TextStyle(
                              fontSize: widget.fontSize,
                              fontWeight: FontWeight.w600,
                              color: widget.isOutlined
                                  ? (widget.textColor ?? AppTheme.primaryGold)
                                  : (widget.textColor ?? Colors.black),
                              letterSpacing: 0.5,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class PremiumIconButton extends StatefulWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? iconColor;
  final double size;
  final String? tooltip;
  final bool hasGradient;
  final bool isElevated;

  const PremiumIconButton({
    super.key,
    required this.icon,
    this.onPressed,
    this.backgroundColor,
    this.iconColor,
    this.size = 48,
    this.tooltip,
    this.hasGradient = false,
    this.isElevated = true,
  });

  @override
  State<PremiumIconButton> createState() => _PremiumIconButtonState();
}

class _PremiumIconButtonState extends State<PremiumIconButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppTheme.fastAnimation,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.9,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.onPressed != null) {
      _animationController.forward();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    _animationController.reverse();
  }

  void _handleTapCancel() {
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: _handleTapDown,
            onTapUp: _handleTapUp,
            onTapCancel: _handleTapCancel,
            child: Tooltip(
              message: widget.tooltip ?? '',
              child: Container(
                width: widget.size,
                height: widget.size,
                decoration: BoxDecoration(
                  gradient:
                      widget.hasGradient ? AppTheme.primaryGradient : null,
                  color: widget.hasGradient
                      ? null
                      : (widget.backgroundColor ?? AppTheme.primaryGold),
                  borderRadius: BorderRadius.circular(widget.size * 0.3),
                  boxShadow: widget.isElevated
                      ? [
                          BoxShadow(
                            color:
                                (widget.backgroundColor ?? AppTheme.primaryGold)
                                    .withValues(alpha: 0.3),
                            blurRadius: 12,
                            offset: const Offset(0, 6),
                            spreadRadius: 0,
                          ),
                        ]
                      : null,
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: widget.onPressed,
                    borderRadius: BorderRadius.circular(widget.size * 0.3),
                    child: Icon(
                      widget.icon,
                      color: widget.iconColor ?? Colors.black,
                      size: widget.size * 0.5,
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class PremiumToggleButton extends StatefulWidget {
  final bool isSelected;
  final VoidCallback? onPressed;
  final String text;
  final IconData? icon;
  final Color? selectedColor;
  final Color? unselectedColor;

  const PremiumToggleButton({
    super.key,
    required this.isSelected,
    this.onPressed,
    required this.text,
    this.icon,
    this.selectedColor,
    this.unselectedColor,
  });

  @override
  State<PremiumToggleButton> createState() => _PremiumToggleButtonState();
}

class _PremiumToggleButtonState extends State<PremiumToggleButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppTheme.normalAnimation,
      vsync: this,
    );

    if (widget.isSelected) {
      _animationController.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(PremiumToggleButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isSelected != oldWidget.isSelected) {
      if (widget.isSelected) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        final selectedColor = widget.selectedColor ?? AppTheme.primaryGold;
        final unselectedColor = widget.unselectedColor ?? AppTheme.textMuted;

        return Container(
          decoration: BoxDecoration(
            gradient: widget.isSelected
                ? LinearGradient(
                    colors: [
                      selectedColor.withValues(alpha: 0.15),
                      selectedColor.withValues(alpha: 0.05),
                    ],
                  )
                : null,
            color: widget.isSelected ? null : Colors.transparent,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Color.lerp(
                unselectedColor.withValues(alpha: 0.3),
                selectedColor,
                _animationController.value,
              )!,
              width: widget.isSelected ? 2 : 1,
            ),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: widget.onPressed,
              borderRadius: BorderRadius.circular(12),
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (widget.icon != null) ...[
                      Icon(
                        widget.icon,
                        color: Color.lerp(
                          unselectedColor,
                          selectedColor,
                          _animationController.value,
                        ),
                        size: 18,
                      ),
                      const SizedBox(width: 8),
                    ],
                    Text(
                      widget.text,
                      style: TextStyle(
                        color: Color.lerp(
                          unselectedColor,
                          selectedColor,
                          _animationController.value,
                        ),
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
